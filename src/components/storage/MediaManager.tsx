"use client";

import { useState } from "react";
import { 
  Zap,
  Tag,
  Eye,
  Download,
  Star,
  MoreHorizontal,
  FileImage,
  Video,
  Archive,
  File,
  Users,
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  Trash2,
  RefreshCw,
  Settings,
  Filter,
  Search,
  Grid,
  List,
  SortAsc
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Checkbox } from "@/components/ui/checkbox";
import { formatFileSize, formatDate, timeAgo, cn } from "@/lib/utils";

interface MediaFile {
  id: string;
  name: string;
  type: 'image' | 'video' | 'archive' | 'document';
  size: number;
  clientId: string;
  clientName: string;
  eventName: string;
  uploadDate: string;
  lastAccessed?: string;
  downloads: number;
  isStarred: boolean;
  tags: string[];
  aiTags: string[];
  faceCount?: number;
  thumbnail?: string;
  status: 'active' | 'processing' | 'archived';
  aiProcessingStatus: 'pending' | 'processing' | 'completed' | 'failed';
  resolution?: string;
  duration?: number;
}

interface MediaManagerProps {
  files: MediaFile[];
  onFileSelect: (fileId: string) => void;
  onBulkAction: (action: string, fileIds: string[]) => void;
  onTagFile: (fileId: string, tags: string[]) => void;
  onAIProcess: (fileIds: string[]) => void;
}

export default function MediaManager({
  files,
  onFileSelect,
  onBulkAction,
  onTagFile,
  onAIProcess
}: MediaManagerProps) {
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('uploadDate');
  const [showAIPanel, setShowAIPanel] = useState(false);

  const filteredFiles = files.filter(file => {
    const matchesSearch = file.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         file.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         file.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         file.aiTags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesType = typeFilter === 'all' || file.type === typeFilter;
    const matchesStatus = statusFilter === 'all' || file.status === statusFilter;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image': return FileImage;
      case 'video': return Video;
      case 'archive': return Archive;
      default: return File;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'image': return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20';
      case 'video': return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'archive': return 'text-purple-600 bg-purple-100 dark:bg-purple-900/20';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-700';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'processing': return 'warning';
      case 'archived': return 'secondary';
      default: return 'secondary';
    }
  };

  const getAIStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'processing': return 'warning';
      case 'failed': return 'destructive';
      default: return 'secondary';
    }
  };

  const handleSelectAll = () => {
    if (selectedFiles.length === filteredFiles.length) {
      setSelectedFiles([]);
    } else {
      setSelectedFiles(filteredFiles.map(f => f.id));
    }
  };

  const handleFileSelect = (fileId: string) => {
    setSelectedFiles(prev => 
      prev.includes(fileId) 
        ? prev.filter(id => id !== fileId)
        : [...prev, fileId]
    );
  };

  const aiProcessingStats = {
    pending: files.filter(f => f.aiProcessingStatus === 'pending').length,
    processing: files.filter(f => f.aiProcessingStatus === 'processing').length,
    completed: files.filter(f => f.aiProcessingStatus === 'completed').length,
    failed: files.filter(f => f.aiProcessingStatus === 'failed').length
  };

  return (
    <div className="space-y-6">
      {/* AI Processing Status */}
      <Card className="border-0 shadow-md bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="h-12 w-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center">
                <Zap className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  AI Processing Status
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Automatic tagging and face recognition progress
                </p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="grid grid-cols-4 gap-4 text-center">
                <div>
                  <p className="text-2xl font-bold text-gray-600">{aiProcessingStats.pending}</p>
                  <p className="text-xs text-gray-500">Pending</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-yellow-600">{aiProcessingStats.processing}</p>
                  <p className="text-xs text-gray-500">Processing</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-600">{aiProcessingStats.completed}</p>
                  <p className="text-xs text-gray-500">Completed</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-red-600">{aiProcessingStats.failed}</p>
                  <p className="text-xs text-gray-500">Failed</p>
                </div>
              </div>
              <Button onClick={() => setShowAIPanel(!showAIPanel)}>
                <Settings className="h-4 w-4 mr-2" />
                AI Settings
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Controls */}
      <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <Input
                  placeholder="Search files by name, client, tags, or AI tags..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-11 bg-gray-50 dark:bg-gray-700"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="all">All Types</option>
                <option value="image">Images</option>
                <option value="video">Videos</option>
                <option value="archive">Archives</option>
                <option value="document">Documents</option>
              </select>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="processing">Processing</option>
                <option value="archived">Archived</option>
              </select>
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedFiles.length > 0 && (
            <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Checkbox
                    checked={selectedFiles.length === filteredFiles.length}
                    onCheckedChange={handleSelectAll}
                  />
                  <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                    {selectedFiles.length} files selected
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onAIProcess(selectedFiles)}
                  >
                    <Zap className="h-4 w-4 mr-2" />
                    AI Process
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onBulkAction('download', selectedFiles)}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onBulkAction('archive', selectedFiles)}
                  >
                    <Archive className="h-4 w-4 mr-2" />
                    Archive
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onBulkAction('delete', selectedFiles)}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Files Display */}
      <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
        <CardContent className="p-6">
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {filteredFiles.map(file => {
                const FileIcon = getFileIcon(file.type);
                return (
                  <div key={file.id} className="group relative bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:shadow-lg transition-all duration-200">
                    <div className="absolute top-2 left-2 z-10">
                      <Checkbox
                        checked={selectedFiles.includes(file.id)}
                        onCheckedChange={() => handleFileSelect(file.id)}
                      />
                    </div>
                    <div className="aspect-square bg-gray-200 dark:bg-gray-600 rounded-lg mb-3 flex items-center justify-center overflow-hidden">
                      {file.thumbnail ? (
                        <img src={file.thumbnail} alt={file.name} className="w-full h-full object-cover" />
                      ) : (
                        <FileIcon className="h-12 w-12 text-gray-400" />
                      )}
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium text-gray-900 dark:text-white text-sm truncate">
                          {file.name}
                        </h3>
                        {file.isStarred && (
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                        )}
                      </div>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        {file.clientName}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-500">
                        {formatFileSize(file.size)}
                      </p>
                      
                      {/* Status Badges */}
                      <div className="flex flex-wrap gap-1">
                        <Badge variant={getStatusColor(file.status) as any} className="text-xs">
                          {file.status}
                        </Badge>
                        <Badge variant={getAIStatusColor(file.aiProcessingStatus) as any} className="text-xs">
                          <Zap className="h-2 w-2 mr-1" />
                          AI: {file.aiProcessingStatus}
                        </Badge>
                      </div>

                      {/* Tags */}
                      <div className="flex flex-wrap gap-1">
                        {file.tags.slice(0, 2).map(tag => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      
                      {/* AI Tags */}
                      {file.aiTags.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {file.aiTags.slice(0, 2).map(tag => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              <Zap className="h-2 w-2 mr-1" />
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}

                      {/* File Info */}
                      {file.faceCount && (
                        <p className="text-xs text-gray-500 dark:text-gray-500">
                          <Users className="h-3 w-3 inline mr-1" />
                          {file.faceCount} faces detected
                        </p>
                      )}
                    </div>
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button variant="outline" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="space-y-2">
              {filteredFiles.map(file => {
                const FileIcon = getFileIcon(file.type);
                return (
                  <div key={file.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:shadow-md transition-all duration-200">
                    <div className="flex items-center gap-4">
                      <Checkbox
                        checked={selectedFiles.includes(file.id)}
                        onCheckedChange={() => handleFileSelect(file.id)}
                      />
                      <div className={cn(
                        "h-10 w-10 rounded-lg flex items-center justify-center",
                        getTypeColor(file.type)
                      )}>
                        <FileIcon className="h-5 w-5" />
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium text-gray-900 dark:text-white">
                            {file.name}
                          </h3>
                          {file.isStarred && (
                            <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          )}
                          <Badge variant={getStatusColor(file.status) as any} className="text-xs">
                            {file.status}
                          </Badge>
                          <Badge variant={getAIStatusColor(file.aiProcessingStatus) as any} className="text-xs">
                            <Zap className="h-2 w-2 mr-1" />
                            AI: {file.aiProcessingStatus}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                          <span>{file.clientName}</span>
                          <span>•</span>
                          <span>{formatFileSize(file.size)}</span>
                          <span>•</span>
                          <span>{formatDate(file.uploadDate)}</span>
                          {file.faceCount && (
                            <>
                              <span>•</span>
                              <span>{file.faceCount} faces</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex flex-wrap gap-1">
                        {file.tags.slice(0, 3).map(tag => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {file.aiTags.slice(0, 2).map(tag => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            <Zap className="h-2 w-2 mr-1" />
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      <Button variant="outline" size="sm" onClick={() => onFileSelect(file.id)}>
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </Button>
                      <Button variant="outline" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {/* Empty State */}
          {filteredFiles.length === 0 && (
            <div className="text-center py-12">
              <FileImage className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                No files found
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                Try adjusting your search or filter criteria.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
