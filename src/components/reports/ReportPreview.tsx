"use client";

import { useState } from "react";
import { 
  FileText,
  Download,
  Share2,
  Eye,
  Calendar,
  BarChart3,
  TrendingUp,
  Users,
  DollarSign,
  Activity,
  X,
  Maximize2,
  Minimize2
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { formatDate, cn } from "@/lib/utils";
import { ReportData, ReportSection } from "@/lib/reports";

interface ReportPreviewProps {
  reportData: ReportData;
  isOpen: boolean;
  onClose: () => void;
  onExport: (format: string) => void;
  onShare: () => void;
}

export default function ReportPreview({ 
  reportData, 
  isOpen, 
  onClose, 
  onExport, 
  onShare 
}: ReportPreviewProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);

  if (!isOpen) return null;

  const renderSection = (section: ReportSection, index: number) => {
    switch (section.type) {
      case 'metrics':
        return (
          <Card key={index} className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg">{section.title}</CardTitle>
              {section.description && (
                <CardDescription>{section.description}</CardDescription>
              )}
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {Object.entries(section.data).map(([key, value]) => (
                  <div key={key} className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600 mb-1">
                      {value}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        );

      case 'table':
        return (
          <Card key={index} className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg">{section.title}</CardTitle>
              {section.description && (
                <CardDescription>{section.description}</CardDescription>
              )}
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b border-gray-200 dark:border-gray-600">
                      {section.data.headers?.map((header: string, i: number) => (
                        <th key={i} className="text-left py-3 px-4 font-semibold text-gray-900 dark:text-white">
                          {header}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {section.data.rows?.map((row: any[], i: number) => (
                      <tr key={i} className="border-b border-gray-100 dark:border-gray-700">
                        {row.map((cell, j) => (
                          <td key={j} className="py-3 px-4 text-gray-700 dark:text-gray-300">
                            {cell}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        );

      case 'chart':
        return (
          <Card key={index} className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg">{section.title}</CardTitle>
              {section.description && (
                <CardDescription>{section.description}</CardDescription>
              )}
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600 dark:text-gray-400">
                    {section.data.type?.toUpperCase()} Chart
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-500">
                    {section.data.labels?.length || 0} data points
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        );

      case 'text':
        return (
          <Card key={index} className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg">{section.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose dark:prose-invert max-w-none">
                {typeof section.data === 'string' ? (
                  <p>{section.data}</p>
                ) : (
                  <div dangerouslySetInnerHTML={{ __html: section.data.html || section.data.content }} />
                )}
              </div>
            </CardContent>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <div className={cn(
      "fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4",
      isFullscreen && "p-0"
    )}>
      <div className={cn(
        "bg-white dark:bg-gray-800 rounded-lg shadow-xl max-h-full overflow-hidden flex flex-col",
        isFullscreen ? "w-full h-full rounded-none" : "w-full max-w-6xl h-5/6"
      )}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-600">
          <div className="flex items-center gap-4">
            <div className="h-10 w-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <FileText className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                {reportData.title}
              </h2>
              {reportData.subtitle && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {reportData.subtitle}
                </p>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsFullscreen(!isFullscreen)}
            >
              {isFullscreen ? (
                <Minimize2 className="h-4 w-4" />
              ) : (
                <Maximize2 className="h-4 w-4" />
              )}
            </Button>
            <Button variant="outline" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Report Metadata */}
        <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              <span>Generated: {formatDate(reportData.generatedAt)}</span>
            </div>
            <div className="flex items-center gap-1">
              <Activity className="h-4 w-4" />
              <span>
                Period: {formatDate(reportData.dateRange.from)} - {formatDate(reportData.dateRange.to)}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <BarChart3 className="h-4 w-4" />
              <span>{reportData.sections.length} sections</span>
            </div>
            {reportData.metadata?.customReport && (
              <Badge variant="secondary" className="text-xs">
                Custom Report
              </Badge>
            )}
          </div>
        </div>

        {/* Report Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="space-y-6">
            {reportData.sections.map((section, index) => renderSection(section, index))}
          </div>
        </div>

        {/* Footer Actions */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {reportData.sections.filter(s => s.type === 'table').length} Tables
            </Badge>
            <Badge variant="outline" className="text-xs">
              {reportData.sections.filter(s => s.type === 'chart').length} Charts
            </Badge>
            <Badge variant="outline" className="text-xs">
              {reportData.sections.filter(s => s.type === 'metrics').length} Metric Sections
            </Badge>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" onClick={onShare}>
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
            <div className="flex items-center gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onExport('pdf')}
              >
                PDF
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onExport('excel')}
              >
                Excel
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onExport('csv')}
              >
                CSV
              </Button>
            </div>
            <Button onClick={() => onExport('pdf')}>
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Report Template Selector Component
interface ReportTemplateSelectorProps {
  templates: any[];
  selectedTemplate: string | null;
  onSelect: (templateId: string) => void;
  onGenerate: () => void;
}

export function ReportTemplateSelector({ 
  templates, 
  selectedTemplate, 
  onSelect, 
  onGenerate 
}: ReportTemplateSelectorProps) {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {templates.map((template) => {
          const IconComponent = template.icon;
          return (
            <Card 
              key={template.id} 
              className={cn(
                "cursor-pointer transition-all duration-200 hover:shadow-lg",
                selectedTemplate === template.id 
                  ? "ring-2 ring-blue-500 border-blue-500" 
                  : "border-gray-200 dark:border-gray-600"
              )}
              onClick={() => onSelect(template.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <div className="h-10 w-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <IconComponent className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-gray-900 dark:text-white text-sm">
                      {template.name}
                    </h3>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                      {template.description}
                    </p>
                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant="secondary" className="text-xs">
                        {template.category}
                      </Badge>
                      {template.isPopular && (
                        <Badge variant="success" className="text-xs">
                          Popular
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
      
      {selectedTemplate && (
        <div className="flex justify-center">
          <Button onClick={onGenerate} size="lg">
            <BarChart3 className="h-4 w-4 mr-2" />
            Generate Report
          </Button>
        </div>
      )}
    </div>
  );
}
