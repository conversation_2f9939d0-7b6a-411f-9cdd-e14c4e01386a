"use client";

import { useEffect, useRef, useState } from 'react';
import * as faceapi from 'face-api.js';
import { FaceDetectionResult } from '@/types';

interface FaceRecognitionProps {
  onFaceDetected?: (descriptor: number[]) => void;
  onError?: (error: string) => void;
  isActive?: boolean;
  className?: string;
}

export default function FaceRecognition({
  onFaceDetected,
  onError,
  isActive = true,
  className = "",
}: FaceRecognitionProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [modelsLoaded, setModelsLoaded] = useState(false);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [isDetecting, setIsDetecting] = useState(false);

  // Load face-api.js models
  useEffect(() => {
    const loadModels = async () => {
      try {
        setIsLoading(true);
        
        // Load models from public/models directory
        await Promise.all([
          faceapi.nets.ssdMobilenetv1.loadFromUri('/models'),
          faceapi.nets.faceLandmark68Net.loadFromUri('/models'),
          faceapi.nets.faceRecognitionNet.loadFromUri('/models'),
        ]);
        
        setModelsLoaded(true);
        console.log('Face-api.js models loaded successfully');
      } catch (error) {
        console.error('Error loading face-api.js models:', error);
        onError?.('Failed to load face recognition models');
      } finally {
        setIsLoading(false);
      }
    };

    loadModels();
  }, [onError]);

  // Start camera when models are loaded and component is active
  useEffect(() => {
    if (modelsLoaded && isActive) {
      startCamera();
    } else {
      stopCamera();
    }

    return () => {
      stopCamera();
    };
  }, [modelsLoaded, isActive]);

  const startCamera = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 640 },
          height: { ideal: 480 },
          facingMode: 'user',
        },
      });

      setStream(mediaStream);
      
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        videoRef.current.onloadedmetadata = () => {
          startFaceDetection();
        };
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      onError?.('Unable to access camera. Please ensure camera permissions are granted.');
    }
  };

  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
    setIsDetecting(false);
  };

  const startFaceDetection = async () => {
    if (!videoRef.current || !canvasRef.current || !modelsLoaded || isDetecting) {
      return;
    }

    setIsDetecting(true);
    const video = videoRef.current;
    const canvas = canvasRef.current;

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    const detectFaces = async () => {
      if (!video || !canvas || !isDetecting) return;

      try {
        // Detect faces with landmarks and descriptors
        const detections = await faceapi
          .detectAllFaces(video)
          .withFaceLandmarks()
          .withFaceDescriptors();

        // Clear canvas
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        if (detections.length > 0) {
          // Draw face detection boxes
          const resizedDetections = faceapi.resizeResults(detections, {
            width: canvas.width,
            height: canvas.height,
          });

          if (ctx) {
            // Draw bounding boxes
            resizedDetections.forEach((detection) => {
              const box = detection.detection.box;
              ctx.strokeStyle = '#10b981';
              ctx.lineWidth = 2;
              ctx.strokeRect(box.x, box.y, box.width, box.height);
              
              // Draw confidence score
              ctx.fillStyle = '#10b981';
              ctx.font = '16px Arial';
              ctx.fillText(
                `${Math.round(detection.detection.score * 100)}%`,
                box.x,
                box.y - 5
              );
            });
          }

          // Get the best detection (highest confidence)
          const bestDetection = detections.reduce((best, current) => 
            current.detection.score > best.detection.score ? current : best
          );

          // Call callback with face descriptor
          if (bestDetection.descriptor && onFaceDetected) {
            onFaceDetected(Array.from(bestDetection.descriptor));
          }
        }

        // Continue detection loop
        if (isDetecting) {
          requestAnimationFrame(detectFaces);
        }
      } catch (error) {
        console.error('Error during face detection:', error);
        onError?.('Face detection failed');
      }
    };

    // Start detection loop
    detectFaces();
  };

  const capturePhoto = async (): Promise<string | null> => {
    if (!videoRef.current || !canvasRef.current) return null;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (!ctx) return null;

    // Set canvas dimensions
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw current video frame
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Return as data URL
    return canvas.toDataURL('image/jpeg', 0.8);
  };

  const detectFacesInImage = async (imageElement: HTMLImageElement): Promise<FaceDetectionResult[]> => {
    if (!modelsLoaded) {
      throw new Error('Face recognition models not loaded');
    }

    try {
      const detections = await faceapi
        .detectAllFaces(imageElement)
        .withFaceLandmarks()
        .withFaceDescriptors();

      return detections.map((detection) => ({
        descriptor: Array.from(detection.descriptor),
        confidence: detection.detection.score,
        boundingBox: {
          x: detection.detection.box.x,
          y: detection.detection.box.y,
          width: detection.detection.box.width,
          height: detection.detection.box.height,
        },
      }));
    } catch (error) {
      console.error('Error detecting faces in image:', error);
      return [];
    }
  };

  // Expose methods for parent components
  useEffect(() => {
    if (videoRef.current) {
      (videoRef.current as any).capturePhoto = capturePhoto;
      (videoRef.current as any).detectFacesInImage = detectFacesInImage;
    }
  }, [modelsLoaded]);

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center bg-gray-900 rounded-lg ${className}`}>
        <div className="text-center text-white">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
          <p>Loading face recognition...</p>
        </div>
      </div>
    );
  }

  if (!modelsLoaded) {
    return (
      <div className={`flex items-center justify-center bg-gray-900 rounded-lg ${className}`}>
        <div className="text-center text-white">
          <p>Failed to load face recognition models</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <video
        ref={videoRef}
        autoPlay
        playsInline
        muted
        className="w-full rounded-lg"
        style={{ maxHeight: '400px' }}
      />
      <canvas
        ref={canvasRef}
        className="absolute top-0 left-0 w-full h-full pointer-events-none"
      />
      
      {!stream && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-900 rounded-lg">
          <div className="text-center text-white">
            <p>Camera not available</p>
          </div>
        </div>
      )}
    </div>
  );
}
