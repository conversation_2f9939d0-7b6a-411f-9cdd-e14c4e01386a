import { Badge } from "@/components/ui/badge";
import { CheckCircle, Clock, XCircle, AlertCircle } from "lucide-react";

interface StatusBadgeProps {
  status: "active" | "pending" | "blocked" | "inactive" | "approved" | "rejected";
  className?: string;
}

export default function StatusBadge({ status, className }: StatusBadgeProps) {
  const getStatusConfig = () => {
    switch (status) {
      case "active":
      case "approved":
        return {
          variant: "success" as const,
          icon: <CheckCircle className="w-3 h-3 mr-1" />,
          label: status === "active" ? "Active" : "Approved",
        };
      case "pending":
        return {
          variant: "warning" as const,
          icon: <Clock className="w-3 h-3 mr-1" />,
          label: "Pending",
        };
      case "blocked":
      case "rejected":
        return {
          variant: "destructive" as const,
          icon: <XCircle className="w-3 h-3 mr-1" />,
          label: status === "blocked" ? "Blocked" : "Rejected",
        };
      case "inactive":
        return {
          variant: "secondary" as const,
          icon: <AlertCircle className="w-3 h-3 mr-1" />,
          label: "Inactive",
        };
      default:
        return {
          variant: "secondary" as const,
          icon: null,
          label: status,
        };
    }
  };

  const config = getStatusConfig();

  return (
    <Badge variant={config.variant} className={className}>
      {config.icon}
      {config.label}
    </Badge>
  );
}
