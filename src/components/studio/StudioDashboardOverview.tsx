"use client";

import { useState, useEffect } from "react";
import {
  Camera,
  Users,
  Upload,
  HardDrive,
  Eye,
  Download,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  Star,
  Zap,
  Activity,
  Calendar,
  Shield,
  BarChart3
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { LineChart, Line, AreaChart, Area, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { formatFileSize, formatCurrency, timeAgo, cn } from "@/lib/utils";

interface StudioStats {
  storageUsed: number;
  storageLimit: number;
  totalClients: number;
  totalPhotos: number;
  matchedPhotos: number;
  unmatchedPhotos: number;
  recentUploads: number;
  subscriptionStatus: string;
  planName: string;
  monthlyUploads: number;
  clientsThisMonth: number;
  downloadsThisMonth: number;
  storageGrowth: number;
  planExpiry: string;
  securityScore: number;
}

interface ActivityItem {
  id: string;
  type: 'upload' | 'download' | 'client_access' | 'client_added' | 'security';
  title: string;
  description: string;
  timestamp: string;
  clientName?: string;
  photoCount?: number;
  icon: React.ComponentType<{ className?: string }>;
}

const uploadTrendData = [
  { name: 'Mon', uploads: 12, downloads: 45 },
  { name: 'Tue', uploads: 19, downloads: 52 },
  { name: 'Wed', uploads: 8, downloads: 38 },
  { name: 'Thu', uploads: 25, downloads: 67 },
  { name: 'Fri', uploads: 32, downloads: 89 },
  { name: 'Sat', uploads: 45, downloads: 123 },
  { name: 'Sun', uploads: 28, downloads: 76 },
];

export default function StudioDashboardOverview() {
  const [stats, setStats] = useState<StudioStats>({
    storageUsed: 4300000000, // 4.3GB
    storageLimit: 10000000000, // 10GB
    totalClients: 25,
    totalPhotos: 1250,
    matchedPhotos: 980,
    unmatchedPhotos: 270,
    recentUploads: 45,
    subscriptionStatus: "active",
    planName: "Pro Plan",
    monthlyUploads: 234,
    clientsThisMonth: 8,
    downloadsThisMonth: 456,
    storageGrowth: 12.5,
    planExpiry: "2024-03-15",
    securityScore: 85,
  });

  const [recentActivity, setRecentActivity] = useState<ActivityItem[]>([
    {
      id: '1',
      type: 'upload',
      title: 'New photos uploaded',
      description: 'Wedding Event - Sarah & Mike',
      timestamp: '2024-01-15T10:30:00Z',
      photoCount: 25,
      icon: Upload
    },
    {
      id: '2',
      type: 'client_access',
      title: 'Client accessed gallery',
      description: 'Sarah Johnson viewed wedding photos',
      timestamp: '2024-01-15T08:15:00Z',
      clientName: 'Sarah Johnson',
      icon: Eye
    },
    {
      id: '3',
      type: 'download',
      title: 'Photos downloaded',
      description: 'Mike Davis downloaded 15 high-res photos',
      timestamp: '2024-01-15T06:45:00Z',
      clientName: 'Mike Davis',
      photoCount: 15,
      icon: Download
    },
    {
      id: '4',
      type: 'client_added',
      title: 'New client added',
      description: 'Emma Wilson - Birthday Event',
      timestamp: '2024-01-14T16:20:00Z',
      clientName: 'Emma Wilson',
      icon: Users
    },
    {
      id: '5',
      type: 'security',
      title: 'Security scan completed',
      description: 'All systems secure, no issues found',
      timestamp: '2024-01-14T12:00:00Z',
      icon: Shield
    }
  ]);

  const storagePercentage = (stats.storageUsed / stats.storageLimit) * 100;
  const matchPercentage = (stats.matchedPhotos / stats.totalPhotos) * 100;

  const getStorageColor = () => {
    if (storagePercentage > 90) return "destructive";
    if (storagePercentage > 75) return "warning";
    return "default";
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'upload': return Upload;
      case 'download': return Download;
      case 'client_access': return Eye;
      case 'client_added': return Users;
      case 'security': return Shield;
      default: return Activity;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'upload': return 'text-green-600';
      case 'download': return 'text-blue-600';
      case 'client_access': return 'text-purple-600';
      case 'client_added': return 'text-orange-600';
      case 'security': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Welcome back! Here's what's happening in your studio.
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Badge variant={stats.subscriptionStatus === 'active' ? 'success' : 'destructive'}>
            {stats.planName}
          </Badge>
          <Button variant="outline" size="sm">
            <Calendar className="h-4 w-4 mr-2" />
            View Calendar
          </Button>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Storage Usage */}
        <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">Storage Used</CardTitle>
            <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <HardDrive className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
              {formatFileSize(stats.storageUsed)}
            </div>
            <div className="mt-2 space-y-2">
              <div className="flex items-center justify-between text-xs">
                <span className="text-blue-700 dark:text-blue-300">
                  of {formatFileSize(stats.storageLimit)}
                </span>
                <span className="font-medium">
                  {storagePercentage.toFixed(1)}%
                </span>
              </div>
              <Progress 
                value={storagePercentage} 
                className="h-2"
                variant={getStorageColor()}
              />
              {storagePercentage > 80 && (
                <div className="flex items-center text-xs text-amber-600 dark:text-amber-400">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  Storage running low
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Total Clients */}
        <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-700 dark:text-green-300">Total Clients</CardTitle>
            <div className="h-8 w-8 bg-green-600 rounded-lg flex items-center justify-center">
              <Users className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-900 dark:text-green-100">
              {stats.totalClients}
            </div>
            <div className="flex items-center gap-1 mt-1">
              <TrendingUp className="h-3 w-3 text-green-600" />
              <span className="text-xs text-green-600 font-medium">
                +{stats.clientsThisMonth} this month
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Total Photos */}
        <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-700 dark:text-purple-300">Total Photos</CardTitle>
            <div className="h-8 w-8 bg-purple-600 rounded-lg flex items-center justify-center">
              <Camera className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">
              {stats.totalPhotos.toLocaleString()}
            </div>
            <div className="mt-2 space-y-1">
              <div className="flex items-center justify-between text-xs">
                <span className="text-purple-700 dark:text-purple-300">
                  {stats.matchedPhotos} matched
                </span>
                <span className="font-medium">
                  {matchPercentage.toFixed(1)}%
                </span>
              </div>
              <Progress value={matchPercentage} className="h-1" />
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-700 dark:text-orange-300">Recent Uploads</CardTitle>
            <div className="h-8 w-8 bg-orange-600 rounded-lg flex items-center justify-center">
              <Upload className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-900 dark:text-orange-100">
              {stats.recentUploads}
            </div>
            <div className="flex items-center gap-1 mt-1">
              <TrendingUp className="h-3 w-3 text-orange-600" />
              <span className="text-xs text-orange-600 font-medium">
                +{stats.monthlyUploads} this month
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upload Trends */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              Weekly Activity
            </CardTitle>
            <CardDescription>
              Photo uploads and downloads over the last 7 days
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={uploadTrendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Area 
                  type="monotone" 
                  dataKey="uploads" 
                  stackId="1"
                  stroke="#3B82F6" 
                  fill="#3B82F6" 
                  fillOpacity={0.6}
                />
                <Area 
                  type="monotone" 
                  dataKey="downloads" 
                  stackId="2"
                  stroke="#10B981" 
                  fill="#10B981" 
                  fillOpacity={0.6}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-green-600" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Latest actions and updates in your studio
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 max-h-80 overflow-y-auto">
              {recentActivity.map((activity) => {
                const IconComponent = activity.icon;
                return (
                  <div key={activity.id} className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                    <div className={cn("p-2 rounded-lg", getActivityColor(activity.type).replace('text-', 'bg-').replace('-600', '-100'), "dark:bg-opacity-20")}>
                      <IconComponent className={cn("h-4 w-4", getActivityColor(activity.type))} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {activity.title}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                        {activity.description}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                        {timeAgo(activity.timestamp)}
                      </p>
                    </div>
                    {activity.photoCount && (
                      <Badge variant="secondary" className="text-xs">
                        {activity.photoCount} photos
                      </Badge>
                    )}
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border-0 shadow-md hover:shadow-lg transition-all duration-200 cursor-pointer bg-gradient-to-br from-blue-500 to-blue-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="h-12 w-12 bg-white/20 rounded-xl flex items-center justify-center">
                <Upload className="h-6 w-6" />
              </div>
              <div>
                <h3 className="font-semibold">Upload Photos</h3>
                <p className="text-sm text-blue-100">Add new photos to your gallery</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-md hover:shadow-lg transition-all duration-200 cursor-pointer bg-gradient-to-br from-green-500 to-green-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="h-12 w-12 bg-white/20 rounded-xl flex items-center justify-center">
                <Users className="h-6 w-6" />
              </div>
              <div>
                <h3 className="font-semibold">Add Client</h3>
                <p className="text-sm text-green-100">Create new client account</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-md hover:shadow-lg transition-all duration-200 cursor-pointer bg-gradient-to-br from-purple-500 to-purple-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="h-12 w-12 bg-white/20 rounded-xl flex items-center justify-center">
                <BarChart3 className="h-6 w-6" />
              </div>
              <div>
                <h3 className="font-semibold">View Analytics</h3>
                <p className="text-sm text-purple-100">Check detailed reports</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
