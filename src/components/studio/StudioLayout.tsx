"use client";

import { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { 
  Camera,
  LayoutDashboard,
  Users,
  Upload,
  BarChart3,
  Settings,
  CreditCard,
  Bell,
  Shield,
  HardDrive,
  Download,
  Share2,
  FileText,
  LogOut,
  Menu,
  X,
  Sun,
  Moon,
  Monitor,
  ChevronLeft,
  ChevronRight,
  Crown,
  Trash2
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { useTheme } from "@/contexts/ThemeContext";

interface StudioLayoutProps {
  children: React.ReactNode;
}

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: number;
  isPro?: boolean;
}

const navigation: NavItem[] = [
  { name: "Dashboard", href: "/studio", icon: LayoutDashboard },
  { name: "Clients", href: "/studio/clients", icon: Users },
  { name: "Upload Photos", href: "/studio/upload", icon: Upload },
  { name: "Gallery", href: "/studio/gallery", icon: Camera },
  { name: "Storage", href: "/studio/storage", icon: HardDrive },
  { name: "Downloads", href: "/studio/downloads", icon: Download },
  { name: "Sharing", href: "/studio/sharing", icon: Share2 },
  { name: "Analytics", href: "/studio/analytics", icon: BarChart3, isPro: true },
  { name: "Notifications", href: "/studio/notifications", icon: Bell, badge: 3 },
  { name: "Trash", href: "/studio/trash", icon: Trash2 },
  { name: "Billing", href: "/studio/billing", icon: CreditCard },
  { name: "Reports", href: "/studio/reports", icon: FileText, isPro: true },
  { name: "Security", href: "/studio/security", icon: Shield },
  { name: "Settings", href: "/studio/settings", icon: Settings },
];

interface User {
  name: string;
  email: string;
  businessName?: string;
  avatar: string;
  plan: string;
  isPro: boolean;
}

export default function StudioLayout({ children }: StudioLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const router = useRouter();
  const pathname = usePathname();
  const { theme, setTheme } = useTheme();

  useEffect(() => {
    // Check authentication
    const token = localStorage.getItem("token");
    if (!token) {
      router.push("/auth/login");
      return;
    }

    // TODO: Fetch user data from API
    setUser({
      name: "John's Photography",
      email: "<EMAIL>",
      businessName: "John's Wedding Photography",
      avatar: "/api/placeholder/40/40",
      plan: "Pro Plan",
      isPro: true
    });
  }, [router]);

  const toggleTheme = () => {
    if (theme === 'light') {
      setTheme('dark');
    } else if (theme === 'dark') {
      setTheme('system');
    } else {
      setTheme('light');
    }
  };

  const handleLogout = () => {
    localStorage.removeItem("token");
    router.push("/");
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        >
          <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm"></div>
        </div>
      )}

      {/* Sidebar */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 flex flex-col bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-lg transition-all duration-300 ease-in-out",
        sidebarOpen ? "w-64" : "w-0 lg:w-64",
        sidebarCollapsed && "lg:w-16"
      )}>
        {/* Sidebar header */}
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-600 to-blue-700">
          <div className={cn("flex items-center", sidebarCollapsed && "lg:justify-center")}>
            <Camera className="h-8 w-8 text-white" />
            {!sidebarCollapsed && (
              <span className="ml-2 text-xl font-bold text-white">
                Studio ERP
              </span>
            )}
          </div>
          
          {/* Mobile close button */}
          <Button
            variant="ghost"
            size="sm"
            className="lg:hidden text-white hover:bg-white/20"
            onClick={() => setSidebarOpen(false)}
          >
            <X className="h-5 w-5" />
          </Button>

          {/* Desktop collapse button */}
          <Button
            variant="ghost"
            size="sm"
            className="hidden lg:flex text-white hover:bg-white/20"
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
          >
            {sidebarCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Studio Info */}
        {!sidebarCollapsed && (
          <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-600">
            <div className="flex items-center gap-3">
              <img
                className="h-10 w-10 rounded-full border-2 border-blue-200 dark:border-blue-400"
                src={user.avatar}
                alt={user.name}
              />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-semibold text-gray-900 dark:text-white truncate">
                  {user.businessName || user.name}
                </p>
                <div className="flex items-center gap-2">
                  <Badge variant={user.isPro ? "default" : "secondary"} className="text-xs">
                    {user.isPro && <Crown className="h-3 w-3 mr-1" />}
                    {user.plan}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Navigation */}
        <nav className="flex-1 px-3 py-6 space-y-2 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
          {navigation.map((item) => {
            const isActive = pathname === item.href;
            const isDisabled = item.isPro && !user.isPro;
            
            return (
              <a
                key={item.name}
                href={isDisabled ? "#" : item.href}
                className={cn(
                  "group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 ease-in-out relative",
                  isActive
                    ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg shadow-blue-500/25"
                    : isDisabled
                    ? "text-gray-400 dark:text-gray-500 cursor-not-allowed"
                    : "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white hover:shadow-md",
                  sidebarCollapsed && "lg:justify-center lg:px-2"
                )}
                onClick={isDisabled ? (e) => e.preventDefault() : undefined}
              >
                <item.icon className={cn(
                  "flex-shrink-0 h-5 w-5 transition-colors",
                  isActive ? "text-white" : isDisabled ? "text-gray-400" : "text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300"
                )} />
                {!sidebarCollapsed && (
                  <>
                    <span className="ml-3 font-medium">{item.name}</span>
                    <div className="ml-auto flex items-center gap-1">
                      {item.badge && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-red-500 text-white animate-pulse">
                          {item.badge}
                        </span>
                      )}
                      {item.isPro && !user.isPro && (
                        <Crown className="h-3 w-3 text-yellow-500" />
                      )}
                    </div>
                  </>
                )}
                {sidebarCollapsed && item.badge && (
                  <span className="absolute -top-1 -right-1 inline-flex items-center justify-center px-1.5 py-0.5 rounded-full text-xs font-bold bg-red-500 text-white">
                    {item.badge}
                  </span>
                )}
              </a>
            );
          })}
        </nav>

        {/* User section */}
        <div className="border-t border-gray-200 dark:border-gray-700 p-4 bg-gray-50 dark:bg-gray-750">
          <div className={cn("flex items-center", sidebarCollapsed && "lg:justify-center")}>
            <div className="relative">
              <img
                className="h-10 w-10 rounded-full border-2 border-blue-200 dark:border-blue-400"
                src={user.avatar}
                alt={user.name}
              />
              <div className="absolute -bottom-1 -right-1 h-4 w-4 bg-green-400 border-2 border-white dark:border-gray-800 rounded-full"></div>
            </div>
            {!sidebarCollapsed && (
              <div className="ml-3 flex-1">
                <p className="text-sm font-semibold text-gray-900 dark:text-white">
                  {user.name}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {user.email}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className={cn(
        "flex-1 flex flex-col transition-all duration-300 ease-in-out",
        sidebarCollapsed ? "lg:ml-16" : "lg:ml-64"
      )}>
        {/* Top header */}
        <header className="sticky top-0 z-30 bg-white/80 dark:bg-gray-800/80 backdrop-blur-md shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <Button
                  variant="ghost"
                  size="sm"
                  className="lg:hidden hover:bg-gray-100 dark:hover:bg-gray-700"
                  onClick={() => setSidebarOpen(true)}
                >
                  <Menu className="h-5 w-5" />
                </Button>
                <div className="ml-4 lg:ml-0">
                  <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Photo Studio Management
                  </h1>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleTheme}
                  className="hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full p-2"
                  title={`Switch to ${theme === 'light' ? 'dark' : theme === 'dark' ? 'system' : 'light'} theme`}
                >
                  {theme === 'light' ? (
                    <Sun className="h-5 w-5 text-yellow-500" />
                  ) : theme === 'dark' ? (
                    <Moon className="h-5 w-5 text-blue-400" />
                  ) : (
                    <Monitor className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                  )}
                </Button>
                
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleLogout}
                  className="hover:bg-red-50 hover:border-red-200 hover:text-red-600 dark:hover:bg-red-900/20 dark:hover:border-red-800 dark:hover:text-red-400"
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  Logout
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-900">
          <div className="p-6 lg:p-8 max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
