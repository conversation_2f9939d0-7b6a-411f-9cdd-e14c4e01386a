"use client";

import { useState, useEffect } from "react";
import { 
  Building2, 
  Users, 
  Camera, 
  HardDrive, 
  DollarSign,
  TrendingUp,
  TrendingDown,
  Clock,
  CheckCircle,
  AlertTriangle,
  Upload,
  Download,
  Eye
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';

interface DashboardStats {
  totalStudios: number;
  activeStudios: number;
  pendingApprovals: number;
  totalClients: number;
  totalPhotos: number;
  totalStorage: number;
  storageLimit: number;
  monthlyRevenue: number;
  revenueGrowth: number;
  uploadsToday: number;
  downloadsToday: number;
  activeUsers: number;
}

interface ChartData {
  name: string;
  value: number;
  uploads?: number;
  downloads?: number;
  revenue?: number;
}

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];

export default function DashboardOverview() {
  const [stats, setStats] = useState<DashboardStats>({
    totalStudios: 45,
    activeStudios: 38,
    pendingApprovals: 7,
    totalClients: 1250,
    totalPhotos: 45000,
    totalStorage: 2500000000000, // 2.5TB
    storageLimit: 5000000000000, // 5TB
    monthlyRevenue: 157500,
    revenueGrowth: 12.5,
    uploadsToday: 234,
    downloadsToday: 1456,
    activeUsers: 89,
  });

  const [uploadsData] = useState<ChartData[]>([
    { name: 'Jan', uploads: 2400, downloads: 4800, revenue: 120000 },
    { name: 'Feb', uploads: 1398, downloads: 3200, revenue: 135000 },
    { name: 'Mar', uploads: 9800, downloads: 6400, revenue: 142000 },
    { name: 'Apr', uploads: 3908, downloads: 7200, revenue: 148000 },
    { name: 'May', uploads: 4800, downloads: 8100, revenue: 155000 },
    { name: 'Jun', uploads: 3800, downloads: 7800, revenue: 157500 },
  ]);

  const [storageData] = useState<ChartData[]>([
    { name: 'Images', value: 65 },
    { name: 'Videos', value: 25 },
    { name: 'RAW Files', value: 8 },
    { name: 'Others', value: 2 },
  ]);

  const [topStudios] = useState([
    { name: 'Pixel Perfect Studio', uploads: 1250, storage: '450 GB', revenue: 25000 },
    { name: 'Moments Photography', uploads: 980, storage: '380 GB', revenue: 22000 },
    { name: 'Creative Lens Studio', uploads: 875, storage: '320 GB', revenue: 18500 },
  ]);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const storageUsagePercent = (stats.totalStorage / stats.storageLimit) * 100;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Dashboard</h1>
        <p className="text-gray-600 dark:text-gray-400">
          Welcome back! Here's what's happening with your photo studio platform.
        </p>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Studios */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Studios</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalStudios}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <span>{stats.activeStudios} active</span>
              <Badge variant="destructive" className="text-xs">
                {stats.pendingApprovals} pending
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Total Clients */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Clients</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalClients.toLocaleString()}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <Eye className="h-3 w-3 mr-1" />
              <span>{stats.activeUsers} active today</span>
            </div>
          </CardContent>
        </Card>

        {/* Total Photos */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Photos</CardTitle>
            <Camera className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalPhotos.toLocaleString()}</div>
            <div className="flex items-center text-xs text-green-600">
              <Upload className="h-3 w-3 mr-1" />
              <span>+{stats.uploadsToday} today</span>
            </div>
          </CardContent>
        </Card>

        {/* Monthly Revenue */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.monthlyRevenue)}</div>
            <div className="flex items-center text-xs text-green-600">
              <TrendingUp className="h-3 w-3 mr-1" />
              <span>+{stats.revenueGrowth}% from last month</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Storage Usage Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <HardDrive className="h-5 w-5 mr-2" />
            Storage Usage
          </CardTitle>
          <CardDescription>
            Current storage utilization across all studios
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">
                {formatFileSize(stats.totalStorage)} of {formatFileSize(stats.storageLimit)} used
              </span>
              <span className="text-sm text-muted-foreground">
                {storageUsagePercent.toFixed(1)}%
              </span>
            </div>
            <Progress value={storageUsagePercent} className="h-2" />
            {storageUsagePercent > 80 && (
              <div className="flex items-center text-sm text-amber-600">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Storage usage is high. Consider upgrading or cleaning up old files.
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upload Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Upload Trends</CardTitle>
            <CardDescription>Photo uploads over the last 6 months</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={uploadsData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Area 
                  type="monotone" 
                  dataKey="uploads" 
                  stroke="#3B82F6" 
                  fill="#3B82F6" 
                  fillOpacity={0.1}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Storage Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Storage Breakdown</CardTitle>
            <CardDescription>File types distribution</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={storageData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {storageData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Top Performing Studios */}
      <Card>
        <CardHeader>
          <CardTitle>Top Performing Studios</CardTitle>
          <CardDescription>Studios with highest activity this month</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {topStudios.map((studio, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full">
                    <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                      #{index + 1}
                    </span>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {studio.name}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {studio.uploads} uploads • {studio.storage} storage
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {formatCurrency(studio.revenue)}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    This month
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
