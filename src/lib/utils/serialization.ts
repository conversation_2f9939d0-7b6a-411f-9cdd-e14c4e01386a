/**
 * Utility functions to handle BigInt serialization for JSON responses
 */

export function serializeBigInt(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'bigint') {
    return obj.toString();
  }

  if (Array.isArray(obj)) {
    return obj.map(serializeBigInt);
  }

  if (typeof obj === 'object') {
    const serialized: any = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        serialized[key] = serializeBigInt(obj[key]);
      }
    }
    return serialized;
  }

  return obj;
}

export function createJsonResponse(data: any, options: { success?: boolean; message?: string } = {}) {
  const { success = true, message } = options;

  const response = {
    success,
    ...(message && { message }),
    ...(data && { data: serializeBigInt(data) }),
  };

  return Response.json(response);
}

// Helper function specifically for NextResponse.json with BigInt serialization
export function createNextJsonResponse(responseData: any) {
  return Response.json(serializeBigInt(responseData));
}
