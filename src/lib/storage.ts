import { formatFileSize } from './utils';

export interface StorageItem {
  id: string;
  name: string;
  type: 'image' | 'video' | 'document' | 'archive' | 'folder';
  size: number;
  clientId?: string;
  clientName?: string;
  eventName?: string;
  uploadDate: string;
  lastAccessed?: string;
  downloadCount: number;
  isStarred: boolean;
  tags: string[];
  aiTags?: string[];
  faceCount?: number;
  status: 'active' | 'archived' | 'deleted';
  compressionRatio?: number;
  originalSize?: number;
  duplicateOf?: string;
}

export interface StorageAnalysis {
  totalSize: number;
  totalFiles: number;
  typeBreakdown: {
    type: string;
    count: number;
    size: number;
    percentage: number;
    avgSize: number;
  }[];
  duplicates: {
    originalId: string;
    duplicateIds: string[];
    totalWastedSpace: number;
  }[];
  largeFiles: StorageItem[];
  oldFiles: StorageItem[];
  untaggedFiles: StorageItem[];
  compressionOpportunities: {
    fileId: string;
    currentSize: number;
    estimatedCompressedSize: number;
    potentialSavings: number;
  }[];
  recommendations: StorageRecommendation[];
}

export interface StorageRecommendation {
  type: 'compression' | 'deletion' | 'archival' | 'deduplication' | 'cleanup';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  potentialSavings: number;
  affectedFiles: number;
  action: string;
}

export interface StorageQuota {
  used: number;
  total: number;
  percentage: number;
  remaining: number;
  warningThreshold: number;
  criticalThreshold: number;
}

export class StorageManager {
  static analyzeStorage(items: StorageItem[]): StorageAnalysis {
    const activeItems = items.filter(item => item.status === 'active');
    const totalSize = activeItems.reduce((sum, item) => sum + item.size, 0);
    const totalFiles = activeItems.length;

    // Type breakdown
    const typeBreakdown = this.getTypeBreakdown(activeItems, totalSize);

    // Find duplicates (simplified - in real implementation would use file hashes)
    const duplicates = this.findDuplicates(activeItems);

    // Large files (top 10% by size)
    const largeFiles = this.getLargeFiles(activeItems);

    // Old files (not accessed in 90+ days)
    const oldFiles = this.getOldFiles(activeItems);

    // Untagged files
    const untaggedFiles = activeItems.filter(item => 
      item.tags.length === 0 && (!item.aiTags || item.aiTags.length === 0)
    );

    // Compression opportunities
    const compressionOpportunities = this.getCompressionOpportunities(activeItems);

    // Generate recommendations
    const recommendations = this.generateRecommendations(
      duplicates,
      largeFiles,
      oldFiles,
      untaggedFiles,
      compressionOpportunities
    );

    return {
      totalSize,
      totalFiles,
      typeBreakdown,
      duplicates,
      largeFiles,
      oldFiles,
      untaggedFiles,
      compressionOpportunities,
      recommendations
    };
  }

  private static getTypeBreakdown(items: StorageItem[], totalSize: number) {
    const types = ['image', 'video', 'document', 'archive', 'folder'];
    return types.map(type => {
      const typeItems = items.filter(item => item.type === type);
      const typeSize = typeItems.reduce((sum, item) => sum + item.size, 0);
      return {
        type,
        count: typeItems.length,
        size: typeSize,
        percentage: (typeSize / totalSize) * 100,
        avgSize: typeItems.length > 0 ? typeSize / typeItems.length : 0
      };
    }).filter(type => type.count > 0);
  }

  private static findDuplicates(items: StorageItem[]) {
    // Simplified duplicate detection based on name and size
    const duplicates: { [key: string]: StorageItem[] } = {};
    
    items.forEach(item => {
      const key = `${item.name}_${item.size}`;
      if (!duplicates[key]) {
        duplicates[key] = [];
      }
      duplicates[key].push(item);
    });

    return Object.values(duplicates)
      .filter(group => group.length > 1)
      .map(group => ({
        originalId: group[0].id,
        duplicateIds: group.slice(1).map(item => item.id),
        totalWastedSpace: group.slice(1).reduce((sum, item) => sum + item.size, 0)
      }));
  }

  private static getLargeFiles(items: StorageItem[]) {
    return items
      .sort((a, b) => b.size - a.size)
      .slice(0, Math.ceil(items.length * 0.1)); // Top 10%
  }

  private static getOldFiles(items: StorageItem[]) {
    const ninetyDaysAgo = new Date();
    ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

    return items.filter(item => {
      const lastAccessed = item.lastAccessed ? new Date(item.lastAccessed) : new Date(item.uploadDate);
      return lastAccessed < ninetyDaysAgo && item.downloadCount === 0;
    });
  }

  private static getCompressionOpportunities(items: StorageItem[]) {
    return items
      .filter(item => item.type === 'image' || item.type === 'video')
      .filter(item => item.size > 10 * 1024 * 1024) // Files larger than 10MB
      .map(item => {
        const estimatedCompression = item.type === 'image' ? 0.7 : 0.8; // 30% for images, 20% for videos
        const estimatedCompressedSize = Math.floor(item.size * estimatedCompression);
        return {
          fileId: item.id,
          currentSize: item.size,
          estimatedCompressedSize,
          potentialSavings: item.size - estimatedCompressedSize
        };
      })
      .filter(opportunity => opportunity.potentialSavings > 1024 * 1024); // At least 1MB savings
  }

  private static generateRecommendations(
    duplicates: any[],
    largeFiles: StorageItem[],
    oldFiles: StorageItem[],
    untaggedFiles: StorageItem[],
    compressionOpportunities: any[]
  ): StorageRecommendation[] {
    const recommendations: StorageRecommendation[] = [];

    // Duplicate files recommendation
    if (duplicates.length > 0) {
      const totalWasted = duplicates.reduce((sum, dup) => sum + dup.totalWastedSpace, 0);
      const totalDuplicates = duplicates.reduce((sum, dup) => sum + dup.duplicateIds.length, 0);
      
      recommendations.push({
        type: 'deduplication',
        priority: 'high',
        title: 'Remove Duplicate Files',
        description: `Found ${totalDuplicates} duplicate files wasting ${formatFileSize(totalWasted)} of storage space.`,
        potentialSavings: totalWasted,
        affectedFiles: totalDuplicates,
        action: 'Review and remove duplicate files'
      });
    }

    // Old files recommendation
    if (oldFiles.length > 0) {
      const totalOldSize = oldFiles.reduce((sum, file) => sum + file.size, 0);
      
      recommendations.push({
        type: 'archival',
        priority: 'medium',
        title: 'Archive Old Files',
        description: `${oldFiles.length} files haven't been accessed in 90+ days and could be archived.`,
        potentialSavings: totalOldSize * 0.8, // Assume 80% compression for archived files
        affectedFiles: oldFiles.length,
        action: 'Archive old, unused files'
      });
    }

    // Compression opportunities
    if (compressionOpportunities.length > 0) {
      const totalSavings = compressionOpportunities.reduce((sum, opp) => sum + opp.potentialSavings, 0);
      
      recommendations.push({
        type: 'compression',
        priority: 'medium',
        title: 'Compress Large Media Files',
        description: `${compressionOpportunities.length} large media files could be compressed to save space.`,
        potentialSavings: totalSavings,
        affectedFiles: compressionOpportunities.length,
        action: 'Apply compression to large media files'
      });
    }

    // Untagged files recommendation
    if (untaggedFiles.length > 0) {
      recommendations.push({
        type: 'cleanup',
        priority: 'low',
        title: 'Tag Unorganized Files',
        description: `${untaggedFiles.length} files have no tags, making them hard to find and manage.`,
        potentialSavings: 0,
        affectedFiles: untaggedFiles.length,
        action: 'Add tags to improve organization'
      });
    }

    return recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  static calculateQuota(usedSpace: number, totalSpace: number): StorageQuota {
    const percentage = (usedSpace / totalSpace) * 100;
    
    return {
      used: usedSpace,
      total: totalSpace,
      percentage,
      remaining: totalSpace - usedSpace,
      warningThreshold: 80,
      criticalThreshold: 95
    };
  }

  static getStorageStatus(quota: StorageQuota): 'normal' | 'warning' | 'critical' {
    if (quota.percentage >= quota.criticalThreshold) return 'critical';
    if (quota.percentage >= quota.warningThreshold) return 'warning';
    return 'normal';
  }

  static async optimizeStorage(items: StorageItem[], options: {
    removeDuplicates?: boolean;
    compressImages?: boolean;
    archiveOldFiles?: boolean;
    deleteUnusedFiles?: boolean;
  }): Promise<{
    optimizedItems: StorageItem[];
    spaceSaved: number;
    actions: string[];
  }> {
    let optimizedItems = [...items];
    let spaceSaved = 0;
    const actions: string[] = [];

    const analysis = this.analyzeStorage(items);

    // Remove duplicates
    if (options.removeDuplicates && analysis.duplicates.length > 0) {
      const duplicateIds = analysis.duplicates.flatMap(dup => dup.duplicateIds);
      const duplicateSize = duplicateIds.reduce((sum, id) => {
        const item = optimizedItems.find(i => i.id === id);
        return sum + (item?.size || 0);
      }, 0);
      
      optimizedItems = optimizedItems.filter(item => !duplicateIds.includes(item.id));
      spaceSaved += duplicateSize;
      actions.push(`Removed ${duplicateIds.length} duplicate files`);
    }

    // Compress images
    if (options.compressImages && analysis.compressionOpportunities.length > 0) {
      analysis.compressionOpportunities.forEach(opp => {
        const itemIndex = optimizedItems.findIndex(item => item.id === opp.fileId);
        if (itemIndex !== -1) {
          optimizedItems[itemIndex] = {
            ...optimizedItems[itemIndex],
            originalSize: optimizedItems[itemIndex].size,
            size: opp.estimatedCompressedSize,
            compressionRatio: opp.estimatedCompressedSize / optimizedItems[itemIndex].size
          };
          spaceSaved += opp.potentialSavings;
        }
      });
      actions.push(`Compressed ${analysis.compressionOpportunities.length} media files`);
    }

    // Archive old files
    if (options.archiveOldFiles && analysis.oldFiles.length > 0) {
      const oldFileIds = analysis.oldFiles.map(f => f.id);
      const archivedSize = analysis.oldFiles.reduce((sum, f) => sum + f.size, 0);
      
      optimizedItems = optimizedItems.map(item => 
        oldFileIds.includes(item.id) 
          ? { ...item, status: 'archived' as const }
          : item
      );
      
      spaceSaved += archivedSize * 0.8; // Assume 80% space savings from archival
      actions.push(`Archived ${analysis.oldFiles.length} old files`);
    }

    return {
      optimizedItems,
      spaceSaved,
      actions
    };
  }

  static generateStorageReport(analysis: StorageAnalysis, quota: StorageQuota) {
    const status = this.getStorageStatus(quota);
    
    return {
      summary: {
        totalFiles: analysis.totalFiles,
        totalSize: formatFileSize(analysis.totalSize),
        usagePercentage: quota.percentage.toFixed(1),
        remainingSpace: formatFileSize(quota.remaining),
        status
      },
      breakdown: analysis.typeBreakdown.map(type => ({
        type: type.type,
        count: type.count,
        size: formatFileSize(type.size),
        percentage: type.percentage.toFixed(1),
        avgSize: formatFileSize(type.avgSize)
      })),
      issues: {
        duplicates: analysis.duplicates.length,
        oldFiles: analysis.oldFiles.length,
        untaggedFiles: analysis.untaggedFiles.length,
        compressionOpportunities: analysis.compressionOpportunities.length
      },
      recommendations: analysis.recommendations,
      potentialSavings: formatFileSize(
        analysis.recommendations.reduce((sum, rec) => sum + rec.potentialSavings, 0)
      )
    };
  }
}

export default StorageManager;
