import { prisma } from '@/lib/db';
import { verifyPassword } from '@/lib/auth';

export class ClientService {
  // Authenticate client with access code and password
  static async authenticateClient(accessCode: string, password: string) {
    try {
      // Find client by access link or QR code
      const client = await prisma.client.findFirst({
        where: {
          OR: [
            { accessLink: accessCode },
            { qrCode: accessCode },
          ],
          isActive: true,
        },
        include: {
          studio: {
            select: {
              name: true,
              businessName: true,
              isActive: true,
              isApproved: true,
            },
          },
        },
      });

      if (!client) {
        return { success: false, message: 'Invalid access code' };
      }

      if (!client.studio.isActive || !client.studio.isApproved) {
        return { success: false, message: 'Studio is not active' };
      }

      // Verify password
      const isValidPassword = await verifyPassword(password, client.password);
      if (!isValidPassword) {
        return { success: false, message: 'Invalid password' };
      }

      // Update last access
      await prisma.client.update({
        where: { id: client.id },
        data: { lastAccess: new Date() },
      });

      // Log access
      await prisma.accessLog.create({
        data: {
          clientId: client.id,
          ipAddress: '0.0.0.0', // This should be passed from the request
          action: 'login',
        },
      });

      return {
        success: true,
        client: {
          id: client.id,
          name: client.name,
          email: client.email,
          studioName: client.studio.businessName || client.studio.name,
        },
      };
    } catch (error) {
      console.error('Error authenticating client:', error);
      return { success: false, message: 'Authentication failed' };
    }
  }

  // Get client photos with pagination and filters
  static async getClientPhotos(clientId: string, params: {
    page?: number;
    limit?: number;
    eventId?: string;
    sortBy?: 'uploadedAt' | 'filename';
    sortOrder?: 'asc' | 'desc';
  }) {
    try {
      const {
        page = 1,
        limit = 20,
        eventId,
        sortBy = 'uploadedAt',
        sortOrder = 'desc',
      } = params;

      const skip = (page - 1) * limit;

      // Build where clause - only show matched photos for this client
      const where: any = {
        clientId,
        isMatched: true,
      };

      if (eventId) {
        where.eventId = eventId;
      }

      // Build orderBy clause
      const orderBy: any = {};
      orderBy[sortBy] = sortOrder;

      const [photos, total] = await Promise.all([
        prisma.photo.findMany({
          where,
          skip,
          take: limit,
          orderBy,
          include: {
            event: {
              select: {
                id: true,
                name: true,
                date: true,
                location: true,
              },
            },
            favorites: {
              where: { clientId },
              select: { id: true },
            },
            comments: {
              where: { clientId },
              select: {
                id: true,
                content: true,
                createdAt: true,
              },
              orderBy: { createdAt: 'desc' },
            },
            _count: {
              select: {
                favorites: true,
                comments: true,
              },
            },
          },
        }),
        prisma.photo.count({ where }),
      ]);

      // Transform photos to include favorite status
      const transformedPhotos = photos.map(photo => ({
        ...photo,
        size: photo.size.toString(),
        isFavorited: photo.favorites.length > 0,
        favorites: undefined, // Remove the favorites array, we only need the boolean
      }));

      return {
        photos: transformedPhotos,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching client photos:', error);
      throw new Error('Failed to fetch photos');
    }
  }

  // Toggle photo favorite
  static async togglePhotoFavorite(clientId: string, photoId: string, action: 'add' | 'remove') {
    try {
      // Verify photo belongs to this client
      const photo = await prisma.photo.findFirst({
        where: {
          id: photoId,
          clientId,
          isMatched: true,
        },
      });

      if (!photo) {
        throw new Error('Photo not found');
      }

      if (action === 'add') {
        // Add to favorites (ignore if already exists)
        await prisma.favorite.upsert({
          where: {
            photoId_clientId: {
              photoId,
              clientId,
            },
          },
          update: {},
          create: {
            photoId,
            clientId,
          },
        });

        // Log action
        await prisma.accessLog.create({
          data: {
            clientId,
            ipAddress: '0.0.0.0',
            action: 'favorite_add',
          },
        });

        return { success: true, message: 'Photo added to favorites' };
      } else {
        // Remove from favorites
        await prisma.favorite.deleteMany({
          where: {
            photoId,
            clientId,
          },
        });

        // Log action
        await prisma.accessLog.create({
          data: {
            clientId,
            ipAddress: '0.0.0.0',
            action: 'favorite_remove',
          },
        });

        return { success: true, message: 'Photo removed from favorites' };
      }
    } catch (error) {
      console.error('Error toggling photo favorite:', error);
      throw new Error('Failed to update favorite');
    }
  }

  // Add comment to photo
  static async addPhotoComment(clientId: string, photoId: string, content: string) {
    try {
      // Verify photo belongs to this client
      const photo = await prisma.photo.findFirst({
        where: {
          id: photoId,
          clientId,
          isMatched: true,
        },
      });

      if (!photo) {
        throw new Error('Photo not found');
      }

      const comment = await prisma.comment.create({
        data: {
          photoId,
          clientId,
          content,
        },
      });

      // Log action
      await prisma.accessLog.create({
        data: {
          clientId,
          ipAddress: '0.0.0.0',
          action: 'comment_add',
        },
      });

      return comment;
    } catch (error) {
      console.error('Error adding photo comment:', error);
      throw new Error('Failed to add comment');
    }
  }

  // Get client events
  static async getClientEvents(clientId: string) {
    try {
      // Get events that have photos for this client
      const events = await prisma.event.findMany({
        where: {
          photos: {
            some: {
              clientId,
              isMatched: true,
            },
          },
        },
        include: {
          _count: {
            select: {
              photos: {
                where: {
                  clientId,
                  isMatched: true,
                },
              },
            },
          },
        },
        orderBy: { date: 'desc' },
      });

      return events;
    } catch (error) {
      console.error('Error fetching client events:', error);
      throw new Error('Failed to fetch events');
    }
  }

  // Get client statistics
  static async getClientStats(clientId: string) {
    try {
      const [
        totalPhotos,
        totalFavorites,
        totalComments,
        totalDownloads,
        recentActivity,
      ] = await Promise.all([
        prisma.photo.count({
          where: { clientId, isMatched: true },
        }),
        prisma.favorite.count({
          where: { clientId },
        }),
        prisma.comment.count({
          where: { clientId },
        }),
        prisma.accessLog.count({
          where: { clientId, action: 'download' },
        }),
        prisma.accessLog.findMany({
          where: { clientId },
          orderBy: { createdAt: 'desc' },
          take: 10,
          select: {
            action: true,
            createdAt: true,
          },
        }),
      ]);

      return {
        totalPhotos,
        totalFavorites,
        totalComments,
        totalDownloads,
        recentActivity,
      };
    } catch (error) {
      console.error('Error fetching client stats:', error);
      throw new Error('Failed to fetch client statistics');
    }
  }

  // Download photo (log the action)
  static async downloadPhoto(clientId: string, photoId: string, ipAddress: string = '0.0.0.0') {
    try {
      // Verify photo belongs to this client
      const photo = await prisma.photo.findFirst({
        where: {
          id: photoId,
          clientId,
          isMatched: true,
        },
      });

      if (!photo) {
        throw new Error('Photo not found');
      }

      // Log download action
      await prisma.accessLog.create({
        data: {
          clientId,
          ipAddress,
          action: 'download',
        },
      });

      return { success: true, photo };
    } catch (error) {
      console.error('Error downloading photo:', error);
      throw new Error('Failed to download photo');
    }
  }

  // Get client profile
  static async getClientProfile(clientId: string) {
    try {
      const client = await prisma.client.findUnique({
        where: { id: clientId },
        include: {
          studio: {
            select: {
              name: true,
              businessName: true,
              logo: true,
            },
          },
          _count: {
            select: {
              photos: { where: { isMatched: true } },
              favorites: true,
              comments: true,
            },
          },
        },
      });

      if (!client) {
        throw new Error('Client not found');
      }

      return client;
    } catch (error) {
      console.error('Error fetching client profile:', error);
      throw new Error('Failed to fetch client profile');
    }
  }

  // Update client profile
  static async updateClientProfile(clientId: string, data: {
    name?: string;
    phone?: string;
  }) {
    try {
      const updatedClient = await prisma.client.update({
        where: { id: clientId },
        data,
        include: {
          studio: {
            select: {
              name: true,
              businessName: true,
              logo: true,
            },
          },
          _count: {
            select: {
              photos: { where: { isMatched: true } },
              favorites: true,
              comments: true,
            },
          },
        },
      });

      return updatedClient;
    } catch (error) {
      console.error('Error updating client profile:', error);
      throw new Error('Failed to update profile');
    }
  }

  // Get client access logs
  static async getClientAccessLogs(clientId: string, params: {
    page?: number;
    limit?: number;
    action?: string;
  }) {
    try {
      const {
        page = 1,
        limit = 20,
        action,
      } = params;

      const skip = (page - 1) * limit;

      const where: any = { clientId };
      if (action) {
        where.action = action;
      }

      const [logs, total] = await Promise.all([
        prisma.accessLog.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
        }),
        prisma.accessLog.count({ where }),
      ]);

      return {
        logs,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching access logs:', error);
      throw new Error('Failed to fetch access logs');
    }
  }
}
