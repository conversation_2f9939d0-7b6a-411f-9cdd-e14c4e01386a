import { prisma } from '@/lib/db';
import { verifyPassword, generateToken } from '@/lib/auth';
import { SecurityService, SecurityContext } from '@/lib/security';
import { BotProtectionService } from '@/lib/security/bot-protection';
import { NextRequest } from 'next/server';

export class ClientService {
  // Enhanced authenticate client with advanced security
  static async authenticateClient(
    accessCode: string,
    password: string,
    request: NextRequest,
    behaviorData?: any,
    challengeResponse?: string
  ) {
    try {
      const context = SecurityService.extractSecurityContext(request);

      // Bot protection check
      const botCheck = BotProtectionService.analyzeRequest(request);
      if (!botCheck.success && botCheck.requiresChallenge && !challengeResponse) {
        return {
          success: false,
          message: botCheck.message,
          requiresChallenge: true,
          challenge: botCheck.challenge,
        };
      }

      // Verify challenge if provided
      if (challengeResponse && botCheck.challenge) {
        if (!BotProtectionService.verifyChallenge(botCheck.challenge, challengeResponse)) {
          return { success: false, message: 'Invalid verification' };
        }
      }

      // Rate limiting check
      const rateCheck = await BotProtectionService.checkRateLimit(
        accessCode,
        'login',
        context.ipAddress
      );
      if (!rateCheck.success) {
        return { success: false, message: rateCheck.message };
      }

      // Find client by access link or QR code
      const client = await prisma.client.findFirst({
        where: {
          OR: [
            { accessLink: accessCode },
            { qrCode: accessCode },
          ],
          isActive: true,
        },
        include: {
          studio: {
            select: {
              name: true,
              businessName: true,
              isActive: true,
              isApproved: true,
            },
          },
        },
      });

      if (!client) {
        await SecurityService.recordSecurityAttempt(
          null,
          'login',
          context,
          false,
          'Invalid access code'
        );
        return { success: false, message: 'Invalid access code' };
      }

      // Check if client is blocked
      if (await SecurityService.isClientBlocked(client.id)) {
        return {
          success: false,
          message: 'Account temporarily blocked due to security violations',
          status: 423,
        };
      }

      if (!client.studio.isActive || !client.studio.isApproved) {
        return { success: false, message: 'Studio is not active' };
      }

      // Verify password
      const isValidPassword = await verifyPassword(password, client.password);
      if (!isValidPassword) {
        await SecurityService.recordSecurityAttempt(
          client.id,
          'login',
          context,
          false,
          'Invalid password'
        );
        return { success: false, message: 'Invalid password' };
      }

      // Check device access
      const deviceCheck = await SecurityService.checkDeviceAccess(client.id, context);
      if (!deviceCheck.success) {
        return { success: false, message: deviceCheck.message };
      }

      // Check geo-restrictions
      const geoCheck = await SecurityService.checkGeoRestrictions(client.id, context);
      if (!geoCheck.success) {
        return { success: false, message: geoCheck.message };
      }

      // Create session
      const sessionToken = await SecurityService.createClientSession(client.id, context);

      // Generate JWT token
      const jwtToken = generateToken({
        userId: client.id,
        email: client.email,
        role: 'client',
      });

      // Update last access
      await prisma.client.update({
        where: { id: client.id },
        data: { lastAccess: new Date() },
      });

      // Log successful access
      await prisma.accessLog.create({
        data: {
          clientId: client.id,
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          browserFingerprint: context.browserFingerprint,
          deviceId: context.deviceId,
          location: context.location ? JSON.stringify(context.location) : null,
          action: 'login',
          success: true,
          sessionId: sessionToken,
        },
      });

      await SecurityService.recordSecurityAttempt(client.id, 'login', context, true);

      return {
        success: true,
        token: jwtToken,
        sessionToken,
        client: {
          id: client.id,
          name: client.name,
          email: client.email,
          studioName: client.studio.businessName || client.studio.name,
          requiresFaceVerification: client.requireFaceVerify,
        },
      };
    } catch (error) {
      console.error('Error authenticating client:', error);
      return { success: false, message: 'Authentication failed' };
    }
  }

  // Get client photos with pagination and filters
  static async getClientPhotos(clientId: string, params: {
    page?: number;
    limit?: number;
    eventId?: string;
    sortBy?: 'uploadedAt' | 'filename';
    sortOrder?: 'asc' | 'desc';
  }) {
    try {
      const {
        page = 1,
        limit = 20,
        eventId,
        sortBy = 'uploadedAt',
        sortOrder = 'desc',
      } = params;

      const skip = (page - 1) * limit;

      // Build where clause - only show matched photos for this client
      const where: any = {
        clientId,
        isMatched: true,
      };

      if (eventId) {
        where.eventId = eventId;
      }

      // Build orderBy clause
      const orderBy: any = {};
      orderBy[sortBy] = sortOrder;

      const [photos, total] = await Promise.all([
        prisma.photo.findMany({
          where,
          skip,
          take: limit,
          orderBy,
          include: {
            event: {
              select: {
                id: true,
                name: true,
                date: true,
                location: true,
              },
            },
            favorites: {
              where: { clientId },
              select: { id: true },
            },
            comments: {
              where: { clientId },
              select: {
                id: true,
                content: true,
                createdAt: true,
              },
              orderBy: { createdAt: 'desc' },
            },
            _count: {
              select: {
                favorites: true,
                comments: true,
              },
            },
          },
        }),
        prisma.photo.count({ where }),
      ]);

      // Transform photos to include favorite status
      const transformedPhotos = photos.map(photo => ({
        ...photo,
        size: photo.size.toString(),
        isFavorited: photo.favorites.length > 0,
        favorites: undefined, // Remove the favorites array, we only need the boolean
      }));

      return {
        photos: transformedPhotos,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching client photos:', error);
      throw new Error('Failed to fetch photos');
    }
  }

  // Toggle photo favorite
  static async togglePhotoFavorite(clientId: string, photoId: string, action: 'add' | 'remove') {
    try {
      // Verify photo belongs to this client
      const photo = await prisma.photo.findFirst({
        where: {
          id: photoId,
          clientId,
          isMatched: true,
        },
      });

      if (!photo) {
        throw new Error('Photo not found');
      }

      if (action === 'add') {
        // Add to favorites (ignore if already exists)
        await prisma.favorite.upsert({
          where: {
            photoId_clientId: {
              photoId,
              clientId,
            },
          },
          update: {},
          create: {
            photoId,
            clientId,
          },
        });

        // Log action
        await prisma.accessLog.create({
          data: {
            clientId,
            ipAddress: '0.0.0.0',
            action: 'favorite_add',
          },
        });

        return { success: true, message: 'Photo added to favorites' };
      } else {
        // Remove from favorites
        await prisma.favorite.deleteMany({
          where: {
            photoId,
            clientId,
          },
        });

        // Log action
        await prisma.accessLog.create({
          data: {
            clientId,
            ipAddress: '0.0.0.0',
            action: 'favorite_remove',
          },
        });

        return { success: true, message: 'Photo removed from favorites' };
      }
    } catch (error) {
      console.error('Error toggling photo favorite:', error);
      throw new Error('Failed to update favorite');
    }
  }

  // Add comment to photo
  static async addPhotoComment(clientId: string, photoId: string, content: string) {
    try {
      // Verify photo belongs to this client
      const photo = await prisma.photo.findFirst({
        where: {
          id: photoId,
          clientId,
          isMatched: true,
        },
      });

      if (!photo) {
        throw new Error('Photo not found');
      }

      const comment = await prisma.comment.create({
        data: {
          photoId,
          clientId,
          content,
        },
      });

      // Log action
      await prisma.accessLog.create({
        data: {
          clientId,
          ipAddress: '0.0.0.0',
          action: 'comment_add',
        },
      });

      return comment;
    } catch (error) {
      console.error('Error adding photo comment:', error);
      throw new Error('Failed to add comment');
    }
  }

  // Get client events
  static async getClientEvents(clientId: string) {
    try {
      // Get events that have photos for this client
      const events = await prisma.event.findMany({
        where: {
          photos: {
            some: {
              clientId,
              isMatched: true,
            },
          },
        },
        include: {
          _count: {
            select: {
              photos: {
                where: {
                  clientId,
                  isMatched: true,
                },
              },
            },
          },
        },
        orderBy: { date: 'desc' },
      });

      return events;
    } catch (error) {
      console.error('Error fetching client events:', error);
      throw new Error('Failed to fetch events');
    }
  }

  // Get client statistics
  static async getClientStats(clientId: string) {
    try {
      const [
        totalPhotos,
        totalFavorites,
        totalComments,
        totalDownloads,
        recentActivity,
      ] = await Promise.all([
        prisma.photo.count({
          where: { clientId, isMatched: true },
        }),
        prisma.favorite.count({
          where: { clientId },
        }),
        prisma.comment.count({
          where: { clientId },
        }),
        prisma.accessLog.count({
          where: { clientId, action: 'download' },
        }),
        prisma.accessLog.findMany({
          where: { clientId },
          orderBy: { createdAt: 'desc' },
          take: 10,
          select: {
            action: true,
            createdAt: true,
          },
        }),
      ]);

      return {
        totalPhotos,
        totalFavorites,
        totalComments,
        totalDownloads,
        recentActivity,
      };
    } catch (error) {
      console.error('Error fetching client stats:', error);
      throw new Error('Failed to fetch client statistics');
    }
  }

  // Download photo (log the action)
  static async downloadPhoto(clientId: string, photoId: string, ipAddress: string = '0.0.0.0') {
    try {
      // Verify photo belongs to this client
      const photo = await prisma.photo.findFirst({
        where: {
          id: photoId,
          clientId,
          isMatched: true,
        },
      });

      if (!photo) {
        throw new Error('Photo not found');
      }

      // Log download action
      await prisma.accessLog.create({
        data: {
          clientId,
          ipAddress,
          action: 'download',
        },
      });

      return { success: true, photo };
    } catch (error) {
      console.error('Error downloading photo:', error);
      throw new Error('Failed to download photo');
    }
  }

  // Get client profile
  static async getClientProfile(clientId: string) {
    try {
      const client = await prisma.client.findUnique({
        where: { id: clientId },
        include: {
          studio: {
            select: {
              name: true,
              businessName: true,
              logo: true,
            },
          },
          _count: {
            select: {
              photos: { where: { isMatched: true } },
              favorites: true,
              comments: true,
            },
          },
        },
      });

      if (!client) {
        throw new Error('Client not found');
      }

      return client;
    } catch (error) {
      console.error('Error fetching client profile:', error);
      throw new Error('Failed to fetch client profile');
    }
  }

  // Update client profile
  static async updateClientProfile(clientId: string, data: {
    name?: string;
    phone?: string;
  }) {
    try {
      const updatedClient = await prisma.client.update({
        where: { id: clientId },
        data,
        include: {
          studio: {
            select: {
              name: true,
              businessName: true,
              logo: true,
            },
          },
          _count: {
            select: {
              photos: { where: { isMatched: true } },
              favorites: true,
              comments: true,
            },
          },
        },
      });

      return updatedClient;
    } catch (error) {
      console.error('Error updating client profile:', error);
      throw new Error('Failed to update profile');
    }
  }

  // Get client access logs
  static async getClientAccessLogs(clientId: string, params: {
    page?: number;
    limit?: number;
    action?: string;
  }) {
    try {
      const {
        page = 1,
        limit = 20,
        action,
      } = params;

      const skip = (page - 1) * limit;

      const where: any = { clientId };
      if (action) {
        where.action = action;
      }

      const [logs, total] = await Promise.all([
        prisma.accessLog.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
        }),
        prisma.accessLog.count({ where }),
      ]);

      return {
        logs,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching access logs:', error);
      throw new Error('Failed to fetch access logs');
    }
  }

  // Enhanced face verification with security controls
  static async verifyFace(
    clientId: string,
    faceDescriptor: number[],
    request: NextRequest,
    behaviorData?: any
  ) {
    try {
      const context = SecurityService.extractSecurityContext(request);

      // Check face scan attempts
      const attemptCheck = await SecurityService.checkFaceScanAttempts(clientId, context);
      if (!attemptCheck.success) {
        return attemptCheck;
      }

      // Bot protection for face scanning
      if (behaviorData) {
        const behaviorCheck = BotProtectionService.analyzeBehavior(behaviorData);
        if (!behaviorCheck.success) {
          await SecurityService.recordFailedFaceScan(
            clientId,
            context,
            'Suspicious behavior detected'
          );
          return behaviorCheck;
        }
      }

      // Rate limiting for face scans
      const rateCheck = await BotProtectionService.checkRateLimit(
        clientId,
        'face_scan',
        context.ipAddress
      );
      if (!rateCheck.success) {
        return rateCheck;
      }

      // Get client's stored face descriptors
      const client = await prisma.client.findUnique({
        where: { id: clientId },
        include: {
          faceDescriptors: true,
        },
      });

      if (!client) {
        return { success: false, message: 'Client not found' };
      }

      if (client.faceDescriptors.length === 0) {
        return {
          success: false,
          message: 'No face profile found. Please enroll your face first.',
          requiresEnrollment: true,
        };
      }

      // Compare with stored descriptors
      let bestMatch = null;
      let bestConfidence = 0;

      for (const stored of client.faceDescriptors) {
        const storedDescriptor = stored.descriptor as number[];
        const confidence = this.calculateFaceConfidence(faceDescriptor, storedDescriptor);

        if (confidence > bestConfidence) {
          bestConfidence = confidence;
          bestMatch = stored;
        }
      }

      const threshold = parseFloat(process.env.FACE_MATCH_THRESHOLD || '0.6');
      const isMatch = bestConfidence >= threshold;

      // Log the attempt
      await prisma.accessLog.create({
        data: {
          clientId,
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          browserFingerprint: context.browserFingerprint,
          deviceId: context.deviceId,
          action: 'face_verification',
          success: isMatch,
          failureReason: isMatch ? null : `Low confidence: ${bestConfidence.toFixed(3)}`,
        },
      });

      if (isMatch) {
        // Reset retry count on success
        await SecurityService.resetFaceScanAttempts(clientId, context);

        // Update last access
        await prisma.client.update({
          where: { id: clientId },
          data: { lastAccess: new Date() },
        });

        return {
          success: true,
          confidence: bestConfidence,
          message: 'Face verified successfully',
          attemptsRemaining: attemptCheck.attemptsRemaining,
        };
      } else {
        // Record failed attempt
        await SecurityService.recordFailedFaceScan(
          clientId,
          context,
          `Face match failed - confidence: ${bestConfidence.toFixed(3)}`
        );

        const updatedAttemptCheck = await SecurityService.checkFaceScanAttempts(clientId, context);

        return {
          success: false,
          confidence: bestConfidence,
          message: `Face verification failed. ${updatedAttemptCheck.attemptsRemaining || 0} attempts remaining.`,
          attemptsRemaining: updatedAttemptCheck.attemptsRemaining,
          blocked: updatedAttemptCheck.blocked,
          blockedUntil: updatedAttemptCheck.blockedUntil,
        };
      }
    } catch (error) {
      console.error('Error verifying face:', error);
      return { success: false, message: 'Face verification failed' };
    }
  }

  // Enroll face descriptor for client
  static async enrollFace(
    clientId: string,
    enrollmentData: {
      faceDescriptor: number[];
      boundingBox: { x: number; y: number; width: number; height: number };
      confidence: number;
    },
    request: NextRequest
  ) {
    try {
      const context = SecurityService.extractSecurityContext(request);

      // Validate face descriptor
      if (!Array.isArray(enrollmentData.faceDescriptor) ||
          enrollmentData.faceDescriptor.length !== 128) {
        return { success: false, message: 'Invalid face descriptor format' };
      }

      // Create face descriptor record
      const faceDescriptor = await prisma.faceDescriptor.create({
        data: {
          clientId,
          photoId: 'enrollment', // Special ID for enrollment
          descriptor: enrollmentData.faceDescriptor,
          confidence: enrollmentData.confidence,
          boundingBox: enrollmentData.boundingBox,
        },
      });

      // Log enrollment
      await prisma.accessLog.create({
        data: {
          clientId,
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          browserFingerprint: context.browserFingerprint,
          deviceId: context.deviceId,
          action: 'face_enrollment',
          success: true,
        },
      });

      return {
        success: true,
        descriptorId: faceDescriptor.id,
        message: 'Face enrolled successfully',
      };
    } catch (error) {
      console.error('Error enrolling face:', error);
      return { success: false, message: 'Face enrollment failed' };
    }
  }

  // Calculate face confidence (Euclidean distance)
  private static calculateFaceConfidence(descriptor1: number[], descriptor2: number[]): number {
    if (descriptor1.length !== descriptor2.length) {
      return 0;
    }

    let sum = 0;
    for (let i = 0; i < descriptor1.length; i++) {
      const diff = descriptor1[i] - descriptor2[i];
      sum += diff * diff;
    }

    const distance = Math.sqrt(sum);
    // Convert distance to confidence (lower distance = higher confidence)
    return Math.max(0, 1 - (distance / 2));
  }
}
