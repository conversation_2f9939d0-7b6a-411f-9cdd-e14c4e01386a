import { prisma } from '@/lib/db';
import { Studio, SubscriptionPlan, BillingHistory } from '@/types';
import { hashPassword } from '@/lib/auth';
import crypto from 'crypto';

export class AdminService {
  // Get all studios with pagination and filters
  static async getStudios(params: {
    page?: number;
    limit?: number;
    search?: string;
    status?: 'all' | 'active' | 'inactive' | 'pending';
    sortBy?: 'name' | 'createdAt' | 'storageUsed';
    sortOrder?: 'asc' | 'desc';
  }) {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        status = 'all',
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = params;

      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = {};
      
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { businessName: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (status !== 'all') {
        switch (status) {
          case 'active':
            where.isActive = true;
            where.isApproved = true;
            break;
          case 'inactive':
            where.isActive = false;
            break;
          case 'pending':
            where.isApproved = false;
            break;
        }
      }

      // Build orderBy clause
      const orderBy: any = {};
      orderBy[sortBy] = sortOrder;

      const [studios, total] = await Promise.all([
        prisma.studio.findMany({
          where,
          skip,
          take: limit,
          orderBy,
          include: {
            subscription: {
              include: {
                plan: true,
              },
            },
            _count: {
              select: {
                clients: true,
                photos: true,
              },
            },
          },
        }),
        prisma.studio.count({ where }),
      ]);

      return {
        studios,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching studios:', error);
      throw new Error('Failed to fetch studios');
    }
  }

  // Get studio details by ID
  static async getStudioById(id: string) {
    try {
      const studio = await prisma.studio.findUnique({
        where: { id },
        include: {
          subscription: {
            include: {
              plan: true,
            },
          },
          clients: {
            select: {
              id: true,
              name: true,
              email: true,
              isActive: true,
              createdAt: true,
            },
          },
          photos: {
            select: {
              id: true,
              filename: true,
              size: true,
              uploadedAt: true,
            },
            take: 10,
            orderBy: { uploadedAt: 'desc' },
          },
          billingHistory: {
            take: 5,
            orderBy: { createdAt: 'desc' },
          },
          _count: {
            select: {
              clients: true,
              photos: true,
              events: true,
            },
          },
        },
      });

      if (!studio) {
        throw new Error('Studio not found');
      }

      return studio;
    } catch (error) {
      console.error('Error fetching studio details:', error);
      throw new Error('Failed to fetch studio details');
    }
  }

  // Approve studio
  static async approveStudio(id: string): Promise<{ success: boolean; message: string }> {
    try {
      await prisma.studio.update({
        where: { id },
        data: { isApproved: true, isActive: true },
      });

      // Create notification for studio
      await prisma.notification.create({
        data: {
          studioId: id,
          type: 'system',
          title: 'Account Approved',
          message: 'Your studio account has been approved and is now active.',
        },
      });

      return { success: true, message: 'Studio approved successfully' };
    } catch (error) {
      console.error('Error approving studio:', error);
      return { success: false, message: 'Failed to approve studio' };
    }
  }

  // Suspend studio
  static async suspendStudio(id: string, reason?: string): Promise<{ success: boolean; message: string }> {
    try {
      await prisma.studio.update({
        where: { id },
        data: { isActive: false },
      });

      // Create notification for studio
      await prisma.notification.create({
        data: {
          studioId: id,
          type: 'system',
          title: 'Account Suspended',
          message: reason || 'Your studio account has been suspended. Please contact support.',
        },
      });

      return { success: true, message: 'Studio suspended successfully' };
    } catch (error) {
      console.error('Error suspending studio:', error);
      return { success: false, message: 'Failed to suspend studio' };
    }
  }

  // Get system analytics
  static async getSystemAnalytics(period: number = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - period);

      const [
        totalStudios,
        activeStudios,
        pendingStudios,
        totalClients,
        totalPhotos,
        totalStorage,
        recentRegistrations,
        revenueData,
      ] = await Promise.all([
        prisma.studio.count(),
        prisma.studio.count({ where: { isActive: true, isApproved: true } }),
        prisma.studio.count({ where: { isApproved: false } }),
        prisma.client.count(),
        prisma.photo.count(),
        prisma.studio.aggregate({
          _sum: { storageUsed: true },
        }),
        prisma.studio.findMany({
          where: { createdAt: { gte: startDate } },
          orderBy: { createdAt: 'desc' },
          take: 10,
          select: {
            id: true,
            name: true,
            businessName: true,
            email: true,
            createdAt: true,
            isApproved: true,
          },
        }),
        prisma.billingHistory.aggregate({
          where: {
            createdAt: { gte: startDate },
            status: 'completed',
          },
          _sum: { amount: true },
        }),
      ]);

      // Get daily registration trends
      const studios = await prisma.studio.findMany({
        where: {
          createdAt: { gte: startDate },
        },
        select: {
          createdAt: true,
        },
        orderBy: {
          createdAt: 'asc',
        },
      });

      // Group by date
      const registrationTrends = studios.reduce((acc: any, studio) => {
        const date = studio.createdAt.toISOString().split('T')[0];
        if (!acc[date]) {
          acc[date] = { date, count: 0 };
        }
        acc[date].count += 1;
        return acc;
      }, {});

      return {
        overview: {
          totalStudios,
          activeStudios,
          pendingStudios,
          totalClients,
          totalPhotos,
          totalStorage: totalStorage._sum.storageUsed?.toString() || '0',
          totalRevenue: revenueData._sum.amount?.toString() || '0',
        },
        trends: {
          registrationTrends: Object.values(registrationTrends),
          period,
        },
        recentRegistrations,
      };
    } catch (error) {
      console.error('Error fetching system analytics:', error);
      throw new Error('Failed to fetch system analytics');
    }
  }

  // Get subscription plans
  static async getSubscriptionPlans() {
    try {
      return await prisma.subscriptionPlan.findMany({
        orderBy: { price: 'asc' },
        include: {
          _count: {
            select: {
              subscriptions: true,
            },
          },
        },
      });
    } catch (error) {
      console.error('Error fetching subscription plans:', error);
      throw new Error('Failed to fetch subscription plans');
    }
  }

  // Create subscription plan
  static async createSubscriptionPlan(data: {
    name: string;
    price: number;
    billingCycle: string;
    storageLimit: bigint;
    downloadQuality: string;
    customBranding: boolean;
    watermark: boolean;
    videoSupport: boolean;
  }) {
    try {
      return await prisma.subscriptionPlan.create({
        data,
      });
    } catch (error) {
      console.error('Error creating subscription plan:', error);
      throw new Error('Failed to create subscription plan');
    }
  }

  // Update subscription plan
  static async updateSubscriptionPlan(id: string, data: Partial<SubscriptionPlan>) {
    try {
      return await prisma.subscriptionPlan.update({
        where: { id },
        data,
      });
    } catch (error) {
      console.error('Error updating subscription plan:', error);
      throw new Error('Failed to update subscription plan');
    }
  }

  // Delete subscription plan
  static async deleteSubscriptionPlan(id: string): Promise<{ success: boolean; message: string }> {
    try {
      // Check if plan is in use
      const subscriptionsCount = await prisma.subscription.count({
        where: { planId: id },
      });

      if (subscriptionsCount > 0) {
        return { success: false, message: 'Cannot delete plan that is currently in use' };
      }

      await prisma.subscriptionPlan.delete({
        where: { id },
      });

      return { success: true, message: 'Subscription plan deleted successfully' };
    } catch (error) {
      console.error('Error deleting subscription plan:', error);
      return { success: false, message: 'Failed to delete subscription plan' };
    }
  }

  // Get system settings
  static async getSystemSettings() {
    try {
      const settings = await prisma.systemSettings.findMany();
      
      // Convert to key-value object
      const settingsObj: Record<string, any> = {};
      settings.forEach(setting => {
        let value: any = setting.value;
        
        // Parse value based on type
        switch (setting.type) {
          case 'number':
            value = parseFloat(setting.value);
            break;
          case 'boolean':
            value = setting.value === 'true';
            break;
          case 'json':
            try {
              value = JSON.parse(setting.value);
            } catch {
              value = setting.value;
            }
            break;
          default:
            value = setting.value;
        }
        
        settingsObj[setting.key] = value;
      });

      return settingsObj;
    } catch (error) {
      console.error('Error fetching system settings:', error);
      throw new Error('Failed to fetch system settings');
    }
  }

  // Update system setting
  static async updateSystemSetting(key: string, value: any, type: string) {
    try {
      let stringValue = value;
      
      // Convert value to string based on type
      if (type === 'json') {
        stringValue = JSON.stringify(value);
      } else {
        stringValue = String(value);
      }

      return await prisma.systemSettings.upsert({
        where: { key },
        update: { value: stringValue, type },
        create: { key, value: stringValue, type },
      });
    } catch (error) {
      console.error('Error updating system setting:', error);
      throw new Error('Failed to update system setting');
    }
  }

  // Create new studio (Admin function)
  static async createStudio(data: {
    name: string;
    businessName: string;
    email: string;
    password: string;
    phone?: string;
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    pincode?: string;
    website?: string;
    description?: string;
  }) {
    try {
      // Check if email already exists
      const existingStudio = await prisma.studio.findUnique({
        where: { email: data.email }
      });

      if (existingStudio) {
        return { success: false, message: 'Studio with this email already exists' };
      }

      // Hash password
      const hashedPassword = await hashPassword(data.password);

      // Generate unique studio ID
      const studioId = crypto.randomBytes(8).toString('hex').toUpperCase();

      // Create studio
      const studio = await prisma.studio.create({
        data: {
          name: data.name,
          businessName: data.businessName,
          email: data.email,
          password: hashedPassword,
          phone: data.phone,
          address: data.address,
          city: data.city,
          state: data.state,
          country: data.country || 'India',
          pincode: data.pincode,
          website: data.website,
          description: data.description,
          studioId,
          isActive: true,
          isApproved: true, // Auto-approve when created by admin
        },
        include: {
          _count: {
            select: {
              clients: true,
              photos: true,
            }
          }
        }
      });

      // Remove password from response
      const { password: _, ...studioResponse } = studio;

      return {
        success: true,
        message: 'Studio created successfully',
        studio: studioResponse
      };
    } catch (error) {
      console.error('Error creating studio:', error);
      return { success: false, message: 'Failed to create studio' };
    }
  }
}
