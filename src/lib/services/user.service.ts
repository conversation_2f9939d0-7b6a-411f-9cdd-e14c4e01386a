import { prisma } from '@/lib/db';
import { hashPassword, verifyPassword, generateToken, registerStudio } from '@/lib/auth';
import { User, Admin, Studio, Client } from '@/types';

export class UserService {
  // Get user by ID and role
  static async getUserById(id: string, role: 'admin' | 'studio' | 'client'): Promise<User | null> {
    try {
      let user: any = null;

      switch (role) {
        case 'admin':
          user = await prisma.admin.findUnique({
            where: { id },
          });
          break;
        case 'studio':
          user = await prisma.studio.findUnique({
            where: { id },
            include: {
              subscription: {
                include: {
                  plan: true,
                },
              },
            },
          });
          break;
        case 'client':
          user = await prisma.client.findUnique({
            where: { id },
            include: {
              studio: true,
            },
          });
          break;
        default:
          return null;
      }

      if (!user) return null;

      return {
        ...user,
        role,
      } as User;
    } catch (error) {
      console.error('Error fetching user:', error);
      return null;
    }
  }

  // Get user by email and role
  static async getUserByEmail(email: string, role: 'admin' | 'studio' | 'client'): Promise<User | null> {
    try {
      let user: any = null;

      switch (role) {
        case 'admin':
          user = await prisma.admin.findUnique({
            where: { email },
          });
          break;
        case 'studio':
          user = await prisma.studio.findUnique({
            where: { email },
            include: {
              subscription: {
                include: {
                  plan: true,
                },
              },
            },
          });
          break;
        case 'client':
          // For clients, we need studioId as well since email is not unique across studios
          return null; // Use getClientByEmailAndStudio instead
        default:
          return null;
      }

      if (!user) return null;

      return {
        ...user,
        role,
      } as User;
    } catch (error) {
      console.error('Error fetching user by email:', error);
      return null;
    }
  }

  // Update user profile
  static async updateUserProfile(
    id: string,
    role: 'admin' | 'studio' | 'client',
    data: Partial<User>
  ): Promise<User | null> {
    try {
      let updatedUser: any = null;

      switch (role) {
        case 'admin':
          updatedUser = await prisma.admin.update({
            where: { id },
            data: {
              name: data.name,
              email: data.email,
            },
          });
          break;
        case 'studio':
          updatedUser = await prisma.studio.update({
            where: { id },
            data: {
              name: data.name,
              email: data.email,
              businessName: (data as Studio).businessName,
              phone: (data as Studio).phone,
              address: (data as Studio).address,
            },
            include: {
              subscription: {
                include: {
                  plan: true,
                },
              },
            },
          });
          break;
        case 'client':
          updatedUser = await prisma.client.update({
            where: { id },
            data: {
              name: data.name,
              email: data.email,
              phone: (data as Client).phone,
            },
            include: {
              studio: true,
            },
          });
          break;
        default:
          return null;
      }

      if (!updatedUser) return null;

      return {
        ...updatedUser,
        role,
      } as User;
    } catch (error) {
      console.error('Error updating user profile:', error);
      return null;
    }
  }

  // Change user password
  static async changePassword(
    id: string,
    role: 'admin' | 'studio' | 'client',
    currentPassword: string,
    newPassword: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Get current user
      const user = await this.getUserById(id, role);
      if (!user) {
        return { success: false, message: 'User not found' };
      }

      // Verify current password
      const isValidPassword = await verifyPassword(currentPassword, (user as any).password);
      if (!isValidPassword) {
        return { success: false, message: 'Current password is incorrect' };
      }

      // Hash new password
      const hashedNewPassword = await hashPassword(newPassword);

      // Update password
      switch (role) {
        case 'admin':
          await prisma.admin.update({
            where: { id },
            data: { password: hashedNewPassword },
          });
          break;
        case 'studio':
          await prisma.studio.update({
            where: { id },
            data: { password: hashedNewPassword },
          });
          break;
        case 'client':
          await prisma.client.update({
            where: { id },
            data: { password: hashedNewPassword },
          });
          break;
      }

      return { success: true, message: 'Password updated successfully' };
    } catch (error) {
      console.error('Error changing password:', error);
      return { success: false, message: 'Failed to update password' };
    }
  }

  // Get client by email and studio
  static async getClientByEmailAndStudio(email: string, studioId: string): Promise<Client | null> {
    try {
      const client = await prisma.client.findUnique({
        where: {
          email_studioId: {
            email,
            studioId,
          },
        },
        include: {
          studio: true,
        },
      });

      if (!client) return null;

      return {
        ...client,
        role: 'client' as const,
      } as unknown as Client;
    } catch (error) {
      console.error('Error fetching client:', error);
      return null;
    }
  }

  // Deactivate user account
  static async deactivateUser(
    id: string,
    role: 'admin' | 'studio' | 'client'
  ): Promise<{ success: boolean; message: string }> {
    try {
      switch (role) {
        case 'studio':
          await prisma.studio.update({
            where: { id },
            data: { isActive: false },
          });
          break;
        case 'client':
          await prisma.client.update({
            where: { id },
            data: { isActive: false },
          });
          break;
        default:
          return { success: false, message: 'Cannot deactivate admin accounts' };
      }

      return { success: true, message: 'Account deactivated successfully' };
    } catch (error) {
      console.error('Error deactivating user:', error);
      return { success: false, message: 'Failed to deactivate account' };
    }
  }

  // Reactivate user account
  static async reactivateUser(
    id: string,
    role: 'admin' | 'studio' | 'client'
  ): Promise<{ success: boolean; message: string }> {
    try {
      switch (role) {
        case 'studio':
          await prisma.studio.update({
            where: { id },
            data: { isActive: true },
          });
          break;
        case 'client':
          await prisma.client.update({
            where: { id },
            data: { isActive: true },
          });
          break;
        default:
          return { success: false, message: 'Cannot modify admin accounts' };
      }

      return { success: true, message: 'Account reactivated successfully' };
    } catch (error) {
      console.error('Error reactivating user:', error);
      return { success: false, message: 'Failed to reactivate account' };
    }
  }

  // Delete user account (soft delete by deactivation)
  static async deleteUser(
    id: string,
    role: 'admin' | 'studio' | 'client'
  ): Promise<{ success: boolean; message: string }> {
    try {
      // For now, we'll use soft delete (deactivation)
      // In the future, we might implement hard delete with proper cleanup
      return await this.deactivateUser(id, role);
    } catch (error) {
      console.error('Error deleting user:', error);
      return { success: false, message: 'Failed to delete account' };
    }
  }

  // Refresh token
  static async refreshToken(
    userId: string,
    role: 'admin' | 'studio' | 'client'
  ): Promise<{ success: boolean; token?: string; user?: User; message?: string; status?: number }> {
    try {
      const user = await this.getUserById(userId, role);
      if (!user) {
        return { success: false, message: 'User not found', status: 404 };
      }

      // Check if user is active (type-safe checks)
      if (role === 'studio') {
        const studioUser = user as any;
        if (!studioUser.isActive || !studioUser.isApproved) {
          return { success: false, message: 'Studio account is not active or approved', status: 403 };
        }
      }

      if (role === 'client') {
        const clientUser = user as any;
        if (!clientUser.isActive) {
          return { success: false, message: 'Client account is not active', status: 403 };
        }
      }

      const token = generateToken({
        userId: user.id,
        email: user.email,
        role,
      });

      return {
        success: true,
        token,
        user: { ...user, role } as User,
      };
    } catch (error) {
      console.error('Error refreshing token:', error);
      return { success: false, message: 'Failed to refresh token', status: 500 };
    }
  }

  // Initiate password reset
  static async initiatePasswordReset(
    email: string,
    userType: 'admin' | 'studio'
  ): Promise<{ success: boolean; message: string; status?: number }> {
    try {
      const user = await this.getUserByEmail(email, userType);
      if (!user) {
        // Don't reveal if user exists or not for security
        return { success: true, message: 'If the email exists, password reset instructions have been sent' };
      }

      // In a real implementation, you would:
      // 1. Generate a secure reset token
      // 2. Store it in the database with expiration
      // 3. Send email with reset link

      // For now, just return success
      return { success: true, message: 'Password reset instructions have been sent' };
    } catch (error) {
      console.error('Error initiating password reset:', error);
      return { success: false, message: 'Failed to initiate password reset', status: 500 };
    }
  }

  // Register studio
  static async registerStudio(
    data: {
      name: string;
      email: string;
      password: string;
      businessName?: string;
      phone?: string;
    }
  ): Promise<{ success: boolean; message: string; user?: any; status?: number }> {
    try {
      const result = await registerStudio(data);
      return result;
    } catch (error) {
      console.error('Error registering studio:', error);
      return { success: false, message: 'Failed to register studio', status: 500 };
    }
  }

  // Log user activity
  static async logUserActivity(
    userId: string,
    role: 'admin' | 'studio' | 'client',
    action: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    try {
      // In a real implementation, you would log this to an activity log table
      console.log(`User activity: ${userId} (${role}) performed ${action} from ${ipAddress || 'unknown'} using ${userAgent || 'unknown'}`);
    } catch (error) {
      console.error('Error logging user activity:', error);
    }
  }
}
