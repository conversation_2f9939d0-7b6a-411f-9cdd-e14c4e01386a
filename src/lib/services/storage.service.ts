import { prisma } from '@/lib/db';
import { writeFile, unlink, mkdir, stat } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

export class StorageService {
  private static readonly UPLOAD_BASE_PATH = join(process.cwd(), 'public', 'uploads');

  // Get storage statistics for a studio
  static async getStorageStats(studioId: string) {
    try {
      const [
        studio,
        totalFiles,
        totalSize,
        filesByType,
        recentFiles,
      ] = await Promise.all([
        prisma.studio.findUnique({
          where: { id: studioId },
          include: {
            subscription: {
              include: {
                plan: {
                  select: {
                    storageLimit: true,
                  },
                },
              },
            },
          },
        }),
        prisma.photo.count({ where: { studioId } }),
        prisma.photo.aggregate({
          where: { studioId },
          _sum: { size: true },
        }),
        this.getFilesByType(studioId),
        prisma.photo.findMany({
          where: { studioId },
          orderBy: { uploadedAt: 'desc' },
          take: 10,
          select: {
            id: true,
            filename: true,
            originalName: true,
            size: true,
            mimeType: true,
            uploadedAt: true,
          },
        }),
      ]);

      if (!studio) {
        throw new Error('Studio not found');
      }

      const storageLimit = studio.subscription?.plan?.storageLimit || BigInt(0);
      const storageUsed = totalSize._sum.size || BigInt(0);
      const storagePercentage = storageLimit > 0 
        ? Number((storageUsed * BigInt(100)) / storageLimit)
        : 0;

      return {
        totalFiles,
        totalSize: storageUsed.toString(),
        storageLimit: storageLimit.toString(),
        storagePercentage: Math.round(storagePercentage * 100) / 100,
        availableStorage: (storageLimit - storageUsed).toString(),
        filesByType,
        recentFiles: recentFiles.map(file => ({
          ...file,
          size: file.size.toString(),
        })),
      };
    } catch (error) {
      console.error('Error fetching storage stats:', error);
      throw new Error('Failed to fetch storage statistics');
    }
  }

  // Clean up orphaned files (files in database but not on disk, or vice versa)
  static async cleanupOrphanedFiles(studioId: string): Promise<{ success: boolean; message: string; cleanedCount: number }> {
    try {
      const studioPath = join(this.UPLOAD_BASE_PATH, studioId);
      
      if (!existsSync(studioPath)) {
        return { success: true, message: 'No files to clean up', cleanedCount: 0 };
      }

      // Get all photos from database
      const dbPhotos = await prisma.photo.findMany({
        where: { studioId },
        select: {
          id: true,
          filename: true,
          path: true,
        },
      });

      let cleanedCount = 0;

      // Check for database records without corresponding files
      for (const photo of dbPhotos) {
        const fullPath = join(process.cwd(), 'public', photo.path);
        
        if (!existsSync(fullPath)) {
          // File doesn't exist, remove from database
          await prisma.photo.delete({
            where: { id: photo.id },
          });
          cleanedCount++;
        }
      }

      // TODO: Check for files without database records
      // This would require reading the directory and comparing with database

      // Update studio storage usage
      const updatedSize = await prisma.photo.aggregate({
        where: { studioId },
        _sum: { size: true },
      });

      await prisma.studio.update({
        where: { id: studioId },
        data: {
          storageUsed: updatedSize._sum.size || BigInt(0),
        },
      });

      return {
        success: true,
        message: `Cleaned up ${cleanedCount} orphaned records`,
        cleanedCount,
      };
    } catch (error) {
      console.error('Error cleaning up orphaned files:', error);
      return { success: false, message: 'Failed to clean up files', cleanedCount: 0 };
    }
  }

  // Get file by ID and verify access
  static async getFile(fileId: string, studioId?: string, clientId?: string) {
    try {
      const where: any = { id: fileId };
      
      if (studioId) {
        where.studioId = studioId;
      }
      
      if (clientId) {
        where.clientId = clientId;
        where.isMatched = true; // Clients can only access matched photos
      }

      const photo = await prisma.photo.findFirst({
        where,
        include: {
          studio: {
            select: {
              name: true,
            },
          },
          client: {
            select: {
              name: true,
            },
          },
        },
      });

      if (!photo) {
        throw new Error('File not found or access denied');
      }

      const fullPath = join(process.cwd(), 'public', photo.path);
      
      if (!existsSync(fullPath)) {
        throw new Error('File not found on disk');
      }

      return {
        ...photo,
        size: photo.size.toString(),
        fullPath,
      };
    } catch (error) {
      console.error('Error getting file:', error);
      throw new Error('Failed to get file');
    }
  }

  // Save uploaded file
  static async saveFile(
    file: File,
    studioId: string,
    options: {
      eventId?: string;
      customPath?: string;
    } = {}
  ) {
    try {
      // Check studio storage limit
      const studio = await prisma.studio.findUnique({
        where: { id: studioId },
        include: {
          subscription: {
            include: {
              plan: true,
            },
          },
        },
      });

      if (!studio) {
        throw new Error('Studio not found');
      }

      const storageLimit = studio.subscription?.plan?.storageLimit || BigInt(0);
      const currentStorageUsed = studio.storageUsed;

      if (storageLimit > 0 && currentStorageUsed + BigInt(file.size) > storageLimit) {
        throw new Error('Storage limit exceeded');
      }

      // Generate file path
      const timestamp = Date.now();
      const filename = `${timestamp}_${file.name}`;
      const studioPath = join(this.UPLOAD_BASE_PATH, studioId);
      const fullPath = join(studioPath, filename);
      const relativePath = join('uploads', studioId, filename);

      // Ensure directory exists
      await mkdir(studioPath, { recursive: true });

      // Save file to disk
      const bytes = await file.arrayBuffer();
      const buffer = Buffer.from(bytes);
      await writeFile(fullPath, buffer);

      // Save to database
      const photo = await prisma.photo.create({
        data: {
          filename,
          originalName: file.name,
          path: relativePath,
          size: BigInt(file.size),
          mimeType: file.type,
          studioId,
          eventId: options.eventId,
        },
      });

      // Update studio storage usage
      await prisma.studio.update({
        where: { id: studioId },
        data: {
          storageUsed: {
            increment: BigInt(file.size),
          },
        },
      });

      return {
        ...photo,
        size: photo.size.toString(),
      };
    } catch (error) {
      console.error('Error saving file:', error);
      throw new Error('Failed to save file');
    }
  }

  // Delete file
  static async deleteFile(fileId: string, studioId: string): Promise<{ success: boolean; message: string }> {
    try {
      const photo = await prisma.photo.findFirst({
        where: { id: fileId, studioId },
      });

      if (!photo) {
        return { success: false, message: 'File not found' };
      }

      const fullPath = join(process.cwd(), 'public', photo.path);

      // Delete from database first
      await prisma.photo.delete({
        where: { id: fileId },
      });

      // Update studio storage usage
      await prisma.studio.update({
        where: { id: studioId },
        data: {
          storageUsed: {
            decrement: photo.size,
          },
        },
      });

      // Delete physical file
      try {
        if (existsSync(fullPath)) {
          await unlink(fullPath);
        }
      } catch (fileError) {
        console.warn('Failed to delete physical file:', fileError);
        // Don't throw error here as database cleanup was successful
      }

      return { success: true, message: 'File deleted successfully' };
    } catch (error) {
      console.error('Error deleting file:', error);
      return { success: false, message: 'Failed to delete file' };
    }
  }

  // Bulk delete files
  static async bulkDeleteFiles(fileIds: string[], studioId: string): Promise<{ success: boolean; message: string; deletedCount: number }> {
    try {
      // Get all photos to be deleted
      const photos = await prisma.photo.findMany({
        where: {
          id: { in: fileIds },
          studioId,
        },
        select: {
          id: true,
          path: true,
          size: true,
        },
      });

      if (photos.length === 0) {
        return { success: false, message: 'No files found', deletedCount: 0 };
      }

      // Calculate total size
      const totalSize = photos.reduce((sum, photo) => sum + photo.size, BigInt(0));

      // Delete from database
      const deleteResult = await prisma.photo.deleteMany({
        where: {
          id: { in: photos.map(p => p.id) },
          studioId,
        },
      });

      // Update studio storage usage
      await prisma.studio.update({
        where: { id: studioId },
        data: {
          storageUsed: {
            decrement: totalSize,
          },
        },
      });

      // Delete physical files
      let physicalDeleteCount = 0;
      for (const photo of photos) {
        try {
          const fullPath = join(process.cwd(), 'public', photo.path);
          if (existsSync(fullPath)) {
            await unlink(fullPath);
            physicalDeleteCount++;
          }
        } catch (fileError) {
          console.warn(`Failed to delete physical file ${photo.path}:`, fileError);
        }
      }

      return {
        success: true,
        message: `${deleteResult.count} files deleted from database, ${physicalDeleteCount} physical files deleted`,
        deletedCount: deleteResult.count,
      };
    } catch (error) {
      console.error('Error bulk deleting files:', error);
      return { success: false, message: 'Failed to delete files', deletedCount: 0 };
    }
  }

  // Get storage usage by date range
  static async getStorageUsageByDateRange(studioId: string, startDate: Date, endDate: Date) {
    try {
      const photos = await prisma.photo.findMany({
        where: {
          studioId,
          uploadedAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        select: {
          uploadedAt: true,
          size: true,
        },
        orderBy: {
          uploadedAt: 'asc',
        },
      });

      // Group by date
      const usageByDate = photos.reduce((acc: any, photo) => {
        const date = photo.uploadedAt.toISOString().split('T')[0];
        if (!acc[date]) {
          acc[date] = { date, file_count: 0, total_size: BigInt(0) };
        }
        acc[date].file_count += 1;
        acc[date].total_size += photo.size;
        return acc;
      }, {});

      return Object.values(usageByDate);
    } catch (error) {
      console.error('Error fetching storage usage by date range:', error);
      throw new Error('Failed to fetch storage usage data');
    }
  }

  // Get largest files for a studio
  static async getLargestFiles(studioId: string, limit: number = 10) {
    try {
      const photos = await prisma.photo.findMany({
        where: { studioId },
        orderBy: { size: 'desc' },
        take: limit,
        select: {
          id: true,
          filename: true,
          originalName: true,
          size: true,
          mimeType: true,
          uploadedAt: true,
          event: {
            select: {
              name: true,
            },
          },
        },
      });

      return photos.map(photo => ({
        ...photo,
        size: photo.size.toString(),
      }));
    } catch (error) {
      console.error('Error fetching largest files:', error);
      throw new Error('Failed to fetch largest files');
    }
  }

  // Helper method to get files by type
  private static async getFilesByType(studioId: string) {
    try {
      const result = await prisma.photo.findMany({
        where: { studioId },
        select: {
          mimeType: true,
          size: true,
        },
      });

      // Group by mime type
      const filesByType = result.reduce((acc: any, photo) => {
        const mimeType = photo.mimeType;
        if (!acc[mimeType]) {
          acc[mimeType] = { mime_type: mimeType, count: 0, total_size: BigInt(0) };
        }
        acc[mimeType].count += 1;
        acc[mimeType].total_size += photo.size;
        return acc;
      }, {});

      return Object.values(filesByType).sort((a: any, b: any) => b.count - a.count);
    } catch (error) {
      console.error('Error fetching files by type:', error);
      return [];
    }
  }

  // Check if file exists on disk
  static async checkFileExists(filePath: string): Promise<boolean> {
    try {
      const fullPath = join(process.cwd(), 'public', filePath);
      await stat(fullPath);
      return true;
    } catch {
      return false;
    }
  }

  // Get admin storage overview
  static async getAdminStorageOverview() {
    try {
      const [
        totalFiles,
        totalSize,
        storageByStudio,
        largestStudios,
      ] = await Promise.all([
        prisma.photo.count(),
        prisma.photo.aggregate({
          _sum: { size: true },
        }),
        this.getStorageByStudio(),
        this.getLargestStudios(10),
      ]);

      return {
        totalFiles,
        totalSize: totalSize._sum.size?.toString() || '0',
        storageByStudio,
        largestStudios,
      };
    } catch (error) {
      console.error('Error fetching admin storage overview:', error);
      throw new Error('Failed to fetch storage overview');
    }
  }

  private static async getStorageByStudio() {
    try {
      const studios = await prisma.studio.findMany({
        select: {
          id: true,
          name: true,
          businessName: true,
          _count: {
            select: {
              photos: true,
            },
          },
          photos: {
            select: {
              size: true,
            },
          },
          subscription: {
            select: {
              plan: {
                select: {
                  storageLimit: true,
                },
              },
            },
          },
        },
        take: 20,
      });

      const result = studios.map(studio => ({
        studio_name: studio.name,
        business_name: studio.businessName,
        file_count: studio._count.photos,
        total_size: studio.photos.reduce((sum, photo) => sum + photo.size, BigInt(0)),
        storage_limit: studio.subscription?.plan?.storageLimit || BigInt(0),
      }));

      // Sort by total size descending
      return result.sort((a, b) => Number(b.total_size - a.total_size));
    } catch (error) {
      console.error('Error fetching storage by studio:', error);
      return [];
    }
  }

  private static async getLargestStudios(limit: number) {
    try {
      return await prisma.studio.findMany({
        take: limit,
        orderBy: {
          storageUsed: 'desc',
        },
        select: {
          id: true,
          name: true,
          businessName: true,
          storageUsed: true,
          _count: {
            select: {
              photos: true,
            },
          },
          subscription: {
            select: {
              plan: {
                select: {
                  name: true,
                  storageLimit: true,
                },
              },
            },
          },
        },
      });
    } catch (error) {
      console.error('Error fetching largest studios:', error);
      return [];
    }
  }
}
