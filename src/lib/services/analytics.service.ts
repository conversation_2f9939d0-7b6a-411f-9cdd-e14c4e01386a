import { prisma } from '@/lib/db';

export class AnalyticsService {
  // Get comprehensive analytics for admin dashboard
  static async getAdminAnalytics(period: number = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - period);

      const [
        totalStudios,
        activeStudios,
        totalClients,
        totalPhotos,
        totalRevenue,
        storageUsage,
        registrationTrends,
        revenueTrends,
        topStudios,
        systemHealth,
      ] = await Promise.all([
        prisma.studio.count(),
        prisma.studio.count({ where: { isActive: true, isApproved: true } }),
        prisma.client.count(),
        prisma.photo.count(),
        prisma.billingHistory.aggregate({
          where: {
            status: 'completed',
            createdAt: { gte: startDate },
          },
          _sum: { amount: true },
        }),
        prisma.studio.aggregate({
          _sum: { storageUsed: true },
        }),
        this.getRegistrationTrends(startDate),
        this.getRevenueTrends(startDate),
        this.getTopStudios(10),
        this.getSystemHealth(),
      ]);

      return {
        overview: {
          totalStudios,
          activeStudios,
          pendingStudios: totalStudios - activeStudios,
          totalClients,
          totalPhotos,
          totalRevenue: totalRevenue._sum.amount?.toString() || '0',
          totalStorage: storageUsage._sum.storageUsed?.toString() || '0',
        },
        trends: {
          registrationTrends,
          revenueTrends,
          period,
        },
        topStudios,
        systemHealth,
      };
    } catch (error) {
      console.error('Error fetching admin analytics:', error);
      throw new Error('Failed to fetch admin analytics');
    }
  }

  // Get studio-specific analytics
  static async getStudioAnalytics(studioId: string, period: number = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - period);

      const [
        totalClients,
        activeClients,
        totalPhotos,
        matchedPhotos,
        totalDownloads,
        totalFavorites,
        uploadTrends,
        clientEngagement,
        topClients,
        eventStats,
      ] = await Promise.all([
        prisma.client.count({ where: { studioId } }),
        prisma.client.count({ where: { studioId, isActive: true } }),
        prisma.photo.count({ where: { studioId } }),
        prisma.photo.count({ where: { studioId, isMatched: true } }),
        prisma.accessLog.count({
          where: {
            action: 'download',
            createdAt: { gte: startDate },
            client: { studioId },
          },
        }),
        prisma.favorite.count({
          where: {
            client: { studioId },
          },
        }),
        this.getUploadTrends(studioId, startDate),
        this.getClientEngagement(studioId, startDate),
        this.getTopClients(studioId, 10),
        this.getEventStats(studioId),
      ]);

      const engagementRate = totalClients > 0 ? (activeClients / totalClients) * 100 : 0;
      const matchingRate = totalPhotos > 0 ? (matchedPhotos / totalPhotos) * 100 : 0;
      const favoriteRate = matchedPhotos > 0 ? (totalFavorites / matchedPhotos) * 100 : 0;

      return {
        overview: {
          totalClients,
          activeClients,
          totalPhotos,
          matchedPhotos,
          unmatchedPhotos: totalPhotos - matchedPhotos,
          totalDownloads,
          totalFavorites,
        },
        metrics: {
          engagementRate: Math.round(engagementRate * 100) / 100,
          matchingRate: Math.round(matchingRate * 100) / 100,
          favoriteRate: Math.round(favoriteRate * 100) / 100,
          avgPhotosPerClient: totalClients > 0 ? Math.round(matchedPhotos / totalClients) : 0,
        },
        trends: {
          uploadTrends,
          clientEngagement,
          period,
        },
        topClients,
        eventStats,
      };
    } catch (error) {
      console.error('Error fetching studio analytics:', error);
      throw new Error('Failed to fetch studio analytics');
    }
  }

  // Get client-specific analytics
  static async getClientAnalytics(clientId: string, period: number = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - period);

      const [
        totalPhotos,
        totalFavorites,
        totalComments,
        totalDownloads,
        activityTrends,
        eventBreakdown,
      ] = await Promise.all([
        prisma.photo.count({
          where: { clientId, isMatched: true },
        }),
        prisma.favorite.count({
          where: { clientId },
        }),
        prisma.comment.count({
          where: { clientId },
        }),
        prisma.accessLog.count({
          where: { clientId, action: 'download' },
        }),
        this.getClientActivityTrends(clientId, startDate),
        this.getClientEventBreakdown(clientId),
      ]);

      return {
        overview: {
          totalPhotos,
          totalFavorites,
          totalComments,
          totalDownloads,
        },
        trends: {
          activityTrends,
          period,
        },
        eventBreakdown,
      };
    } catch (error) {
      console.error('Error fetching client analytics:', error);
      throw new Error('Failed to fetch client analytics');
    }
  }

  // Helper methods for specific analytics

  private static async getRegistrationTrends(startDate: Date) {
    try {
      const studios = await prisma.studio.findMany({
        where: {
          createdAt: { gte: startDate },
        },
        select: {
          createdAt: true,
        },
        orderBy: {
          createdAt: 'asc',
        },
      });

      // Group by date
      const trends = studios.reduce((acc: any, studio) => {
        const date = studio.createdAt.toISOString().split('T')[0];
        if (!acc[date]) {
          acc[date] = { date, count: 0 };
        }
        acc[date].count += 1;
        return acc;
      }, {});

      return Object.values(trends);
    } catch (error) {
      console.error('Error fetching registration trends:', error);
      return [];
    }
  }

  private static async getRevenueTrends(startDate: Date) {
    try {
      const billingHistory = await prisma.billingHistory.findMany({
        where: {
          createdAt: { gte: startDate },
          status: 'completed',
        },
        select: {
          createdAt: true,
          amount: true,
        },
        orderBy: {
          createdAt: 'asc',
        },
      });

      // Group by date
      const trends = billingHistory.reduce((acc: any, record) => {
        const date = record.createdAt.toISOString().split('T')[0];
        if (!acc[date]) {
          acc[date] = { date, revenue: 0 };
        }
        acc[date].revenue += Number(record.amount);
        return acc;
      }, {});

      return Object.values(trends);
    } catch (error) {
      console.error('Error fetching revenue trends:', error);
      return [];
    }
  }

  private static async getTopStudios(limit: number) {
    try {
      return await prisma.studio.findMany({
        take: limit,
        orderBy: {
          clients: {
            _count: 'desc',
          },
        },
        include: {
          _count: {
            select: {
              clients: true,
              photos: true,
            },
          },
          subscription: {
            include: {
              plan: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
      });
    } catch (error) {
      console.error('Error fetching top studios:', error);
      return [];
    }
  }

  private static async getSystemHealth() {
    try {
      const [
        totalStorage,
        activeConnections,
        errorRate,
        avgResponseTime,
      ] = await Promise.all([
        prisma.studio.aggregate({
          _sum: { storageUsed: true },
        }),
        prisma.client.count({
          where: {
            lastAccess: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
            },
          },
        }),
        // Placeholder for error rate calculation
        Promise.resolve(0.5),
        // Placeholder for average response time
        Promise.resolve(150),
      ]);

      return {
        totalStorage: totalStorage._sum.storageUsed?.toString() || '0',
        activeConnections,
        errorRate,
        avgResponseTime,
        status: 'healthy', // This would be calculated based on various metrics
      };
    } catch (error) {
      console.error('Error fetching system health:', error);
      return {
        totalStorage: '0',
        activeConnections: 0,
        errorRate: 0,
        avgResponseTime: 0,
        status: 'unknown',
      };
    }
  }

  private static async getUploadTrends(studioId: string, startDate: Date) {
    try {
      const photos = await prisma.photo.findMany({
        where: {
          studioId,
          uploadedAt: { gte: startDate },
        },
        select: {
          uploadedAt: true,
          size: true,
        },
        orderBy: {
          uploadedAt: 'asc',
        },
      });

      // Group by date
      const trends = photos.reduce((acc: any, photo) => {
        const date = photo.uploadedAt.toISOString().split('T')[0];
        if (!acc[date]) {
          acc[date] = { date, count: 0, total_size: BigInt(0) };
        }
        acc[date].count += 1;
        acc[date].total_size += photo.size;
        return acc;
      }, {});

      return Object.values(trends);
    } catch (error) {
      console.error('Error fetching upload trends:', error);
      return [];
    }
  }

  private static async getClientEngagement(studioId: string, startDate: Date) {
    try {
      return await prisma.client.findMany({
        where: { studioId },
        select: {
          id: true,
          name: true,
          _count: {
            select: {
              photos: { where: { isMatched: true } },
              favorites: true,
              accessLogs: { where: { createdAt: { gte: startDate } } },
            },
          },
        },
        orderBy: {
          accessLogs: {
            _count: 'desc',
          },
        },
        take: 10,
      });
    } catch (error) {
      console.error('Error fetching client engagement:', error);
      return [];
    }
  }

  private static async getTopClients(studioId: string, limit: number) {
    try {
      return await prisma.client.findMany({
        where: { studioId },
        take: limit,
        orderBy: {
          photos: {
            _count: 'desc',
          },
        },
        include: {
          _count: {
            select: {
              photos: { where: { isMatched: true } },
              favorites: true,
              comments: true,
              accessLogs: true,
            },
          },
        },
      });
    } catch (error) {
      console.error('Error fetching top clients:', error);
      return [];
    }
  }

  private static async getEventStats(studioId: string) {
    try {
      return await prisma.event.findMany({
        where: { studioId },
        include: {
          _count: {
            select: {
              photos: true,
            },
          },
        },
        orderBy: {
          photos: {
            _count: 'desc',
          },
        },
        take: 10,
      });
    } catch (error) {
      console.error('Error fetching event stats:', error);
      return [];
    }
  }

  private static async getClientActivityTrends(clientId: string, startDate: Date) {
    try {
      const accessLogs = await prisma.accessLog.findMany({
        where: {
          clientId,
          createdAt: { gte: startDate },
        },
        select: {
          createdAt: true,
          action: true,
        },
        orderBy: {
          createdAt: 'asc',
        },
      });

      // Group by date and action
      const trends = accessLogs.reduce((acc: any, log) => {
        const date = log.createdAt.toISOString().split('T')[0];
        const key = `${date}-${log.action}`;
        if (!acc[key]) {
          acc[key] = { date, action: log.action, count: 0 };
        }
        acc[key].count += 1;
        return acc;
      }, {});

      return Object.values(trends);
    } catch (error) {
      console.error('Error fetching client activity trends:', error);
      return [];
    }
  }

  private static async getClientEventBreakdown(clientId: string) {
    try {
      return await prisma.event.findMany({
        where: {
          photos: {
            some: {
              clientId,
              isMatched: true,
            },
          },
        },
        include: {
          _count: {
            select: {
              photos: {
                where: {
                  clientId,
                  isMatched: true,
                },
              },
            },
          },
        },
        orderBy: {
          date: 'desc',
        },
      });
    } catch (error) {
      console.error('Error fetching client event breakdown:', error);
      return [];
    }
  }
}
