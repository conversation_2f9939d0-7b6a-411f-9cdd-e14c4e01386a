import { prisma } from '@/lib/db';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { generateUniqueFilename, isImageFile } from '@/lib/utils';

export class PhotoService {
  // Upload photos for a studio
  static async uploadPhotos(studioId: string, files: File[], eventId?: string) {
    try {
      const uploadResults = [];
      const uploadDir = join(process.cwd(), 'public', 'uploads', studioId);
      
      // Ensure upload directory exists
      await mkdir(uploadDir, { recursive: true });

      // Check studio storage limit
      const studio = await prisma.studio.findUnique({
        where: { id: studioId },
        include: {
          subscription: {
            include: {
              plan: true,
            },
          },
        },
      });

      if (!studio) {
        throw new Error('Studio not found');
      }

      const storageLimit = studio.subscription?.plan?.storageLimit || BigInt(0);
      let currentStorageUsed = studio.storageUsed;

      for (const file of files) {
        try {
          // Validate file
          if (!isImageFile(file.name)) {
            uploadResults.push({
              filename: file.name,
              success: false,
              error: 'Invalid file type',
            });
            continue;
          }

          // Check storage limit
          if (storageLimit > 0 && currentStorageUsed + BigInt(file.size) > storageLimit) {
            uploadResults.push({
              filename: file.name,
              success: false,
              error: 'Storage limit exceeded',
            });
            continue;
          }

          // Generate unique filename
          const filename = generateUniqueFilename(file.name);
          const filepath = join(uploadDir, filename);
          const relativePath = join('uploads', studioId, filename);

          // Save file
          const bytes = await file.arrayBuffer();
          const buffer = Buffer.from(bytes);
          await writeFile(filepath, buffer);

          // Save to database
          const photo = await prisma.photo.create({
            data: {
              filename,
              originalName: file.name,
              path: relativePath,
              size: BigInt(file.size),
              mimeType: file.type,
              studioId,
              eventId,
            },
          });

          // Update studio storage usage
          currentStorageUsed += BigInt(file.size);

          uploadResults.push({
            filename: file.name,
            success: true,
            photoId: photo.id,
            size: file.size,
          });
        } catch (error) {
          console.error(`Error uploading file ${file.name}:`, error);
          uploadResults.push({
            filename: file.name,
            success: false,
            error: 'Upload failed',
          });
        }
      }

      // Update studio storage usage
      await prisma.studio.update({
        where: { id: studioId },
        data: { storageUsed: currentStorageUsed },
      });

      return {
        success: true,
        results: uploadResults,
        totalUploaded: uploadResults.filter(r => r.success).length,
        totalFailed: uploadResults.filter(r => !r.success).length,
      };
    } catch (error) {
      console.error('Error uploading photos:', error);
      throw new Error('Failed to upload photos');
    }
  }

  // Get photos for a studio with pagination and filters
  static async getStudioPhotos(studioId: string, params: {
    page?: number;
    limit?: number;
    eventId?: string;
    isMatched?: boolean;
    isProcessed?: boolean;
    search?: string;
    sortBy?: 'uploadedAt' | 'filename' | 'size';
    sortOrder?: 'asc' | 'desc';
  }) {
    try {
      const {
        page = 1,
        limit = 20,
        eventId,
        isMatched,
        isProcessed,
        search,
        sortBy = 'uploadedAt',
        sortOrder = 'desc',
      } = params;

      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = { studioId };
      
      if (eventId) {
        where.eventId = eventId;
      }
      
      if (typeof isMatched === 'boolean') {
        where.isMatched = isMatched;
      }
      
      if (typeof isProcessed === 'boolean') {
        where.isProcessed = isProcessed;
      }
      
      if (search) {
        where.OR = [
          { filename: { contains: search, mode: 'insensitive' } },
          { originalName: { contains: search, mode: 'insensitive' } },
        ];
      }

      // Build orderBy clause
      const orderBy: any = {};
      orderBy[sortBy] = sortOrder;

      const [photos, total] = await Promise.all([
        prisma.photo.findMany({
          where,
          skip,
          take: limit,
          orderBy,
          include: {
            client: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            event: {
              select: {
                id: true,
                name: true,
                date: true,
              },
            },
            _count: {
              select: {
                favorites: true,
                comments: true,
              },
            },
          },
        }),
        prisma.photo.count({ where }),
      ]);

      // Transform BigInt values to strings for JSON serialization
      const transformedPhotos = photos.map(photo => ({
        ...photo,
        size: photo.size.toString(),
      }));

      return {
        photos: transformedPhotos,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching studio photos:', error);
      throw new Error('Failed to fetch photos');
    }
  }

  // Get photo details by ID
  static async getPhotoById(photoId: string, studioId?: string) {
    try {
      const where: any = { id: photoId };
      if (studioId) {
        where.studioId = studioId;
      }

      const photo = await prisma.photo.findUnique({
        where,
        include: {
          client: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          event: {
            select: {
              id: true,
              name: true,
              date: true,
              location: true,
            },
          },
          studio: {
            select: {
              id: true,
              name: true,
              businessName: true,
            },
          },
          faceDescriptors: true,
          favorites: {
            include: {
              client: {
                select: {
                  name: true,
                },
              },
            },
          },
          comments: {
            include: {
              client: {
                select: {
                  name: true,
                },
              },
            },
            orderBy: { createdAt: 'desc' },
          },
        },
      });

      if (!photo) {
        throw new Error('Photo not found');
      }

      return {
        ...photo,
        size: photo.size.toString(),
      };
    } catch (error) {
      console.error('Error fetching photo details:', error);
      throw new Error('Failed to fetch photo details');
    }
  }

  // Update photo details
  static async updatePhoto(photoId: string, studioId: string, data: {
    originalName?: string;
    eventId?: string;
    clientId?: string;
    isMatched?: boolean;
    isProcessed?: boolean;
  }) {
    try {
      // Verify photo belongs to studio
      const existingPhoto = await prisma.photo.findFirst({
        where: { id: photoId, studioId },
      });

      if (!existingPhoto) {
        throw new Error('Photo not found');
      }

      const updatedPhoto = await prisma.photo.update({
        where: { id: photoId },
        data: {
          ...data,
          processedAt: data.isProcessed ? new Date() : undefined,
        },
        include: {
          client: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          event: {
            select: {
              id: true,
              name: true,
              date: true,
            },
          },
          _count: {
            select: {
              favorites: true,
              comments: true,
            },
          },
        },
      });

      return {
        ...updatedPhoto,
        size: updatedPhoto.size.toString(),
      };
    } catch (error) {
      console.error('Error updating photo:', error);
      throw new Error('Failed to update photo');
    }
  }

  // Delete photo
  static async deletePhoto(photoId: string, studioId: string): Promise<{ success: boolean; message: string }> {
    try {
      // Verify photo belongs to studio
      const photo = await prisma.photo.findFirst({
        where: { id: photoId, studioId },
      });

      if (!photo) {
        return { success: false, message: 'Photo not found' };
      }

      // Delete photo from database (cascade will handle related records)
      await prisma.photo.delete({
        where: { id: photoId },
      });

      // Update studio storage usage
      await prisma.studio.update({
        where: { id: studioId },
        data: {
          storageUsed: {
            decrement: photo.size,
          },
        },
      });

      // TODO: Delete physical file from storage
      // This should be implemented based on your storage solution

      return { success: true, message: 'Photo deleted successfully' };
    } catch (error) {
      console.error('Error deleting photo:', error);
      return { success: false, message: 'Failed to delete photo' };
    }
  }

  // Bulk delete photos
  static async bulkDeletePhotos(photoIds: string[], studioId: string): Promise<{ success: boolean; message: string; deletedCount: number }> {
    try {
      // Verify all photos belong to studio
      const photos = await prisma.photo.findMany({
        where: {
          id: { in: photoIds },
          studioId,
        },
        select: {
          id: true,
          size: true,
        },
      });

      if (photos.length !== photoIds.length) {
        return { success: false, message: 'Some photos not found', deletedCount: 0 };
      }

      // Calculate total size to subtract from storage
      const totalSize = photos.reduce((sum, photo) => sum + photo.size, BigInt(0));

      // Delete photos from database
      const deleteResult = await prisma.photo.deleteMany({
        where: {
          id: { in: photoIds },
          studioId,
        },
      });

      // Update studio storage usage
      await prisma.studio.update({
        where: { id: studioId },
        data: {
          storageUsed: {
            decrement: totalSize,
          },
        },
      });

      return {
        success: true,
        message: `${deleteResult.count} photos deleted successfully`,
        deletedCount: deleteResult.count,
      };
    } catch (error) {
      console.error('Error bulk deleting photos:', error);
      return { success: false, message: 'Failed to delete photos', deletedCount: 0 };
    }
  }

  // Get photo statistics for a studio
  static async getPhotoStats(studioId: string) {
    try {
      const [
        totalPhotos,
        processedPhotos,
        matchedPhotos,
        unmatchedPhotos,
        totalSize,
        recentUploads,
      ] = await Promise.all([
        prisma.photo.count({ where: { studioId } }),
        prisma.photo.count({ where: { studioId, isProcessed: true } }),
        prisma.photo.count({ where: { studioId, isMatched: true } }),
        prisma.photo.count({ where: { studioId, isMatched: false } }),
        prisma.photo.aggregate({
          where: { studioId },
          _sum: { size: true },
        }),
        prisma.photo.findMany({
          where: { studioId },
          orderBy: { uploadedAt: 'desc' },
          take: 10,
          include: {
            event: {
              select: {
                name: true,
              },
            },
          },
        }),
      ]);

      const pendingProcessing = totalPhotos - processedPhotos;

      return {
        totalPhotos,
        processedPhotos,
        matchedPhotos,
        unmatchedPhotos,
        pendingProcessing,
        totalSize: totalSize._sum.size?.toString() || '0',
        recentUploads: recentUploads.map(photo => ({
          ...photo,
          size: photo.size.toString(),
        })),
      };
    } catch (error) {
      console.error('Error fetching photo stats:', error);
      throw new Error('Failed to fetch photo statistics');
    }
  }

  // Match photos to clients using face recognition
  static async matchPhotosToClients(studioId: string, photoIds?: string[]) {
    try {
      // This is a placeholder for face recognition matching logic
      // In a real implementation, this would:
      // 1. Get face descriptors from photos
      // 2. Compare with client face descriptors
      // 3. Update photo.clientId and photo.isMatched based on matches

      const where: any = { studioId, isProcessed: true };
      if (photoIds) {
        where.id = { in: photoIds };
      }

      const photos = await prisma.photo.findMany({
        where,
        include: {
          faceDescriptors: true,
        },
      });

      let matchedCount = 0;

      for (const photo of photos) {
        // Placeholder matching logic
        // In reality, this would use face-api.js or similar
        if (photo.faceDescriptors.length > 0) {
          // Simulate matching
          const randomClient = await prisma.client.findFirst({
            where: { studioId },
            skip: Math.floor(Math.random() * 10),
          });

          if (randomClient) {
            await prisma.photo.update({
              where: { id: photo.id },
              data: {
                clientId: randomClient.id,
                isMatched: true,
              },
            });
            matchedCount++;
          }
        }
      }

      return {
        success: true,
        message: `${matchedCount} photos matched to clients`,
        matchedCount,
        totalProcessed: photos.length,
      };
    } catch (error) {
      console.error('Error matching photos to clients:', error);
      throw new Error('Failed to match photos to clients');
    }
  }
}
