import { prisma } from '@/lib/db';

export class EventService {
  // Get events for a studio with pagination and filters
  static async getStudioEvents(studioId: string, params: {
    page?: number;
    limit?: number;
    search?: string;
    status?: 'all' | 'active' | 'inactive';
    sortBy?: 'name' | 'date' | 'createdAt';
    sortOrder?: 'asc' | 'desc';
    dateFrom?: Date;
    dateTo?: Date;
  }) {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        status = 'all',
        sortBy = 'date',
        sortOrder = 'desc',
        dateFrom,
        dateTo,
      } = params;

      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = { studioId };
      
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { location: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (status !== 'all') {
        where.isActive = status === 'active';
      }

      if (dateFrom || dateTo) {
        where.date = {};
        if (dateFrom) where.date.gte = dateFrom;
        if (dateTo) where.date.lte = dateTo;
      }

      // Build orderBy clause
      const orderBy: any = {};
      orderBy[sortBy] = sortOrder;

      const [events, total] = await Promise.all([
        prisma.event.findMany({
          where,
          skip,
          take: limit,
          orderBy,
          include: {
            _count: {
              select: {
                photos: true,
              },
            },
          },
        }),
        prisma.event.count({ where }),
      ]);

      return {
        events,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching studio events:', error);
      throw new Error('Failed to fetch events');
    }
  }

  // Get event by ID
  static async getEventById(eventId: string, studioId?: string) {
    try {
      const where: any = { id: eventId };
      if (studioId) {
        where.studioId = studioId;
      }

      const event = await prisma.event.findUnique({
        where,
        include: {
          studio: {
            select: {
              id: true,
              name: true,
              businessName: true,
            },
          },
          photos: {
            take: 10,
            orderBy: { uploadedAt: 'desc' },
            select: {
              id: true,
              filename: true,
              path: true,
              size: true,
              isMatched: true,
              uploadedAt: true,
            },
          },
          _count: {
            select: {
              photos: true,
            },
          },
        },
      });

      if (!event) {
        throw new Error('Event not found');
      }

      return {
        ...event,
        photos: event.photos.map(photo => ({
          ...photo,
          size: photo.size.toString(),
        })),
      };
    } catch (error) {
      console.error('Error fetching event details:', error);
      throw new Error('Failed to fetch event details');
    }
  }

  // Create new event
  static async createEvent(studioId: string, data: {
    name: string;
    description?: string;
    date: Date;
    location?: string;
  }) {
    try {
      const event = await prisma.event.create({
        data: {
          ...data,
          studioId,
        },
        include: {
          _count: {
            select: {
              photos: true,
            },
          },
        },
      });

      return event;
    } catch (error) {
      console.error('Error creating event:', error);
      throw new Error('Failed to create event');
    }
  }

  // Update event
  static async updateEvent(eventId: string, studioId: string, data: {
    name?: string;
    description?: string;
    date?: Date;
    location?: string;
    isActive?: boolean;
  }) {
    try {
      // Verify event belongs to studio
      const existingEvent = await prisma.event.findFirst({
        where: { id: eventId, studioId },
      });

      if (!existingEvent) {
        throw new Error('Event not found');
      }

      const updatedEvent = await prisma.event.update({
        where: { id: eventId },
        data,
        include: {
          _count: {
            select: {
              photos: true,
            },
          },
        },
      });

      return updatedEvent;
    } catch (error) {
      console.error('Error updating event:', error);
      throw new Error('Failed to update event');
    }
  }

  // Delete event
  static async deleteEvent(eventId: string, studioId: string): Promise<{ success: boolean; message: string }> {
    try {
      // Verify event belongs to studio
      const event = await prisma.event.findFirst({
        where: { id: eventId, studioId },
      });

      if (!event) {
        return { success: false, message: 'Event not found' };
      }

      // Check if event has photos
      const photoCount = await prisma.photo.count({
        where: { eventId },
      });

      if (photoCount > 0) {
        return { success: false, message: 'Cannot delete event with associated photos' };
      }

      // Delete event
      await prisma.event.delete({
        where: { id: eventId },
      });

      return { success: true, message: 'Event deleted successfully' };
    } catch (error) {
      console.error('Error deleting event:', error);
      return { success: false, message: 'Failed to delete event' };
    }
  }

  // Get event photos with pagination
  static async getEventPhotos(eventId: string, studioId: string, params: {
    page?: number;
    limit?: number;
    isMatched?: boolean;
    sortBy?: 'uploadedAt' | 'filename';
    sortOrder?: 'asc' | 'desc';
  }) {
    try {
      // Verify event belongs to studio
      const event = await prisma.event.findFirst({
        where: { id: eventId, studioId },
      });

      if (!event) {
        throw new Error('Event not found');
      }

      const {
        page = 1,
        limit = 20,
        isMatched,
        sortBy = 'uploadedAt',
        sortOrder = 'desc',
      } = params;

      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = { eventId };
      if (typeof isMatched === 'boolean') {
        where.isMatched = isMatched;
      }

      // Build orderBy clause
      const orderBy: any = {};
      orderBy[sortBy] = sortOrder;

      const [photos, total] = await Promise.all([
        prisma.photo.findMany({
          where,
          skip,
          take: limit,
          orderBy,
          include: {
            client: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            _count: {
              select: {
                favorites: true,
                comments: true,
              },
            },
          },
        }),
        prisma.photo.count({ where }),
      ]);

      return {
        photos: photos.map(photo => ({
          ...photo,
          size: photo.size.toString(),
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching event photos:', error);
      throw new Error('Failed to fetch event photos');
    }
  }

  // Get event statistics
  static async getEventStats(eventId: string, studioId: string) {
    try {
      // Verify event belongs to studio
      const event = await prisma.event.findFirst({
        where: { id: eventId, studioId },
      });

      if (!event) {
        throw new Error('Event not found');
      }

      const [
        totalPhotos,
        matchedPhotos,
        unmatchedPhotos,
        totalClients,
        totalFavorites,
        totalComments,
        totalDownloads,
        clientBreakdown,
      ] = await Promise.all([
        prisma.photo.count({ where: { eventId } }),
        prisma.photo.count({ where: { eventId, isMatched: true } }),
        prisma.photo.count({ where: { eventId, isMatched: false } }),
        prisma.photo.count({
          where: { eventId, isMatched: true, clientId: { not: null } },
          distinct: ['clientId'],
        }),
        prisma.favorite.count({
          where: {
            photo: { eventId },
          },
        }),
        prisma.comment.count({
          where: {
            photo: { eventId },
          },
        }),
        prisma.accessLog.count({
          where: {
            action: 'download',
            client: {
              photos: {
                some: { eventId },
              },
            },
          },
        }),
        this.getEventClientBreakdown(eventId),
      ]);

      return {
        overview: {
          totalPhotos,
          matchedPhotos,
          unmatchedPhotos,
          totalClients,
          totalFavorites,
          totalComments,
          totalDownloads,
        },
        metrics: {
          matchingRate: totalPhotos > 0 ? Math.round((matchedPhotos / totalPhotos) * 100) : 0,
          avgPhotosPerClient: totalClients > 0 ? Math.round(matchedPhotos / totalClients) : 0,
          engagementRate: matchedPhotos > 0 ? Math.round((totalFavorites / matchedPhotos) * 100) : 0,
        },
        clientBreakdown,
      };
    } catch (error) {
      console.error('Error fetching event stats:', error);
      throw new Error('Failed to fetch event statistics');
    }
  }

  // Get events for client (events where client has photos)
  static async getClientEvents(clientId: string) {
    try {
      const events = await prisma.event.findMany({
        where: {
          photos: {
            some: {
              clientId,
              isMatched: true,
            },
          },
        },
        include: {
          _count: {
            select: {
              photos: {
                where: {
                  clientId,
                  isMatched: true,
                },
              },
            },
          },
        },
        orderBy: { date: 'desc' },
      });

      return events;
    } catch (error) {
      console.error('Error fetching client events:', error);
      throw new Error('Failed to fetch client events');
    }
  }

  // Get upcoming events for a studio
  static async getUpcomingEvents(studioId: string, limit: number = 5) {
    try {
      const today = new Date();
      
      const events = await prisma.event.findMany({
        where: {
          studioId,
          date: { gte: today },
          isActive: true,
        },
        orderBy: { date: 'asc' },
        take: limit,
        include: {
          _count: {
            select: {
              photos: true,
            },
          },
        },
      });

      return events;
    } catch (error) {
      console.error('Error fetching upcoming events:', error);
      throw new Error('Failed to fetch upcoming events');
    }
  }

  // Get recent events for a studio
  static async getRecentEvents(studioId: string, limit: number = 5) {
    try {
      const events = await prisma.event.findMany({
        where: { studioId },
        orderBy: { createdAt: 'desc' },
        take: limit,
        include: {
          _count: {
            select: {
              photos: true,
            },
          },
        },
      });

      return events;
    } catch (error) {
      console.error('Error fetching recent events:', error);
      throw new Error('Failed to fetch recent events');
    }
  }

  // Bulk update event status
  static async bulkUpdateEventStatus(
    eventIds: string[],
    studioId: string,
    isActive: boolean
  ): Promise<{ success: boolean; message: string; updatedCount: number }> {
    try {
      // Verify all events belong to studio
      const events = await prisma.event.findMany({
        where: {
          id: { in: eventIds },
          studioId,
        },
        select: { id: true },
      });

      if (events.length !== eventIds.length) {
        return { success: false, message: 'Some events not found', updatedCount: 0 };
      }

      const result = await prisma.event.updateMany({
        where: {
          id: { in: eventIds },
          studioId,
        },
        data: { isActive },
      });

      return {
        success: true,
        message: `${result.count} events updated successfully`,
        updatedCount: result.count,
      };
    } catch (error) {
      console.error('Error bulk updating events:', error);
      return { success: false, message: 'Failed to update events', updatedCount: 0 };
    }
  }

  // Get event analytics for admin
  static async getEventAnalytics(period: number = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - period);

      const [
        totalEvents,
        activeEvents,
        eventsWithPhotos,
        avgPhotosPerEvent,
        eventsByStudio,
        recentEvents,
      ] = await Promise.all([
        prisma.event.count(),
        prisma.event.count({ where: { isActive: true } }),
        prisma.event.count({
          where: {
            photos: {
              some: {},
            },
          },
        }),
        prisma.event.aggregate({
          _avg: {
            photos: {
              _count: true,
            },
          },
        }),
        this.getEventsByStudio(10),
        prisma.event.findMany({
          where: {
            createdAt: { gte: startDate },
          },
          orderBy: { createdAt: 'desc' },
          take: 10,
          include: {
            studio: {
              select: {
                name: true,
                businessName: true,
              },
            },
            _count: {
              select: {
                photos: true,
              },
            },
          },
        }),
      ]);

      return {
        overview: {
          totalEvents,
          activeEvents,
          inactiveEvents: totalEvents - activeEvents,
          eventsWithPhotos,
          eventsWithoutPhotos: totalEvents - eventsWithPhotos,
          avgPhotosPerEvent: Math.round(avgPhotosPerEvent._avg.photos || 0),
        },
        eventsByStudio,
        recentEvents,
        period,
      };
    } catch (error) {
      console.error('Error fetching event analytics:', error);
      throw new Error('Failed to fetch event analytics');
    }
  }

  // Helper methods

  private static async getEventClientBreakdown(eventId: string) {
    try {
      const clients = await prisma.client.findMany({
        where: {
          photos: {
            some: {
              eventId,
              isMatched: true,
            },
          },
        },
        select: {
          id: true,
          name: true,
          email: true,
          _count: {
            select: {
              photos: {
                where: {
                  eventId,
                  isMatched: true,
                },
              },
              favorites: {
                where: {
                  photo: {
                    eventId,
                  },
                },
              },
              comments: {
                where: {
                  photo: {
                    eventId,
                  },
                },
              },
            },
          },
        },
        orderBy: {
          photos: {
            _count: 'desc',
          },
        },
      });

      return clients.map(client => ({
        id: client.id,
        name: client.name,
        email: client.email,
        photo_count: client._count.photos,
        favorite_count: client._count.favorites,
        comment_count: client._count.comments,
      }));
    } catch (error) {
      console.error('Error fetching event client breakdown:', error);
      return [];
    }
  }

  private static async getEventsByStudio(limit: number) {
    try {
      return await prisma.studio.findMany({
        take: limit,
        orderBy: {
          events: {
            _count: 'desc',
          },
        },
        select: {
          id: true,
          name: true,
          businessName: true,
          _count: {
            select: {
              events: true,
            },
          },
        },
      });
    } catch (error) {
      console.error('Error fetching events by studio:', error);
      return [];
    }
  }
}
