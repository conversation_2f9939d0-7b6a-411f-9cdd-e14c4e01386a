import { prisma } from '@/lib/db';

export class NotificationService {
  // Create notification
  static async createNotification(data: {
    studioId?: string;
    clientId?: string;
    type: 'email' | 'whatsapp' | 'system';
    title: string;
    message: string;
  }) {
    try {
      const notification = await prisma.notification.create({
        data,
      });

      // TODO: Implement actual notification sending based on type
      // For now, we'll just create the database record

      return notification;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw new Error('Failed to create notification');
    }
  }

  // Get notifications for a studio
  static async getStudioNotifications(studioId: string, params: {
    page?: number;
    limit?: number;
    isRead?: boolean;
    type?: 'email' | 'whatsapp' | 'system';
    sortBy?: 'createdAt';
    sortOrder?: 'asc' | 'desc';
  }) {
    try {
      const {
        page = 1,
        limit = 20,
        isRead,
        type,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = params;

      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = { studioId };
      
      if (typeof isRead === 'boolean') {
        where.isRead = isRead;
      }
      
      if (type) {
        where.type = type;
      }

      // Build orderBy clause
      const orderBy: any = {};
      orderBy[sortBy] = sortOrder;

      const [notifications, total, unreadCount] = await Promise.all([
        prisma.notification.findMany({
          where,
          skip,
          take: limit,
          orderBy,
        }),
        prisma.notification.count({ where }),
        prisma.notification.count({
          where: { studioId, isRead: false },
        }),
      ]);

      return {
        notifications,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        unreadCount,
      };
    } catch (error) {
      console.error('Error fetching studio notifications:', error);
      throw new Error('Failed to fetch notifications');
    }
  }

  // Mark notification as read
  static async markAsRead(notificationId: string, studioId?: string): Promise<{ success: boolean; message: string }> {
    try {
      const where: any = { id: notificationId };
      if (studioId) {
        where.studioId = studioId;
      }

      const notification = await prisma.notification.findFirst({ where });
      
      if (!notification) {
        return { success: false, message: 'Notification not found' };
      }

      await prisma.notification.update({
        where: { id: notificationId },
        data: { isRead: true },
      });

      return { success: true, message: 'Notification marked as read' };
    } catch (error) {
      console.error('Error marking notification as read:', error);
      return { success: false, message: 'Failed to mark notification as read' };
    }
  }

  // Mark all notifications as read for a studio
  static async markAllAsRead(studioId: string): Promise<{ success: boolean; message: string; updatedCount: number }> {
    try {
      const result = await prisma.notification.updateMany({
        where: { studioId, isRead: false },
        data: { isRead: true },
      });

      return {
        success: true,
        message: 'All notifications marked as read',
        updatedCount: result.count,
      };
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      return { success: false, message: 'Failed to mark notifications as read', updatedCount: 0 };
    }
  }

  // Delete notification
  static async deleteNotification(notificationId: string, studioId?: string): Promise<{ success: boolean; message: string }> {
    try {
      const where: any = { id: notificationId };
      if (studioId) {
        where.studioId = studioId;
      }

      const notification = await prisma.notification.findFirst({ where });
      
      if (!notification) {
        return { success: false, message: 'Notification not found' };
      }

      await prisma.notification.delete({
        where: { id: notificationId },
      });

      return { success: true, message: 'Notification deleted successfully' };
    } catch (error) {
      console.error('Error deleting notification:', error);
      return { success: false, message: 'Failed to delete notification' };
    }
  }

  // Send welcome notification to new studio
  static async sendWelcomeNotification(studioId: string) {
    try {
      await this.createNotification({
        studioId,
        type: 'system',
        title: 'Welcome to Studio ERP!',
        message: 'Thank you for joining Studio ERP. Start by uploading your first photos and adding clients to get started.',
      });

      return { success: true, message: 'Welcome notification sent' };
    } catch (error) {
      console.error('Error sending welcome notification:', error);
      return { success: false, message: 'Failed to send welcome notification' };
    }
  }

  // Send photo processing notification
  static async sendPhotoProcessingNotification(studioId: string, photoCount: number, matchedCount: number) {
    try {
      await this.createNotification({
        studioId,
        type: 'system',
        title: 'Photo Processing Complete',
        message: `${photoCount} photos have been processed. ${matchedCount} photos were successfully matched to clients.`,
      });

      return { success: true, message: 'Processing notification sent' };
    } catch (error) {
      console.error('Error sending processing notification:', error);
      return { success: false, message: 'Failed to send processing notification' };
    }
  }

  // Send storage warning notification
  static async sendStorageWarningNotification(studioId: string, usagePercentage: number) {
    try {
      let title = 'Storage Warning';
      let message = `Your storage is ${usagePercentage}% full. Consider upgrading your plan or removing old photos.`;

      if (usagePercentage >= 95) {
        title = 'Storage Almost Full';
        message = `Your storage is ${usagePercentage}% full. You may not be able to upload new photos soon.`;
      }

      await this.createNotification({
        studioId,
        type: 'system',
        title,
        message,
      });

      return { success: true, message: 'Storage warning sent' };
    } catch (error) {
      console.error('Error sending storage warning:', error);
      return { success: false, message: 'Failed to send storage warning' };
    }
  }

  // Send subscription expiry notification
  static async sendSubscriptionExpiryNotification(studioId: string, daysUntilExpiry: number) {
    try {
      let title = 'Subscription Expiring Soon';
      let message = `Your subscription will expire in ${daysUntilExpiry} days. Renew now to continue using all features.`;

      if (daysUntilExpiry <= 0) {
        title = 'Subscription Expired';
        message = 'Your subscription has expired. Please renew to continue using the service.';
      }

      await this.createNotification({
        studioId,
        type: 'system',
        title,
        message,
      });

      return { success: true, message: 'Subscription notification sent' };
    } catch (error) {
      console.error('Error sending subscription notification:', error);
      return { success: false, message: 'Failed to send subscription notification' };
    }
  }

  // Send client access notification
  static async sendClientAccessNotification(studioId: string, clientName: string, action: string) {
    try {
      await this.createNotification({
        studioId,
        type: 'system',
        title: 'Client Activity',
        message: `${clientName} ${action} their gallery.`,
      });

      return { success: true, message: 'Client access notification sent' };
    } catch (error) {
      console.error('Error sending client access notification:', error);
      return { success: false, message: 'Failed to send client access notification' };
    }
  }

  // Get notification statistics
  static async getNotificationStats(studioId: string) {
    try {
      const [
        totalNotifications,
        unreadNotifications,
        systemNotifications,
        emailNotifications,
        whatsappNotifications,
        recentNotifications,
      ] = await Promise.all([
        prisma.notification.count({ where: { studioId } }),
        prisma.notification.count({ where: { studioId, isRead: false } }),
        prisma.notification.count({ where: { studioId, type: 'system' } }),
        prisma.notification.count({ where: { studioId, type: 'email' } }),
        prisma.notification.count({ where: { studioId, type: 'whatsapp' } }),
        prisma.notification.findMany({
          where: { studioId },
          orderBy: { createdAt: 'desc' },
          take: 5,
          select: {
            id: true,
            title: true,
            type: true,
            isRead: true,
            createdAt: true,
          },
        }),
      ]);

      return {
        totalNotifications,
        unreadNotifications,
        readNotifications: totalNotifications - unreadNotifications,
        byType: {
          system: systemNotifications,
          email: emailNotifications,
          whatsapp: whatsappNotifications,
        },
        recentNotifications,
      };
    } catch (error) {
      console.error('Error fetching notification stats:', error);
      throw new Error('Failed to fetch notification statistics');
    }
  }

  // Bulk delete notifications
  static async bulkDeleteNotifications(notificationIds: string[], studioId?: string): Promise<{ success: boolean; message: string; deletedCount: number }> {
    try {
      const where: any = { id: { in: notificationIds } };
      if (studioId) {
        where.studioId = studioId;
      }

      const result = await prisma.notification.deleteMany({ where });

      return {
        success: true,
        message: `${result.count} notifications deleted successfully`,
        deletedCount: result.count,
      };
    } catch (error) {
      console.error('Error bulk deleting notifications:', error);
      return { success: false, message: 'Failed to delete notifications', deletedCount: 0 };
    }
  }

  // Get admin notifications (system-wide)
  static async getAdminNotifications(params: {
    page?: number;
    limit?: number;
    type?: 'email' | 'whatsapp' | 'system';
    sortBy?: 'createdAt';
    sortOrder?: 'asc' | 'desc';
  }) {
    try {
      const {
        page = 1,
        limit = 20,
        type,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = params;

      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = {};
      if (type) {
        where.type = type;
      }

      // Build orderBy clause
      const orderBy: any = {};
      orderBy[sortBy] = sortOrder;

      const [notifications, total] = await Promise.all([
        prisma.notification.findMany({
          where,
          skip,
          take: limit,
          orderBy,
          include: {
            studio: {
              select: {
                name: true,
                businessName: true,
              },
            },
          },
        }),
        prisma.notification.count({ where }),
      ]);

      return {
        notifications,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching admin notifications:', error);
      throw new Error('Failed to fetch admin notifications');
    }
  }

  // Send broadcast notification to all studios
  static async sendBroadcastNotification(data: {
    type: 'email' | 'whatsapp' | 'system';
    title: string;
    message: string;
    studioIds?: string[]; // If provided, send only to these studios
  }) {
    try {
      let studios;
      
      if (data.studioIds && data.studioIds.length > 0) {
        studios = await prisma.studio.findMany({
          where: { id: { in: data.studioIds } },
          select: { id: true },
        });
      } else {
        studios = await prisma.studio.findMany({
          where: { isActive: true, isApproved: true },
          select: { id: true },
        });
      }

      const notifications = studios.map(studio => ({
        studioId: studio.id,
        type: data.type,
        title: data.title,
        message: data.message,
      }));

      await prisma.notification.createMany({
        data: notifications,
      });

      return {
        success: true,
        message: `Broadcast notification sent to ${studios.length} studios`,
        sentCount: studios.length,
      };
    } catch (error) {
      console.error('Error sending broadcast notification:', error);
      return { success: false, message: 'Failed to send broadcast notification', sentCount: 0 };
    }
  }
}
