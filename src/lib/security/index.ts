import { prisma } from '@/lib/db';
import { NextRequest } from 'next/server';
import crypto from 'crypto';

// Security configuration
const SECURITY_CONFIG = {
  FACE_RETRY_LIMIT: parseInt(process.env.FACE_RETRY_LIMIT || '3'),
  LOGIN_RETRY_LIMIT: parseInt(process.env.LOGIN_RETRY_LIMIT || '5'),
  BLOCK_DURATION_MINUTES: parseInt(process.env.BLOCK_DURATION_MINUTES || '30'),
  SESSION_TIMEOUT_MINUTES: parseInt(process.env.SESSION_TIMEOUT_MINUTES || '60'),
  MAX_DEVICES_PER_CLIENT: parseInt(process.env.MAX_DEVICES_PER_CLIENT || '1'),
  ALLOWED_COUNTRIES: process.env.ALLOWED_COUNTRIES?.split(',') || [],
};

export interface SecurityContext {
  ipAddress: string;
  userAgent: string;
  browserFingerprint: string;
  deviceId: string;
  location?: {
    country: string;
    region: string;
    city: string;
  };
}

export interface SecurityResult {
  success: boolean;
  message?: string;
  blocked?: boolean;
  blockedUntil?: Date;
  attemptsRemaining?: number;
}

export class SecurityService {
  // Generate browser fingerprint from request headers
  static generateBrowserFingerprint(request: NextRequest): string {
    const userAgent = request.headers.get('user-agent') || '';
    const acceptLanguage = request.headers.get('accept-language') || '';
    const acceptEncoding = request.headers.get('accept-encoding') || '';
    const connection = request.headers.get('connection') || '';
    
    const fingerprint = `${userAgent}|${acceptLanguage}|${acceptEncoding}|${connection}`;
    return crypto.createHash('sha256').update(fingerprint).digest('hex');
  }

  // Generate device ID from browser fingerprint and IP
  static generateDeviceId(browserFingerprint: string, ipAddress: string): string {
    const deviceString = `${browserFingerprint}|${ipAddress}`;
    return crypto.createHash('md5').update(deviceString).digest('hex');
  }

  // Extract security context from request
  static extractSecurityContext(request: NextRequest): SecurityContext {
    const ipAddress = request.headers.get('x-forwarded-for')?.split(',')[0] || 
                     request.headers.get('x-real-ip') || 
                     'unknown';
    const userAgent = request.headers.get('user-agent') || '';
    const browserFingerprint = this.generateBrowserFingerprint(request);
    const deviceId = this.generateDeviceId(browserFingerprint, ipAddress);

    return {
      ipAddress,
      userAgent,
      browserFingerprint,
      deviceId,
    };
  }

  // Check if client is blocked
  static async isClientBlocked(clientId: string): Promise<boolean> {
    const client = await prisma.client.findUnique({
      where: { id: clientId },
      select: { isBlocked: true, blockedUntil: true },
    });

    if (!client) return false;
    if (!client.isBlocked) return false;
    if (!client.blockedUntil) return true;

    // Check if block has expired
    if (new Date() > client.blockedUntil) {
      await prisma.client.update({
        where: { id: clientId },
        data: { 
          isBlocked: false, 
          blockedUntil: null,
          faceRetryCount: 0,
        },
      });
      return false;
    }

    return true;
  }

  // Record security attempt
  static async recordSecurityAttempt(
    clientId: string | null,
    attemptType: 'face_scan' | 'login' | 'access',
    context: SecurityContext,
    success: boolean,
    failureReason?: string
  ): Promise<void> {
    await prisma.securityAttempt.create({
      data: {
        clientId,
        attemptType,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        browserFingerprint: context.browserFingerprint,
        success,
        failureReason,
      },
    });
  }

  // Check and update face scan attempts
  static async checkFaceScanAttempts(clientId: string, context: SecurityContext): Promise<SecurityResult> {
    const client = await prisma.client.findUnique({
      where: { id: clientId },
      select: { faceRetryCount: true, isBlocked: true, blockedUntil: true },
    });

    if (!client) {
      return { success: false, message: 'Client not found' };
    }

    // Check if already blocked
    if (await this.isClientBlocked(clientId)) {
      return { 
        success: false, 
        message: 'Account temporarily blocked due to too many failed attempts',
        blocked: true,
        blockedUntil: client.blockedUntil || undefined,
      };
    }

    // Check retry limit
    if (client.faceRetryCount >= SECURITY_CONFIG.FACE_RETRY_LIMIT) {
      const blockedUntil = new Date(Date.now() + SECURITY_CONFIG.BLOCK_DURATION_MINUTES * 60 * 1000);
      
      await prisma.client.update({
        where: { id: clientId },
        data: { 
          isBlocked: true, 
          blockedUntil,
        },
      });

      await this.recordSecurityAttempt(clientId, 'face_scan', context, false, 'Retry limit exceeded');

      return { 
        success: false, 
        message: `Too many failed attempts. Account blocked for ${SECURITY_CONFIG.BLOCK_DURATION_MINUTES} minutes.`,
        blocked: true,
        blockedUntil,
      };
    }

    const attemptsRemaining = SECURITY_CONFIG.FACE_RETRY_LIMIT - client.faceRetryCount;
    return { 
      success: true, 
      attemptsRemaining,
    };
  }

  // Record failed face scan attempt
  static async recordFailedFaceScan(clientId: string, context: SecurityContext, reason: string): Promise<void> {
    await prisma.client.update({
      where: { id: clientId },
      data: { 
        faceRetryCount: { increment: 1 },
      },
    });

    await this.recordSecurityAttempt(clientId, 'face_scan', context, false, reason);
  }

  // Reset face scan attempts on success
  static async resetFaceScanAttempts(clientId: string, context: SecurityContext): Promise<void> {
    await prisma.client.update({
      where: { id: clientId },
      data: { faceRetryCount: 0 },
    });

    await this.recordSecurityAttempt(clientId, 'face_scan', context, true);
  }

  // Check device access
  static async checkDeviceAccess(clientId: string, context: SecurityContext): Promise<SecurityResult> {
    const client = await prisma.client.findUnique({
      where: { id: clientId },
      select: { maxDevices: true },
      include: {
        deviceAccess: {
          where: { isBlocked: false },
          orderBy: { lastSeen: 'desc' },
        },
      },
    });

    if (!client) {
      return { success: false, message: 'Client not found' };
    }

    // Check if current device exists
    const existingDevice = client.deviceAccess.find(d => d.deviceId === context.deviceId);
    
    if (existingDevice) {
      // Update last seen
      await prisma.deviceAccess.update({
        where: { id: existingDevice.id },
        data: { 
          lastSeen: new Date(),
          accessCount: { increment: 1 },
        },
      });
      return { success: true };
    }

    // Check device limit
    if (client.deviceAccess.length >= client.maxDevices) {
      return { 
        success: false, 
        message: `Access limited to ${client.maxDevices} device(s). Please contact your photographer to add more devices.`,
      };
    }

    // Add new device
    await prisma.deviceAccess.create({
      data: {
        clientId,
        deviceId: context.deviceId,
        browserFingerprint: context.browserFingerprint,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        location: context.location ? JSON.stringify(context.location) : null,
        isApproved: true, // Auto-approve for now
      },
    });

    return { success: true };
  }

  // Create client session
  static async createClientSession(clientId: string, context: SecurityContext): Promise<string> {
    const sessionToken = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date(Date.now() + SECURITY_CONFIG.SESSION_TIMEOUT_MINUTES * 60 * 1000);

    await prisma.clientSession.create({
      data: {
        clientId,
        sessionToken,
        deviceId: context.deviceId,
        browserFingerprint: context.browserFingerprint,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        location: context.location ? JSON.stringify(context.location) : null,
        expiresAt,
      },
    });

    return sessionToken;
  }

  // Validate client session
  static async validateClientSession(sessionToken: string): Promise<{ valid: boolean; clientId?: string }> {
    const session = await prisma.clientSession.findUnique({
      where: { sessionToken },
      select: { 
        clientId: true, 
        expiresAt: true, 
        isActive: true,
        lastActivity: true,
      },
    });

    if (!session || !session.isActive) {
      return { valid: false };
    }

    // Check expiration
    if (new Date() > session.expiresAt) {
      await prisma.clientSession.update({
        where: { sessionToken },
        data: { isActive: false },
      });
      return { valid: false };
    }

    // Update last activity
    await prisma.clientSession.update({
      where: { sessionToken },
      data: { lastActivity: new Date() },
    });

    return { valid: true, clientId: session.clientId };
  }

  // Cleanup expired sessions
  static async cleanupExpiredSessions(): Promise<void> {
    await prisma.clientSession.updateMany({
      where: {
        OR: [
          { expiresAt: { lt: new Date() } },
          { 
            lastActivity: { 
              lt: new Date(Date.now() - SECURITY_CONFIG.SESSION_TIMEOUT_MINUTES * 60 * 1000) 
            } 
          },
        ],
      },
      data: { isActive: false },
    });
  }

  // Check geo-restrictions
  static async checkGeoRestrictions(clientId: string, context: SecurityContext): Promise<SecurityResult> {
    if (!context.location || SECURITY_CONFIG.ALLOWED_COUNTRIES.length === 0) {
      return { success: true }; // No restrictions
    }

    const client = await prisma.client.findUnique({
      where: { id: clientId },
      select: { geoRestrictions: true },
    });

    if (!client || !client.geoRestrictions) {
      return { success: true }; // No client-specific restrictions
    }

    const restrictions = client.geoRestrictions as string[];
    if (restrictions.includes(context.location.country)) {
      return { success: true };
    }

    return { 
      success: false, 
      message: 'Access not allowed from your current location',
    };
  }
}
