import { NextRequest } from 'next/server';
import crypto from 'crypto';

export interface BotProtectionResult {
  success: boolean;
  message?: string;
  challenge?: string;
  requiresChallenge?: boolean;
}

export interface BehaviorAnalysis {
  mouseMovements: number;
  keystrokes: number;
  timeSpent: number;
  scrollEvents: number;
  clickEvents: number;
  suspiciousPatterns: string[];
}

export class BotProtectionService {
  private static readonly SUSPICIOUS_USER_AGENTS = [
    'bot', 'crawler', 'spider', 'scraper', 'headless', 'phantom', 'selenium',
    'puppeteer', 'playwright', 'automation', 'test', 'curl', 'wget'
  ];

  private static readonly RATE_LIMITS = {
    requests_per_minute: 30,
    requests_per_hour: 200,
    face_scans_per_hour: 10,
  };

  // Analyze request for bot-like behavior
  static analyzeRequest(request: NextRequest): BotProtectionResult {
    const userAgent = request.headers.get('user-agent') || '';
    const referer = request.headers.get('referer') || '';
    const acceptLanguage = request.headers.get('accept-language') || '';
    const acceptEncoding = request.headers.get('accept-encoding') || '';

    const suspiciousPatterns: string[] = [];

    // Check user agent
    if (this.isSuspiciousUserAgent(userAgent)) {
      suspiciousPatterns.push('suspicious_user_agent');
    }

    // Check for missing common headers
    if (!acceptLanguage) {
      suspiciousPatterns.push('missing_accept_language');
    }

    if (!acceptEncoding) {
      suspiciousPatterns.push('missing_accept_encoding');
    }

    // Check for automation indicators
    if (userAgent.toLowerCase().includes('headless')) {
      suspiciousPatterns.push('headless_browser');
    }

    // Check referer patterns
    if (referer && this.isSuspiciousReferer(referer)) {
      suspiciousPatterns.push('suspicious_referer');
    }

    // If multiple suspicious patterns, require challenge
    if (suspiciousPatterns.length >= 2) {
      return {
        success: false,
        requiresChallenge: true,
        message: 'Additional verification required',
        challenge: this.generateChallenge(),
      };
    }

    return { success: true };
  }

  // Check if user agent is suspicious
  private static isSuspiciousUserAgent(userAgent: string): boolean {
    const lowerUA = userAgent.toLowerCase();
    return this.SUSPICIOUS_USER_AGENTS.some(pattern => lowerUA.includes(pattern));
  }

  // Check if referer is suspicious
  private static isSuspiciousReferer(referer: string): boolean {
    // Check for direct access without proper referer
    if (!referer.includes(process.env.NEXT_PUBLIC_APP_URL || 'localhost')) {
      return true;
    }
    return false;
  }

  // Generate invisible challenge
  static generateChallenge(): string {
    const timestamp = Date.now();
    const random = crypto.randomBytes(16).toString('hex');
    const challenge = `${timestamp}:${random}`;
    
    // Create hash for verification
    const hash = crypto.createHmac('sha256', process.env.BOT_PROTECTION_SECRET || 'default-secret')
      .update(challenge)
      .digest('hex');
    
    return `${challenge}:${hash}`;
  }

  // Verify challenge response
  static verifyChallenge(challenge: string, response: string): boolean {
    try {
      const [timestamp, random, hash] = challenge.split(':');
      const expectedChallenge = `${timestamp}:${random}`;
      
      const expectedHash = crypto.createHmac('sha256', process.env.BOT_PROTECTION_SECRET || 'default-secret')
        .update(expectedChallenge)
        .digest('hex');
      
      // Verify hash
      if (hash !== expectedHash) {
        return false;
      }

      // Check timestamp (challenge should be used within 5 minutes)
      const challengeTime = parseInt(timestamp);
      const now = Date.now();
      if (now - challengeTime > 5 * 60 * 1000) {
        return false;
      }

      // Verify response (should be a simple calculation or pattern)
      const expectedResponse = this.calculateChallengeResponse(timestamp, random);
      return response === expectedResponse;
    } catch (error) {
      return false;
    }
  }

  // Calculate expected challenge response
  private static calculateChallengeResponse(timestamp: string, random: string): string {
    // Simple calculation: sum of timestamp digits + length of random string
    const timestampSum = timestamp.split('').reduce((sum, digit) => sum + parseInt(digit), 0);
    const randomLength = random.length;
    return (timestampSum + randomLength).toString();
  }

  // Analyze user behavior patterns
  static analyzeBehavior(behaviorData: BehaviorAnalysis): BotProtectionResult {
    const suspiciousPatterns: string[] = [];

    // Check for human-like mouse movements
    if (behaviorData.mouseMovements < 5) {
      suspiciousPatterns.push('insufficient_mouse_movement');
    }

    // Check time spent on page
    if (behaviorData.timeSpent < 2000) { // Less than 2 seconds
      suspiciousPatterns.push('too_fast_interaction');
    }

    // Check for natural interaction patterns
    if (behaviorData.clickEvents === 0 && behaviorData.keystrokes === 0) {
      suspiciousPatterns.push('no_user_interaction');
    }

    // Check for bot-like patterns
    if (behaviorData.scrollEvents > 100 && behaviorData.timeSpent < 10000) {
      suspiciousPatterns.push('rapid_scrolling');
    }

    // If too many suspicious patterns, flag as bot
    if (suspiciousPatterns.length >= 2) {
      return {
        success: false,
        message: 'Suspicious behavior detected',
        requiresChallenge: true,
        challenge: this.generateChallenge(),
      };
    }

    return { success: true };
  }

  // Rate limiting check
  static async checkRateLimit(
    identifier: string, 
    action: 'request' | 'face_scan' | 'login',
    ipAddress: string
  ): Promise<BotProtectionResult> {
    // This would typically use Redis or similar for production
    // For now, we'll use a simple in-memory approach
    
    const key = `${action}:${identifier}:${ipAddress}`;
    const now = Date.now();
    
    // Get current counts (this would be from Redis in production)
    const counts = await this.getRateLimitCounts(key);
    
    let limit: number;
    let window: number;
    
    switch (action) {
      case 'face_scan':
        limit = this.RATE_LIMITS.face_scans_per_hour;
        window = 60 * 60 * 1000; // 1 hour
        break;
      case 'login':
        limit = 10; // 10 login attempts per hour
        window = 60 * 60 * 1000; // 1 hour
        break;
      default:
        limit = this.RATE_LIMITS.requests_per_minute;
        window = 60 * 1000; // 1 minute
    }

    // Clean old entries
    const validCounts = counts.filter(timestamp => now - timestamp < window);
    
    if (validCounts.length >= limit) {
      return {
        success: false,
        message: `Rate limit exceeded. Please wait before trying again.`,
        requiresChallenge: true,
      };
    }

    // Add current request
    validCounts.push(now);
    await this.setRateLimitCounts(key, validCounts);

    return { success: true };
  }

  // Get rate limit counts (would use Redis in production)
  private static async getRateLimitCounts(key: string): Promise<number[]> {
    // Placeholder - in production, this would query Redis
    // For now, return empty array
    return [];
  }

  // Set rate limit counts (would use Redis in production)
  private static async setRateLimitCounts(key: string, counts: number[]): Promise<void> {
    // Placeholder - in production, this would update Redis
    // For now, do nothing
  }

  // Generate honeypot fields for forms
  static generateHoneypotFields(): { [key: string]: string } {
    return {
      email_confirm: '', // Should remain empty
      phone_verify: '', // Should remain empty
      website_url: '', // Should remain empty
      company_name: '', // Should remain empty
    };
  }

  // Verify honeypot fields
  static verifyHoneypot(formData: { [key: string]: any }): boolean {
    const honeypotFields = ['email_confirm', 'phone_verify', 'website_url', 'company_name'];
    
    for (const field of honeypotFields) {
      if (formData[field] && formData[field].trim() !== '') {
        return false; // Bot filled honeypot field
      }
    }
    
    return true;
  }

  // Generate timing challenge
  static generateTimingChallenge(): { challenge: string; minTime: number } {
    const challenge = crypto.randomBytes(8).toString('hex');
    const minTime = 3000; // Minimum 3 seconds to complete
    
    return { challenge, minTime };
  }

  // Verify timing challenge
  static verifyTimingChallenge(
    challenge: string, 
    startTime: number, 
    endTime: number, 
    minTime: number
  ): boolean {
    const actualTime = endTime - startTime;
    
    // Too fast - likely bot
    if (actualTime < minTime) {
      return false;
    }
    
    // Too slow - might be suspicious
    if (actualTime > minTime * 10) {
      return false;
    }
    
    return true;
  }

  // Check for common bot signatures
  static checkBotSignatures(request: NextRequest): string[] {
    const signatures: string[] = [];
    
    const userAgent = request.headers.get('user-agent') || '';
    const acceptHeader = request.headers.get('accept') || '';
    const acceptLanguage = request.headers.get('accept-language') || '';
    
    // Check for automation tools
    if (userAgent.includes('selenium') || userAgent.includes('webdriver')) {
      signatures.push('automation_tool');
    }
    
    // Check for missing browser headers
    if (!acceptHeader.includes('text/html')) {
      signatures.push('missing_html_accept');
    }
    
    // Check for single language (bots often don't set multiple languages)
    if (acceptLanguage && !acceptLanguage.includes(',')) {
      signatures.push('single_language');
    }
    
    // Check for exact version matches (bots often use exact versions)
    const chromeMatch = userAgent.match(/Chrome\/(\d+\.\d+\.\d+\.\d+)/);
    if (chromeMatch && chromeMatch[1].endsWith('.0.0')) {
      signatures.push('exact_version');
    }
    
    return signatures;
  }
}
