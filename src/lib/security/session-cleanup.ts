import { prisma } from '@/lib/db';
import { SecurityService } from './index';

export class SessionCleanupService {
  // Clean up expired sessions and security attempts
  static async cleanupExpiredData(): Promise<{
    expiredSessions: number;
    oldSecurityAttempts: number;
    oldAccessLogs: number;
    unblockedClients: number;
  }> {
    try {
      console.log('Starting security data cleanup...');

      // 1. Clean up expired sessions
      await SecurityService.cleanupExpiredSessions();
      
      const expiredSessionsCount = await prisma.clientSession.count({
        where: { isActive: false }
      });

      // Delete inactive sessions older than 7 days
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      await prisma.clientSession.deleteMany({
        where: {
          isActive: false,
          createdAt: { lt: sevenDaysAgo }
        }
      });

      // 2. Clean up old security attempts (keep only last 30 days)
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const oldSecurityAttempts = await prisma.securityAttempt.deleteMany({
        where: {
          createdAt: { lt: thirtyDaysAgo }
        }
      });

      // 3. Clean up old access logs (keep only last 90 days)
      const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
      const oldAccessLogs = await prisma.accessLog.deleteMany({
        where: {
          createdAt: { lt: ninetyDaysAgo }
        }
      });

      // 4. Unblock clients whose block period has expired
      const unblockedClients = await prisma.client.updateMany({
        where: {
          isBlocked: true,
          blockedUntil: { lt: new Date() }
        },
        data: {
          isBlocked: false,
          blockedUntil: null,
          faceRetryCount: 0,
        }
      });

      // 5. Clean up old device access records (inactive for 90+ days)
      await prisma.deviceAccess.deleteMany({
        where: {
          lastSeen: { lt: ninetyDaysAgo }
        }
      });

      console.log('Security data cleanup completed:', {
        expiredSessions: expiredSessionsCount,
        oldSecurityAttempts: oldSecurityAttempts.count,
        oldAccessLogs: oldAccessLogs.count,
        unblockedClients: unblockedClients.count,
      });

      return {
        expiredSessions: expiredSessionsCount,
        oldSecurityAttempts: oldSecurityAttempts.count,
        oldAccessLogs: oldAccessLogs.count,
        unblockedClients: unblockedClients.count,
      };
    } catch (error) {
      console.error('Error during security data cleanup:', error);
      throw error;
    }
  }

  // Generate security report
  static async generateSecurityReport(): Promise<{
    activeSessions: number;
    blockedClients: number;
    recentFailedAttempts: number;
    suspiciousDevices: number;
    topFailureReasons: Array<{ reason: string; count: number }>;
    deviceStats: Array<{ deviceId: string; accessCount: number; lastSeen: Date }>;
  }> {
    try {
      const now = new Date();
      const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      // Count active sessions
      const activeSessions = await prisma.clientSession.count({
        where: { isActive: true }
      });

      // Count blocked clients
      const blockedClients = await prisma.client.count({
        where: { isBlocked: true }
      });

      // Count recent failed attempts
      const recentFailedAttempts = await prisma.securityAttempt.count({
        where: {
          success: false,
          createdAt: { gte: last24Hours }
        }
      });

      // Count suspicious devices (multiple failed attempts)
      const suspiciousDevices = await prisma.securityAttempt.groupBy({
        by: ['browserFingerprint'],
        where: {
          success: false,
          createdAt: { gte: last7Days }
        },
        having: {
          browserFingerprint: {
            _count: { gte: 5 }
          }
        }
      });

      // Get top failure reasons
      const failureReasons = await prisma.securityAttempt.groupBy({
        by: ['failureReason'],
        where: {
          success: false,
          createdAt: { gte: last7Days },
          failureReason: { not: null }
        },
        _count: {
          failureReason: true
        },
        orderBy: {
          _count: {
            failureReason: 'desc'
          }
        },
        take: 10
      });

      const topFailureReasons = failureReasons.map(item => ({
        reason: item.failureReason || 'Unknown',
        count: item._count.failureReason
      }));

      // Get device statistics
      const deviceStats = await prisma.deviceAccess.findMany({
        where: {
          lastSeen: { gte: last7Days }
        },
        select: {
          deviceId: true,
          accessCount: true,
          lastSeen: true
        },
        orderBy: {
          accessCount: 'desc'
        },
        take: 20
      });

      return {
        activeSessions,
        blockedClients,
        recentFailedAttempts,
        suspiciousDevices: suspiciousDevices.length,
        topFailureReasons,
        deviceStats
      };
    } catch (error) {
      console.error('Error generating security report:', error);
      throw error;
    }
  }

  // Check for security anomalies
  static async checkSecurityAnomalies(): Promise<{
    alerts: Array<{
      type: string;
      severity: 'low' | 'medium' | 'high';
      message: string;
      count: number;
    }>;
  }> {
    try {
      const alerts: Array<{
        type: string;
        severity: 'low' | 'medium' | 'high';
        message: string;
        count: number;
      }> = [];

      const now = new Date();
      const last1Hour = new Date(now.getTime() - 60 * 60 * 1000);
      const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      // Check for high failure rates
      const recentFailures = await prisma.securityAttempt.count({
        where: {
          success: false,
          createdAt: { gte: last1Hour }
        }
      });

      if (recentFailures > 50) {
        alerts.push({
          type: 'high_failure_rate',
          severity: 'high',
          message: `High number of failed authentication attempts in the last hour`,
          count: recentFailures
        });
      } else if (recentFailures > 20) {
        alerts.push({
          type: 'elevated_failure_rate',
          severity: 'medium',
          message: `Elevated number of failed authentication attempts in the last hour`,
          count: recentFailures
        });
      }

      // Check for multiple clients blocked
      const blockedClients = await prisma.client.count({
        where: {
          isBlocked: true,
          blockedUntil: { gte: now }
        }
      });

      if (blockedClients > 10) {
        alerts.push({
          type: 'multiple_blocked_clients',
          severity: 'high',
          message: `Multiple clients are currently blocked`,
          count: blockedClients
        });
      }

      // Check for suspicious IP activity
      const suspiciousIPs = await prisma.securityAttempt.groupBy({
        by: ['ipAddress'],
        where: {
          success: false,
          createdAt: { gte: last24Hours }
        },
        having: {
          ipAddress: {
            _count: { gte: 20 }
          }
        }
      });

      if (suspiciousIPs.length > 0) {
        alerts.push({
          type: 'suspicious_ip_activity',
          severity: 'medium',
          message: `IP addresses with high failure rates detected`,
          count: suspiciousIPs.length
        });
      }

      // Check for rapid session creation
      const recentSessions = await prisma.clientSession.count({
        where: {
          createdAt: { gte: last1Hour }
        }
      });

      if (recentSessions > 100) {
        alerts.push({
          type: 'rapid_session_creation',
          severity: 'medium',
          message: `High number of new sessions created in the last hour`,
          count: recentSessions
        });
      }

      return { alerts };
    } catch (error) {
      console.error('Error checking security anomalies:', error);
      throw error;
    }
  }

  // Reset client security status (admin function)
  static async resetClientSecurity(clientId: string): Promise<void> {
    try {
      await prisma.$transaction(async (tx) => {
        // Unblock client
        await tx.client.update({
          where: { id: clientId },
          data: {
            isBlocked: false,
            blockedUntil: null,
            faceRetryCount: 0,
          }
        });

        // Deactivate all sessions
        await tx.clientSession.updateMany({
          where: { clientId },
          data: { isActive: false }
        });

        // Clear device access (force re-approval)
        await tx.deviceAccess.updateMany({
          where: { clientId },
          data: { isApproved: false }
        });
      });

      console.log(`Security status reset for client: ${clientId}`);
    } catch (error) {
      console.error('Error resetting client security:', error);
      throw error;
    }
  }
}
