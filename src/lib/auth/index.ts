import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { NextRequest } from 'next/server';
import { prisma } from '@/lib/db';
import { User } from '@/types';

const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';

export interface JWTPayload {
  userId: string;
  email: string;
  role: 'admin' | 'studio' | 'client';
  iat?: number;
  exp?: number;
}

// Hash password
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
}

// Verify password
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

// Generate JWT token
export function generateToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: '7d', // Token expires in 7 days
  });
}

// Verify JWT token
export function verifyToken(token: string): JWTPayload | null {
  try {
    return jwt.verify(token, JWT_SECRET) as JWTPayload;
  } catch {
    return null;
  }
}

// Get user from token
export async function getUserFromToken(token: string): Promise<User | null> {
  const payload = verifyToken(token);
  if (!payload) return null;

  try {
    let user = null;
    
    switch (payload.role) {
      case 'admin':
        user = await prisma.admin.findUnique({
          where: { id: payload.userId },
        });
        break;
      case 'studio':
        user = await prisma.studio.findUnique({
          where: { id: payload.userId },
          include: {
            subscription: {
              include: {
                plan: true,
              },
            },
          },
        });
        break;
      case 'client':
        user = await prisma.client.findUnique({
          where: { id: payload.userId },
          include: {
            studio: true,
          },
        });
        break;
    }

    if (!user) return null;

    return {
      ...user,
      role: payload.role,
    } as User;
  } catch (error) {
    console.error('Error fetching user from token:', error);
    return null;
  }
}

// Extract token from request
export function getTokenFromRequest(request: NextRequest): string | null {
  // Check Authorization header
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // Check cookies
  const tokenCookie = request.cookies.get('token');
  if (tokenCookie) {
    return tokenCookie.value;
  }

  return null;
}

// Middleware helper to authenticate requests
export async function authenticateRequest(request: NextRequest): Promise<User | null> {
  const token = getTokenFromRequest(request);
  if (!token) return null;

  return getUserFromToken(token);
}

// Role-based authorization
export function authorizeRole(user: User | null, allowedRoles: string[]): boolean {
  if (!user) return false;
  return allowedRoles.includes(user.role);
}

// Generate secure random string for QR codes and access links
export function generateSecureString(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Login function for admin and studio users (clients use separate access system)
export async function loginUser(email: string, password: string, userType: 'admin' | 'studio') {
  try {
    let user = null;

    switch (userType) {
      case 'admin':
        user = await prisma.admin.findUnique({
          where: { email },
        });
        break;
      case 'studio':
        user = await prisma.studio.findUnique({
          where: { email },
          include: {
            subscription: {
              include: {
                plan: true,
              },
            },
          },
        });
        break;
      default:
        return { success: false, message: 'Invalid user type' };
    }

    if (!user) {
      return { success: false, message: 'User not found' };
    }

    const isPasswordValid = await verifyPassword(password, user.password);
    if (!isPasswordValid) {
      return { success: false, message: 'Invalid password' };
    }

    // Check if studio is approved and active
    if (userType === 'studio' && (!user.isApproved || !user.isActive)) {
      return { success: false, message: 'Studio account is not approved or inactive' };
    }

    const token = generateToken({
      userId: user.id,
      email: user.email,
      role: userType,
    });

    // No additional updates needed for admin/studio login

    return {
      success: true,
      token,
      user: {
        ...user,
        role: userType,
      },
    };
  } catch (error) {
    console.error('Login error:', error);
    return { success: false, message: 'Login failed' };
  }
}

// Register studio function
export async function registerStudio(data: {
  name: string;
  email: string;
  password: string;
  businessName?: string;
  phone?: string;
}) {
  try {
    // Check if email already exists
    const existingStudio = await prisma.studio.findUnique({
      where: { email: data.email },
    });

    if (existingStudio) {
      return { success: false, message: 'Email already registered' };
    }

    const hashedPassword = await hashPassword(data.password);

    const studio = await prisma.studio.create({
      data: {
        name: data.name,
        email: data.email,
        password: hashedPassword,
        businessName: data.businessName,
        phone: data.phone,
        isActive: true,
        isApproved: false, // Requires admin approval
      },
    });

    return {
      success: true,
      message: 'Studio registered successfully. Awaiting admin approval.',
      studio: {
        id: studio.id,
        name: studio.name,
        email: studio.email,
        businessName: studio.businessName,
      },
    };
  } catch (error) {
    console.error('Registration error:', error);
    return { success: false, message: 'Registration failed' };
  }
}
