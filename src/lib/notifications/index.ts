import nodemailer from 'nodemailer';

// Email configuration
const emailTransporter = nodemailer.createTransporter({
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
});

// WhatsApp configuration (using Twilio or similar service)
const WHATSAPP_API_URL = process.env.WHATSAPP_API_URL;
const WHATSAPP_API_KEY = process.env.WHATSAPP_API_KEY;

export interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  attachments?: Array<{
    filename: string;
    path: string;
  }>;
}

export interface WhatsAppOptions {
  to: string;
  message: string;
  mediaUrl?: string;
}

export class NotificationService {
  // Send email notification
  static async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      await emailTransporter.sendMail({
        from: process.env.SMTP_USER,
        to: options.to,
        subject: options.subject,
        html: options.html,
        attachments: options.attachments,
      });
      
      console.log('Email sent successfully to:', options.to);
      return true;
    } catch (error) {
      console.error('Email sending failed:', error);
      return false;
    }
  }

  // Send WhatsApp notification
  static async sendWhatsApp(options: WhatsAppOptions): Promise<boolean> {
    try {
      if (!WHATSAPP_API_URL || !WHATSAPP_API_KEY) {
        console.warn('WhatsApp API not configured');
        return false;
      }

      const response = await fetch(WHATSAPP_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${WHATSAPP_API_KEY}`,
        },
        body: JSON.stringify({
          to: options.to,
          message: options.message,
          media_url: options.mediaUrl,
        }),
      });

      if (response.ok) {
        console.log('WhatsApp message sent successfully to:', options.to);
        return true;
      } else {
        console.error('WhatsApp sending failed:', await response.text());
        return false;
      }
    } catch (error) {
      console.error('WhatsApp sending failed:', error);
      return false;
    }
  }

  // Send client gallery notification
  static async notifyClientGalleryUpdate(
    clientEmail: string,
    clientPhone: string,
    studioName: string,
    photoCount: number,
    accessLink: string
  ): Promise<void> {
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #3b82f6;">New Photos Available!</h2>
        <p>Hello,</p>
        <p>Great news! ${studioName} has uploaded ${photoCount} new photos to your gallery.</p>
        <p>Click the link below to view and download your photos:</p>
        <a href="${accessLink}" style="background-color: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 16px 0;">
          View My Photos
        </a>
        <p>If the button doesn't work, copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #6b7280;">${accessLink}</p>
        <hr style="margin: 24px 0; border: none; border-top: 1px solid #e5e7eb;">
        <p style="color: #6b7280; font-size: 14px;">
          This is an automated message from ${studioName}. Please do not reply to this email.
        </p>
      </div>
    `;

    // Send email notification
    await this.sendEmail({
      to: clientEmail,
      subject: `New Photos Available - ${studioName}`,
      html: emailHtml,
    });

    // Send WhatsApp notification if phone number is provided
    if (clientPhone) {
      const whatsappMessage = `🎉 New photos available!\n\n${studioName} has uploaded ${photoCount} new photos to your gallery.\n\nView your photos: ${accessLink}`;
      
      await this.sendWhatsApp({
        to: clientPhone,
        message: whatsappMessage,
      });
    }
  }

  // Send studio approval notification
  static async notifyStudioApproval(
    studioEmail: string,
    studioName: string,
    isApproved: boolean
  ): Promise<void> {
    const subject = isApproved 
      ? 'Studio Account Approved - Welcome to Studio ERP!'
      : 'Studio Account Application Update';

    const emailHtml = isApproved ? `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #10b981;">Welcome to Studio ERP!</h2>
        <p>Hello ${studioName},</p>
        <p>Congratulations! Your studio account has been approved and is now active.</p>
        <p>You can now:</p>
        <ul>
          <li>Upload and manage photos</li>
          <li>Add and manage clients</li>
          <li>Use face recognition features</li>
          <li>Access analytics and reports</li>
        </ul>
        <a href="${process.env.APP_URL}/auth/login" style="background-color: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 16px 0;">
          Login to Your Dashboard
        </a>
        <p>If you have any questions, please don't hesitate to contact our support team.</p>
        <p>Welcome aboard!</p>
        <hr style="margin: 24px 0; border: none; border-top: 1px solid #e5e7eb;">
        <p style="color: #6b7280; font-size: 14px;">
          Studio ERP Team
        </p>
      </div>
    ` : `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #ef4444;">Studio Account Application Update</h2>
        <p>Hello ${studioName},</p>
        <p>Thank you for your interest in Studio ERP. Unfortunately, we cannot approve your studio account at this time.</p>
        <p>This could be due to:</p>
        <ul>
          <li>Incomplete information in your application</li>
          <li>Verification requirements not met</li>
          <li>Other administrative reasons</li>
        </ul>
        <p>If you believe this is an error or would like to reapply, please contact our support team.</p>
        <hr style="margin: 24px 0; border: none; border-top: 1px solid #e5e7eb;">
        <p style="color: #6b7280; font-size: 14px;">
          Studio ERP Team
        </p>
      </div>
    `;

    await this.sendEmail({
      to: studioEmail,
      subject,
      html: emailHtml,
    });
  }

  // Send subscription expiry warning
  static async notifySubscriptionExpiry(
    studioEmail: string,
    studioName: string,
    daysUntilExpiry: number
  ): Promise<void> {
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #f59e0b;">Subscription Expiring Soon</h2>
        <p>Hello ${studioName},</p>
        <p>Your Studio ERP subscription will expire in ${daysUntilExpiry} days.</p>
        <p>To continue enjoying uninterrupted service, please renew your subscription:</p>
        <a href="${process.env.APP_URL}/studio/subscription" style="background-color: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 16px 0;">
          Renew Subscription
        </a>
        <p>Don't lose access to:</p>
        <ul>
          <li>Photo uploads and storage</li>
          <li>Client management</li>
          <li>Face recognition features</li>
          <li>Analytics and reports</li>
        </ul>
        <hr style="margin: 24px 0; border: none; border-top: 1px solid #e5e7eb;">
        <p style="color: #6b7280; font-size: 14px;">
          Studio ERP Team
        </p>
      </div>
    `;

    await this.sendEmail({
      to: studioEmail,
      subject: 'Subscription Expiring Soon - Studio ERP',
      html: emailHtml,
    });
  }

  // Send welcome email to new client
  static async sendClientWelcomeEmail(
    clientEmail: string,
    clientName: string,
    studioName: string,
    accessLink: string,
    qrCode: string
  ): Promise<void> {
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #3b82f6;">Welcome to Your Photo Gallery!</h2>
        <p>Hello ${clientName},</p>
        <p>${studioName} has created a secure photo gallery for you.</p>
        <p>You can access your photos using either of these methods:</p>
        
        <div style="background-color: #f3f4f6; padding: 16px; border-radius: 8px; margin: 16px 0;">
          <h3 style="margin-top: 0;">Method 1: Direct Link</h3>
          <a href="${accessLink}" style="background-color: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
            Access My Photos
          </a>
        </div>
        
        <div style="background-color: #f3f4f6; padding: 16px; border-radius: 8px; margin: 16px 0;">
          <h3 style="margin-top: 0;">Method 2: QR Code</h3>
          <p>Use this code: <strong>${qrCode}</strong></p>
          <p>Visit <a href="${process.env.APP_URL}/client/access">${process.env.APP_URL}/client/access</a> and enter the code above.</p>
        </div>
        
        <p><strong>Important:</strong> You will need the password provided by ${studioName} to access your photos.</p>
        
        <hr style="margin: 24px 0; border: none; border-top: 1px solid #e5e7eb;">
        <p style="color: #6b7280; font-size: 14px;">
          This gallery was created by ${studioName}. For support, please contact them directly.
        </p>
      </div>
    `;

    await this.sendEmail({
      to: clientEmail,
      subject: `Your Photo Gallery is Ready - ${studioName}`,
      html: emailHtml,
    });
  }
}
