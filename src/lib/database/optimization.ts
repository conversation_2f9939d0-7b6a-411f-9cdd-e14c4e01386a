import { prisma } from '@/lib/db';

export class DatabaseOptimization {
  // Optimize photo queries with proper indexing and relations
  static async getOptimizedPhotos(studioId: string, params: {
    page: number;
    limit: number;
    eventId?: string;
    isMatched?: boolean;
    isProcessed?: boolean;
    search?: string;
    sortBy?: 'uploadedAt' | 'filename' | 'size';
    sortOrder?: 'asc' | 'desc';
  }) {
    const {
      page,
      limit,
      eventId,
      isMatched,
      isProcessed,
      search,
      sortBy = 'uploadedAt',
      sortOrder = 'desc',
    } = params;

    const skip = (page - 1) * limit;

    // Build optimized where clause
    const where: any = { studioId };
    
    if (eventId) where.eventId = eventId;
    if (typeof isMatched === 'boolean') where.isMatched = isMatched;
    if (typeof isProcessed === 'boolean') where.isProcessed = isProcessed;
    
    if (search) {
      where.OR = [
        { originalName: { contains: search, mode: 'insensitive' } },
        { filename: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Use transaction for consistency
    const [photos, totalCount] = await prisma.$transaction([
      prisma.photo.findMany({
        where,
        skip,
        take: limit,
        orderBy: { [sortBy]: sortOrder },
        include: {
          event: {
            select: {
              id: true,
              name: true,
              date: true,
            },
          },
          client: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          _count: {
            select: {
              favorites: true,
              comments: true,
            },
          },
        },
      }),
      prisma.photo.count({ where }),
    ]);

    return {
      photos: photos.map(photo => ({
        ...photo,
        size: photo.size.toString(), // Convert BigInt to string
      })),
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit),
      },
    };
  }

  // Optimize client queries with proper relations
  static async getOptimizedClients(studioId: string, params: {
    page: number;
    limit: number;
    search?: string;
    status?: string;
    sortBy?: 'createdAt' | 'name' | 'lastLogin';
    sortOrder?: 'asc' | 'desc';
  }) {
    const {
      page,
      limit,
      search,
      status,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = params;

    const skip = (page - 1) * limit;

    const where: any = { studioId };
    
    if (status) where.status = status;
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [clients, totalCount] = await prisma.$transaction([
      prisma.client.findMany({
        where,
        skip,
        take: limit,
        orderBy: { [sortBy]: sortOrder },
        include: {
          _count: {
            select: {
              photos: true,
              favorites: true,
              accessLogs: true,
            },
          },
        },
      }),
      prisma.client.count({ where }),
    ]);

    return {
      clients,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit),
      },
    };
  }

  // Optimize event queries
  static async getOptimizedEvents(studioId: string, params: {
    page: number;
    limit: number;
    status?: string;
    search?: string;
    dateFrom?: Date;
    dateTo?: Date;
  }) {
    const { page, limit, status, search, dateFrom, dateTo } = params;
    const skip = (page - 1) * limit;

    const where: any = { studioId };
    
    if (status) where.status = status;
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { location: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (dateFrom || dateTo) {
      where.date = {};
      if (dateFrom) where.date.gte = dateFrom;
      if (dateTo) where.date.lte = dateTo;
    }

    const [events, totalCount] = await prisma.$transaction([
      prisma.event.findMany({
        where,
        skip,
        take: limit,
        orderBy: { date: 'desc' },
        include: {
          _count: {
            select: {
              photos: true,
            },
          },
        },
      }),
      prisma.event.count({ where }),
    ]);

    return {
      events,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit),
      },
    };
  }

  // Batch operations for better performance
  static async batchUpdatePhotos(photoIds: string[], studioId: string, updateData: any) {
    return await prisma.$transaction(async (tx) => {
      // Verify all photos belong to the studio
      const photoCount = await tx.photo.count({
        where: {
          id: { in: photoIds },
          studioId,
        },
      });

      if (photoCount !== photoIds.length) {
        throw new Error('Some photos do not belong to this studio');
      }

      // Perform batch update
      const result = await tx.photo.updateMany({
        where: {
          id: { in: photoIds },
          studioId,
        },
        data: updateData,
      });

      return result;
    });
  }

  // Batch delete with cleanup
  static async batchDeletePhotos(photoIds: string[], studioId: string) {
    return await prisma.$transaction(async (tx) => {
      // Get photos to calculate storage reduction
      const photos = await tx.photo.findMany({
        where: {
          id: { in: photoIds },
          studioId,
        },
        select: {
          id: true,
          size: true,
        },
      });

      if (photos.length === 0) {
        return { deletedCount: 0, storageFreed: BigInt(0) };
      }

      const totalSize = photos.reduce((sum, photo) => sum + photo.size, BigInt(0));

      // Delete related records first
      await tx.favorite.deleteMany({
        where: { photoId: { in: photoIds } },
      });

      await tx.comment.deleteMany({
        where: { photoId: { in: photoIds } },
      });

      // Delete photos
      const deleteResult = await tx.photo.deleteMany({
        where: {
          id: { in: photoIds },
          studioId,
        },
      });

      // Update studio storage usage
      await tx.studio.update({
        where: { id: studioId },
        data: {
          storageUsed: {
            decrement: totalSize,
          },
        },
      });

      return {
        deletedCount: deleteResult.count,
        storageFreed: totalSize,
      };
    });
  }

  // Optimize analytics queries
  static async getOptimizedAnalytics(studioId: string, dateFrom: Date, dateTo: Date) {
    const [
      totalPhotos,
      processedPhotos,
      matchedPhotos,
      totalClients,
      activeClients,
      totalEvents,
      storageStats,
    ] = await prisma.$transaction([
      // Total photos
      prisma.photo.count({
        where: {
          studioId,
          uploadedAt: {
            gte: dateFrom,
            lte: dateTo,
          },
        },
      }),

      // Processed photos
      prisma.photo.count({
        where: {
          studioId,
          isProcessed: true,
          uploadedAt: {
            gte: dateFrom,
            lte: dateTo,
          },
        },
      }),

      // Matched photos
      prisma.photo.count({
        where: {
          studioId,
          isMatched: true,
          uploadedAt: {
            gte: dateFrom,
            lte: dateTo,
          },
        },
      }),

      // Total clients
      prisma.client.count({
        where: {
          studioId,
          createdAt: {
            gte: dateFrom,
            lte: dateTo,
          },
        },
      }),

      // Active clients
      prisma.client.count({
        where: {
          studioId,
          accessLogs: {
            some: {
              createdAt: {
                gte: dateFrom,
                lte: dateTo,
              },
            },
          },
        },
      }),

      // Total events
      prisma.event.count({
        where: {
          studioId,
          date: {
            gte: dateFrom,
            lte: dateTo,
          },
        },
      }),

      // Storage statistics
      prisma.studio.findUnique({
        where: { id: studioId },
        select: {
          storageUsed: true,
          subscription: {
            include: {
              plan: {
                select: {
                  storageLimit: true,
                },
              },
            },
          },
        },
      }),
    ]);

    return {
      photos: {
        total: totalPhotos,
        processed: processedPhotos,
        matched: matchedPhotos,
      },
      clients: {
        total: totalClients,
        active: activeClients,
      },
      events: {
        total: totalEvents,
      },
      storage: storageStats,
    };
  }

  // Database maintenance operations
  static async performMaintenance() {
    return await prisma.$transaction(async (tx) => {
      // Clean up orphaned favorites (favorites without photos)
      const orphanedFavorites = await tx.favorite.deleteMany({
        where: {
          photoId: {
            notIn: await tx.photo.findMany({ select: { id: true } }).then(photos => photos.map(p => p.id)),
          },
        },
      });

      // Clean up orphaned comments (comments without photos)
      const orphanedComments = await tx.comment.deleteMany({
        where: {
          photoId: {
            notIn: await tx.photo.findMany({ select: { id: true } }).then(photos => photos.map(p => p.id)),
          },
        },
      });

      // Database statistics are handled automatically by Prisma
      return {
        orphanedFavoritesDeleted: orphanedFavorites.count,
        orphanedCommentsDeleted: orphanedComments.count,
      };
    });
  }

  // Connection pool optimization
  static async optimizeConnections() {
    // Return basic connection info since $metrics is not available in all Prisma versions
    return {
      activeConnections: 0,
      idleConnections: 0,
      totalConnections: 0,
      message: 'Connection metrics not available',
    };
  }
}
