import { prisma } from '@/lib/db';
import { Prisma } from '@prisma/client';

export class DatabaseTransactions {
  // User registration transaction
  static async registerStudio(userData: {
    name: string;
    email: string;
    password: string;
    businessName?: string;
    phone?: string;
  }, ipAddress: string, userAgent: string) {
    return await prisma.$transaction(async (tx) => {
      // Check if email already exists
      const existingUser = await tx.studio.findUnique({
        where: { email: userData.email },
      });

      if (existingUser) {
        throw new Error('Email already registered');
      }

      // Create studio
      const studio = await tx.studio.create({
        data: {
          name: userData.name,
          email: userData.email,
          password: userData.password,
          businessName: userData.businessName,
          phone: userData.phone,
          status: 'pending',
          storageUsed: BigInt(0),
        },
      });

      // Log registration activity
      await tx.activityLog.create({
        data: {
          userId: studio.id,
          userType: 'studio',
          action: 'register',
          details: 'Studio registration',
          ipAddress,
          userAgent,
        },
      });

      return studio;
    });
  }

  // Photo upload transaction
  static async uploadPhotos(studioId: string, photoData: Array<{
    filename: string;
    originalName: string;
    size: bigint;
    path: string;
    eventId?: string;
  }>) {
    return await prisma.$transaction(async (tx) => {
      // Calculate total size
      const totalSize = photoData.reduce((sum, photo) => sum + photo.size, BigInt(0));

      // Check storage limit
      const studio = await tx.studio.findUnique({
        where: { id: studioId },
        include: {
          subscription: {
            include: {
              plan: true,
            },
          },
        },
      });

      if (!studio) {
        throw new Error('Studio not found');
      }

      const storageLimit = studio.subscription?.plan?.storageLimit || BigInt(1024 * 1024 * 1024); // 1GB default
      const newStorageUsed = studio.storageUsed + totalSize;

      if (newStorageUsed > storageLimit) {
        throw new Error('Storage limit exceeded');
      }

      // Create photos
      const photos = await Promise.all(
        photoData.map(photo =>
          tx.photo.create({
            data: {
              filename: photo.filename,
              originalName: photo.originalName,
              size: photo.size,
              path: photo.path,
              studioId,
              eventId: photo.eventId,
              isProcessed: false,
              isMatched: false,
            },
          })
        )
      );

      // Update studio storage usage
      await tx.studio.update({
        where: { id: studioId },
        data: {
          storageUsed: newStorageUsed,
        },
      });

      return {
        photos,
        totalSize,
        newStorageUsed,
      };
    });
  }

  // Client creation transaction
  static async createClient(studioId: string, clientData: {
    name: string;
    email: string;
    password: string;
    phone?: string;
  }, createdBy: string, ipAddress: string, userAgent: string) {
    return await prisma.$transaction(async (tx) => {
      // Check if client email already exists for this studio
      const existingClient = await tx.client.findFirst({
        where: {
          email: clientData.email,
          studioId,
        },
      });

      if (existingClient) {
        throw new Error('Client email already exists for this studio');
      }

      // Create client
      const client = await tx.client.create({
        data: {
          name: clientData.name,
          email: clientData.email,
          password: clientData.password,
          phone: clientData.phone,
          studioId,
          status: 'active',
        },
      });

      // Log client creation
      await tx.activityLog.create({
        data: {
          userId: createdBy,
          userType: 'studio',
          action: 'create_client',
          details: `Created client: ${client.name}`,
          ipAddress,
          userAgent,
        },
      });

      return client;
    });
  }

  // Event creation transaction
  static async createEvent(studioId: string, eventData: {
    name: string;
    description?: string;
    date: Date;
    location?: string;
    eventType?: string;
  }, createdBy: string, ipAddress: string, userAgent: string) {
    return await prisma.$transaction(async (tx) => {
      // Create event
      const event = await tx.event.create({
        data: {
          name: eventData.name,
          description: eventData.description,
          date: eventData.date,
          location: eventData.location,
          eventType: eventData.eventType,
          studioId,
          status: 'active',
        },
      });

      // Log event creation
      await tx.activityLog.create({
        data: {
          userId: createdBy,
          userType: 'studio',
          action: 'create_event',
          details: `Created event: ${event.name}`,
          ipAddress,
          userAgent,
        },
      });

      return event;
    });
  }

  // Face recognition processing transaction
  static async processFaceRecognition(studioId: string, photoIds: string[]) {
    return await prisma.$transaction(async (tx) => {
      // Verify photos belong to studio
      const photos = await tx.photo.findMany({
        where: {
          id: { in: photoIds },
          studioId,
        },
      });

      if (photos.length !== photoIds.length) {
        throw new Error('Some photos do not belong to this studio');
      }

      // Update photos as processed
      await tx.photo.updateMany({
        where: {
          id: { in: photoIds },
          studioId,
        },
        data: {
          isProcessed: true,
          processedAt: new Date(),
        },
      });

      // Here you would integrate with face recognition service
      // For now, we'll simulate the process

      return {
        processedCount: photos.length,
        message: `${photos.length} photos processed for face recognition`,
      };
    });
  }

  // Billing transaction
  static async processBilling(studioId: string, billingData: {
    amount: number;
    description: string;
    type: 'subscription' | 'storage' | 'addon';
  }) {
    return await prisma.$transaction(async (tx) => {
      // Create billing record
      const billing = await tx.billing.create({
        data: {
          studioId,
          amount: billingData.amount,
          description: billingData.description,
          type: billingData.type,
          status: 'pending',
          dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        },
      });

      // Update studio billing status if needed
      if (billingData.type === 'subscription') {
        await tx.studio.update({
          where: { id: studioId },
          data: {
            lastBillingDate: new Date(),
          },
        });
      }

      return billing;
    });
  }

  // Subscription update transaction
  static async updateSubscription(studioId: string, planId: string, paymentData: any) {
    return await prisma.$transaction(async (tx) => {
      // Get current subscription
      const currentSubscription = await tx.subscription.findFirst({
        where: { studioId },
      });

      // Get new plan
      const newPlan = await tx.subscriptionPlan.findUnique({
        where: { id: planId },
      });

      if (!newPlan) {
        throw new Error('Subscription plan not found');
      }

      // Update or create subscription
      const subscription = await tx.subscription.upsert({
        where: {
          studioId,
        },
        update: {
          planId,
          status: 'active',
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        },
        create: {
          studioId,
          planId,
          status: 'active',
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        },
      });

      // Create payment record
      await tx.payment.create({
        data: {
          studioId,
          subscriptionId: subscription.id,
          amount: newPlan.price,
          status: 'completed',
          paymentMethod: paymentData.method || 'card',
          transactionId: paymentData.transactionId,
        },
      });

      return subscription;
    });
  }

  // Data cleanup transaction
  static async cleanupData(studioId: string, options: {
    deleteOldPhotos?: boolean;
    deleteInactiveClients?: boolean;
    daysOld?: number;
  }) {
    return await prisma.$transaction(async (tx) => {
      const { deleteOldPhotos, deleteInactiveClients, daysOld = 90 } = options;
      const cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);

      let deletedPhotos = 0;
      let deletedClients = 0;
      let storageFreed = BigInt(0);

      if (deleteOldPhotos) {
        // Get old photos
        const oldPhotos = await tx.photo.findMany({
          where: {
            studioId,
            uploadedAt: { lt: cutoffDate },
            isMatched: false,
          },
          select: { id: true, size: true },
        });

        if (oldPhotos.length > 0) {
          const photoIds = oldPhotos.map(p => p.id);
          storageFreed = oldPhotos.reduce((sum, photo) => sum + photo.size, BigInt(0));

          // Delete related records
          await tx.favorite.deleteMany({
            where: { photoId: { in: photoIds } },
          });

          await tx.comment.deleteMany({
            where: { photoId: { in: photoIds } },
          });

          // Delete photos
          const deleteResult = await tx.photo.deleteMany({
            where: { id: { in: photoIds } },
          });

          deletedPhotos = deleteResult.count;

          // Update storage usage
          await tx.studio.update({
            where: { id: studioId },
            data: {
              storageUsed: { decrement: storageFreed },
            },
          });
        }
      }

      if (deleteInactiveClients) {
        const deleteResult = await tx.client.deleteMany({
          where: {
            studioId,
            lastLogin: { lt: cutoffDate },
            status: 'inactive',
          },
        });

        deletedClients = deleteResult.count;
      }

      return {
        deletedPhotos,
        deletedClients,
        storageFreed,
      };
    });
  }
}
