// Export database utilities
export * from './optimization';
export * from './transactions';

// Database connection utilities
import { prisma } from '@/lib/db';

export class DatabaseUtils {
  // Test database connection
  static async testConnection(): Promise<boolean> {
    try {
      await prisma.studio.findFirst();
      return true;
    } catch (error) {
      console.error('Database connection test failed:', error);
      return false;
    }
  }

  // Get database statistics
  static async getDatabaseStats() {
    try {
      const [
        studioCount,
        clientCount,
        photoCount,
        eventCount,
        totalStorage,
      ] = await Promise.all([
        prisma.studio.count(),
        prisma.client.count(),
        prisma.photo.count(),
        prisma.event.count(),
        prisma.photo.aggregate({
          _sum: { size: true },
        }),
      ]);

      return {
        studios: studioCount,
        clients: clientCount,
        photos: photoCount,
        events: eventCount,
        totalStorage: totalStorage._sum.size?.toString() || '0',
      };
    } catch (error) {
      console.error('Error getting database stats:', error);
      throw error;
    }
  }

  // Execute raw query safely - deprecated, use Prisma built-in methods instead
  static async executeRawQuery(query: string, params: any[] = []) {
    console.warn('Raw queries are deprecated. Use Prisma built-in methods instead.');
    throw new Error('Raw queries are not supported. Use Prisma built-in methods.');
  }

  // Backup database (placeholder for actual backup logic)
  static async backupDatabase() {
    // In a real implementation, this would trigger a database backup
    // For now, we'll just return a success message
    return {
      success: true,
      message: 'Database backup initiated',
      timestamp: new Date().toISOString(),
    };
  }

  // Optimize database performance
  static async optimizeDatabase() {
    try {
      // Use Prisma's built-in optimization
      // This will be handled by the database automatically

      return {
        success: true,
        message: 'Database optimization completed',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Database optimization failed:', error);
      throw error;
    }
  }

  // Clean up orphaned records
  static async cleanupOrphanedRecords() {
    try {
      const results = await prisma.$transaction(async (tx) => {
        // Clean up orphaned favorites (favorites without photos)
        const orphanedFavorites = await tx.favorite.deleteMany({
          where: {
            photoId: {
              notIn: await tx.photo.findMany({ select: { id: true } }).then(photos => photos.map(p => p.id)),
            },
          },
        });

        // Clean up orphaned comments (comments without photos)
        const orphanedComments = await tx.comment.deleteMany({
          where: {
            photoId: {
              notIn: await tx.photo.findMany({ select: { id: true } }).then(photos => photos.map(p => p.id)),
            },
          },
        });

        // Clean up orphaned access logs (access logs for deleted clients)
        const orphanedAccessLogs = await tx.accessLog.deleteMany({
          where: {
            clientId: {
              notIn: await tx.client.findMany({ select: { id: true } }).then(clients => clients.map(c => c.id)),
            },
          },
        });

        return {
          orphanedFavorites: orphanedFavorites.count,
          orphanedComments: orphanedComments.count,
          orphanedAccessLogs: orphanedAccessLogs.count,
        };
      });

      return {
        success: true,
        message: 'Orphaned records cleaned up',
        results,
      };
    } catch (error) {
      console.error('Cleanup failed:', error);
      throw error;
    }
  }

  // Get slow queries (placeholder for monitoring)
  static async getSlowQueries() {
    // In a real implementation, this would query the database's slow query log
    // For now, we'll return a placeholder
    return {
      queries: [],
      message: 'Slow query monitoring not implemented',
    };
  }

  // Monitor connection pool
  static async getConnectionPoolStatus() {
    try {
      // Get Prisma metrics if available
      const metrics = await prisma.$metrics.json();
      
      return {
        activeConnections: metrics.counters.find(c => c.key === 'db.client.connections.active')?.value || 0,
        idleConnections: metrics.counters.find(c => c.key === 'db.client.connections.idle')?.value || 0,
        totalConnections: metrics.counters.find(c => c.key === 'db.client.connections.total')?.value || 0,
      };
    } catch (error) {
      console.error('Error getting connection pool status:', error);
      return {
        activeConnections: 0,
        idleConnections: 0,
        totalConnections: 0,
        error: 'Unable to get connection pool status',
      };
    }
  }

  // Validate data integrity
  static async validateDataIntegrity() {
    try {
      const issues = [];

      // Check for photos without studios
      const photosWithoutStudio = await prisma.photo.count({
        where: { studio: null },
      });
      if (photosWithoutStudio > 0) {
        issues.push(`${photosWithoutStudio} photos without associated studio`);
      }

      // Check for clients without studios
      const clientsWithoutStudio = await prisma.client.count({
        where: { studio: null },
      });
      if (clientsWithoutStudio > 0) {
        issues.push(`${clientsWithoutStudio} clients without associated studio`);
      }

      // Check for events without studios
      const eventsWithoutStudio = await prisma.event.count({
        where: { studio: null },
      });
      if (eventsWithoutStudio > 0) {
        issues.push(`${eventsWithoutStudio} events without associated studio`);
      }

      return {
        isValid: issues.length === 0,
        issues,
        checkedAt: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Data integrity validation failed:', error);
      throw error;
    }
  }

  // Get table sizes using Prisma aggregations
  static async getTableSizes() {
    try {
      const [
        studioCount,
        clientCount,
        photoCount,
        eventCount,
        photoSize,
      ] = await Promise.all([
        prisma.studio.count(),
        prisma.client.count(),
        prisma.photo.count(),
        prisma.event.count(),
        prisma.photo.aggregate({
          _sum: { size: true },
        }),
      ]);

      return [
        { tablename: 'Studio', count: studioCount, size: 'N/A' },
        { tablename: 'Client', count: clientCount, size: 'N/A' },
        { tablename: 'Photo', count: photoCount, size: photoSize._sum.size?.toString() || '0' },
        { tablename: 'Event', count: eventCount, size: 'N/A' },
      ];
    } catch (error) {
      console.error('Error getting table sizes:', error);
      return [];
    }
  }

  // Create database indexes for optimization
  static async createOptimizationIndexes() {
    try {
      // Create indexes for commonly queried fields
      const indexQueries = [
        'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_photos_studio_uploaded ON "Photo" ("studioId", "uploadedAt")',
        'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_photos_event_matched ON "Photo" ("eventId", "isMatched")',
        'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_clients_studio_status ON "Client" ("studioId", "status")',
        'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_events_studio_date ON "Event" ("studioId", "date")',
        'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activity_logs_user_action ON "ActivityLog" ("userId", "action")',
      ];

      // Index creation is handled automatically by Prisma migrations
      console.log('Index creation is handled by Prisma migrations');

      return {
        success: true,
        message: 'Optimization indexes created',
        indexCount: indexQueries.length,
      };
    } catch (error) {
      console.error('Error creating optimization indexes:', error);
      throw error;
    }
  }
}
