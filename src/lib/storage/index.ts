import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import sharp from 'sharp';

// AWS S3 Configuration
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },
});

const BUCKET_NAME = process.env.AWS_S3_BUCKET || 'studio-erp-photos';

export interface UploadOptions {
  quality?: 'original' | 'high' | 'medium' | 'low';
  generateThumbnail?: boolean;
  watermark?: boolean;
}

export interface UploadResult {
  key: string;
  url: string;
  thumbnailKey?: string;
  thumbnailUrl?: string;
  size: number;
  mimeType: string;
}

export class StorageService {
  // Upload file to S3 with quality options
  static async uploadFile(
    file: Buffer,
    filename: string,
    mimeType: string,
    studioId: string,
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    const { quality = 'original', generateThumbnail = true, watermark = false } = options;

    // Process image based on quality
    let processedBuffer = file;
    let processedMimeType = mimeType;

    if (mimeType.startsWith('image/')) {
      processedBuffer = await this.processImage(file, quality, watermark);
      processedMimeType = 'image/jpeg'; // Convert all to JPEG for consistency
    }

    // Generate unique key
    const timestamp = Date.now();
    const key = `studios/${studioId}/photos/${timestamp}-${filename}`;

    // Upload main file
    await s3Client.send(new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
      Body: processedBuffer,
      ContentType: processedMimeType,
      Metadata: {
        studioId,
        originalName: filename,
        quality,
      },
    }));

    const url = await this.getSignedUrl(key);
    
    const result: UploadResult = {
      key,
      url,
      size: processedBuffer.length,
      mimeType: processedMimeType,
    };

    // Generate thumbnail if requested
    if (generateThumbnail && mimeType.startsWith('image/')) {
      const thumbnailBuffer = await this.generateThumbnail(file);
      const thumbnailKey = `studios/${studioId}/thumbnails/${timestamp}-thumb-${filename}`;

      await s3Client.send(new PutObjectCommand({
        Bucket: BUCKET_NAME,
        Key: thumbnailKey,
        Body: thumbnailBuffer,
        ContentType: 'image/jpeg',
        Metadata: {
          studioId,
          originalName: filename,
          type: 'thumbnail',
        },
      }));

      result.thumbnailKey = thumbnailKey;
      result.thumbnailUrl = await this.getSignedUrl(thumbnailKey);
    }

    return result;
  }

  // Process image based on quality settings
  private static async processImage(
    buffer: Buffer,
    quality: string,
    watermark: boolean = false
  ): Promise<Buffer> {
    let sharpInstance = sharp(buffer);

    // Apply quality settings
    switch (quality) {
      case 'low':
        sharpInstance = sharpInstance.resize(800, 600, { fit: 'inside', withoutEnlargement: true });
        break;
      case 'medium':
        sharpInstance = sharpInstance.resize(1200, 900, { fit: 'inside', withoutEnlargement: true });
        break;
      case 'high':
        sharpInstance = sharpInstance.resize(1920, 1440, { fit: 'inside', withoutEnlargement: true });
        break;
      case 'original':
        // Keep original size
        break;
    }

    // Apply watermark if requested
    if (watermark) {
      // In a real implementation, you would overlay a watermark image
      // For now, we'll just add a text watermark
      const watermarkSvg = `
        <svg width="200" height="50">
          <text x="10" y="30" font-family="Arial" font-size="20" fill="rgba(255,255,255,0.5)">
            Studio ERP
          </text>
        </svg>
      `;
      
      sharpInstance = sharpInstance.composite([{
        input: Buffer.from(watermarkSvg),
        gravity: 'southeast',
      }]);
    }

    // Convert to JPEG with appropriate quality
    const jpegQuality = quality === 'low' ? 60 : quality === 'medium' ? 80 : 90;
    
    return sharpInstance
      .jpeg({ quality: jpegQuality })
      .toBuffer();
  }

  // Generate thumbnail
  private static async generateThumbnail(buffer: Buffer): Promise<Buffer> {
    return sharp(buffer)
      .resize(300, 300, { fit: 'cover' })
      .jpeg({ quality: 80 })
      .toBuffer();
  }

  // Get signed URL for file access
  static async getSignedUrl(key: string, expiresIn: number = 3600): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
    });

    return getSignedUrl(s3Client, command, { expiresIn });
  }

  // Delete file from S3
  static async deleteFile(key: string): Promise<void> {
    await s3Client.send(new DeleteObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
    }));
  }

  // Get storage usage for a studio
  static async getStorageUsage(studioId: string): Promise<{
    totalSize: number;
    photoCount: number;
    thumbnailCount: number;
  }> {
    // In a real implementation, you would use S3 APIs to calculate this
    // For now, we'll return mock data
    return {
      totalSize: 0,
      photoCount: 0,
      thumbnailCount: 0,
    };
  }

  // Cleanup old files (for maintenance)
  static async cleanupOldFiles(studioId: string, olderThanDays: number = 30): Promise<number> {
    // In a real implementation, you would list and delete old files
    // This is a placeholder for the cleanup logic
    return 0;
  }
}

// Fallback local storage for development
export class LocalStorageService {
  static async uploadFile(
    file: Buffer,
    filename: string,
    mimeType: string,
    studioId: string,
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    const fs = require('fs').promises;
    const path = require('path');

    // Create directory if it doesn't exist
    const uploadDir = path.join(process.cwd(), 'public', 'uploads', studioId);
    await fs.mkdir(uploadDir, { recursive: true });

    // Save file
    const timestamp = Date.now();
    const savedFilename = `${timestamp}-${filename}`;
    const filePath = path.join(uploadDir, savedFilename);
    
    await fs.writeFile(filePath, file);

    return {
      key: `uploads/${studioId}/${savedFilename}`,
      url: `/uploads/${studioId}/${savedFilename}`,
      size: file.length,
      mimeType,
    };
  }

  static async deleteFile(key: string): Promise<void> {
    const fs = require('fs').promises;
    const path = require('path');
    
    const filePath = path.join(process.cwd(), 'public', key);
    try {
      await fs.unlink(filePath);
    } catch (error) {
      // File might not exist
      console.warn('File not found for deletion:', filePath);
    }
  }
}

// Export the appropriate service based on environment
export const storageService = process.env.NODE_ENV === 'production' && process.env.AWS_ACCESS_KEY_ID
  ? StorageService
  : LocalStorageService;
