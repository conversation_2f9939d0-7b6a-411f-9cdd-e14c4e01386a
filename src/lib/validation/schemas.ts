import { z } from 'zod';

// Common validation schemas
export const emailSchema = z.string().email('Invalid email format');
export const passwordSchema = z.string()
  .min(8, 'Password must be at least 8 characters')
  .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
    'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character');

export const phoneSchema = z.string()
  .regex(/^\+?[\d\s\-\(\)]+$/, 'Invalid phone number format')
  .optional();

export const paginationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
});

// Auth validation schemas
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
  userType: z.enum(['admin', 'studio'], {
    errorMap: () => ({ message: 'User type must be admin or studio' })
  }),
});

export const registerSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: emailSchema,
  password: passwordSchema,
  confirmPassword: z.string(),
  businessName: z.string().min(2, 'Business name must be at least 2 characters').optional(),
  phone: phoneSchema,
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export const forgotPasswordSchema = z.object({
  email: emailSchema,
  userType: z.enum(['admin', 'studio']),
});

export const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: passwordSchema,
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Client validation schemas
export const clientAccessSchema = z.object({
  accessCode: z.string().min(1, 'Access code is required'),
  password: z.string().min(1, 'Password is required'),
});

export const clientProfileUpdateSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').optional(),
  phone: phoneSchema,
  preferences: z.object({
    emailNotifications: z.boolean().optional(),
    downloadQuality: z.enum(['low', 'medium', 'high', 'original']).optional(),
  }).optional(),
});

// Studio validation schemas
export const studioProfileUpdateSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').optional(),
  businessName: z.string().min(2, 'Business name must be at least 2 characters').optional(),
  email: emailSchema.optional(),
  phone: phoneSchema,
  address: z.string().optional(),
  website: z.string().url('Invalid website URL').optional(),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  socialMedia: z.object({
    facebook: z.string().url().optional(),
    instagram: z.string().url().optional(),
    twitter: z.string().url().optional(),
  }).optional(),
  businessHours: z.object({
    monday: z.string().optional(),
    tuesday: z.string().optional(),
    wednesday: z.string().optional(),
    thursday: z.string().optional(),
    friday: z.string().optional(),
    saturday: z.string().optional(),
    sunday: z.string().optional(),
  }).optional(),
  services: z.array(z.string()).optional(),
});

export const createClientSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: emailSchema,
  password: z.string().min(6, 'Password must be at least 6 characters'),
  phone: phoneSchema,
});

export const createEventSchema = z.object({
  name: z.string().min(2, 'Event name must be at least 2 characters'),
  description: z.string().optional(),
  eventDate: z.string().datetime('Invalid date format'),
  location: z.string().optional(),
  eventType: z.string().optional(),
});

export const updateEventSchema = z.object({
  name: z.string().min(2, 'Event name must be at least 2 characters').optional(),
  description: z.string().optional(),
  eventDate: z.string().datetime('Invalid date format').optional(),
  location: z.string().optional(),
  eventType: z.string().optional(),
  status: z.enum(['active', 'completed', 'cancelled']).optional(),
});

// Photo validation schemas
export const photoUpdateSchema = z.object({
  photoId: z.string().min(1, 'Photo ID is required'),
  originalName: z.string().optional(),
  eventId: z.string().optional(),
  clientId: z.string().optional(),
  isMatched: z.boolean().optional(),
  isProcessed: z.boolean().optional(),
});

export const bulkPhotoActionSchema = z.object({
  photoIds: z.array(z.string()).min(1, 'At least one photo ID is required'),
  action: z.enum(['favorite', 'unfavorite', 'delete', 'process']),
});

// Admin validation schemas
export const createUserSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: emailSchema,
  password: passwordSchema,
  role: z.enum(['admin', 'studio']),
});

export const updateUserSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  action: z.enum(['approve', 'suspend', 'activate', 'update']),
  data: z.object({
    name: z.string().min(2).optional(),
    email: emailSchema.optional(),
    reason: z.string().optional(),
  }).optional(),
});

// System validation schemas
export const systemActionSchema = z.object({
  action: z.enum(['cleanup-storage', 'optimize-database', 'clear-cache', 'backup-database', 'update-settings']),
  parameters: z.record(z.any()).optional(),
});

// Face recognition validation schemas
export const faceRecognitionActionSchema = z.object({
  action: z.enum(['process-photos', 'process-event', 'match-client', 'rematch-all']),
  photoIds: z.array(z.string()).optional(),
  eventId: z.string().optional(),
  clientId: z.string().optional(),
});

export const faceRecognitionSettingsSchema = z.object({
  threshold: z.number().min(0).max(1).optional(),
  autoProcess: z.boolean().optional(),
  batchSize: z.number().int().min(1).max(100).optional(),
});

// Download validation schemas
export const downloadPhotoSchema = z.object({
  photoId: z.string().min(1, 'Photo ID is required'),
  quality: z.enum(['low', 'medium', 'high', 'original']).default('original'),
});

export const bulkDownloadSchema = z.object({
  photoIds: z.array(z.string()).min(1).max(50, 'Maximum 50 photos can be downloaded at once'),
  quality: z.enum(['low', 'medium', 'high', 'original']).default('original'),
  format: z.enum(['zip', 'tar']).default('zip'),
});

// Change password schema
export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: passwordSchema,
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "New passwords don't match",
  path: ["confirmPassword"],
});

// Query parameter schemas
export const photoQuerySchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  eventId: z.string().optional(),
  isMatched: z.boolean().optional(),
  isProcessed: z.boolean().optional(),
  search: z.string().optional(),
  sortBy: z.enum(['uploadedAt', 'filename', 'size']).default('uploadedAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export const userQuerySchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  userType: z.enum(['admin', 'studio']).optional(),
  status: z.enum(['active', 'inactive', 'pending', 'suspended']).optional(),
  search: z.string().optional(),
});
