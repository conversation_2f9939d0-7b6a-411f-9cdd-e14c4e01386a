// Export all validation schemas
export * from './schemas';

// Export validation middleware
export * from './middleware';

// Common validation utilities
export const ValidationUtils = {
  // Check if string is valid UUID
  isValidUUID: (str: string): boolean => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
  },

  // Check if string is valid email
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // Check if string is valid phone number
  isValidPhone: (phone: string): boolean => {
    const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
    return phoneRegex.test(phone);
  },

  // Check if string is valid URL
  isValidURL: (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },

  // Sanitize string input
  sanitizeString: (str: string): string => {
    return str.trim().replace(/[<>]/g, '');
  },

  // Validate password strength
  validatePasswordStrength: (password: string): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    if (!/[@$!%*?&]/.test(password)) {
      errors.push('Password must contain at least one special character (@$!%*?&)');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  // Validate file type
  isValidImageFile: (filename: string): boolean => {
    const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    return validExtensions.includes(extension);
  },

  // Validate file size
  isValidFileSize: (size: number, maxSizeMB: number = 10): boolean => {
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    return size <= maxSizeBytes;
  },

  // Parse and validate pagination parameters
  parsePagination: (page?: string, limit?: string) => {
    const parsedPage = Math.max(1, parseInt(page || '1', 10) || 1);
    const parsedLimit = Math.min(100, Math.max(1, parseInt(limit || '20', 10) || 20));
    
    return {
      page: parsedPage,
      limit: parsedLimit,
      skip: (parsedPage - 1) * parsedLimit,
    };
  },

  // Parse and validate sort parameters
  parseSort: (sortBy?: string, sortOrder?: string, allowedFields: string[] = []) => {
    const validSortBy = allowedFields.includes(sortBy || '') ? sortBy : allowedFields[0] || 'createdAt';
    const validSortOrder = ['asc', 'desc'].includes(sortOrder || '') ? sortOrder : 'desc';
    
    return {
      sortBy: validSortBy,
      sortOrder: validSortOrder as 'asc' | 'desc',
    };
  },

  // Parse boolean from string
  parseBoolean: (value?: string): boolean | undefined => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return undefined;
  },

  // Parse array from comma-separated string
  parseArray: (value?: string): string[] => {
    if (!value) return [];
    return value.split(',').map(item => item.trim()).filter(Boolean);
  },

  // Validate date string
  isValidDate: (dateString: string): boolean => {
    const date = new Date(dateString);
    return !isNaN(date.getTime());
  },

  // Validate date range
  isValidDateRange: (startDate: string, endDate: string): boolean => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    return !isNaN(start.getTime()) && !isNaN(end.getTime()) && start <= end;
  },

  // Clean and validate search query
  cleanSearchQuery: (query?: string): string | undefined => {
    if (!query) return undefined;
    const cleaned = query.trim().replace(/[<>]/g, '');
    return cleaned.length > 0 ? cleaned : undefined;
  },

  // Validate color hex code
  isValidHexColor: (color: string): boolean => {
    const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    return hexRegex.test(color);
  },

  // Validate social media URL
  isValidSocialMediaURL: (url: string, platform: 'facebook' | 'instagram' | 'twitter'): boolean => {
    const patterns = {
      facebook: /^https?:\/\/(www\.)?facebook\.com\/[a-zA-Z0-9.]+$/,
      instagram: /^https?:\/\/(www\.)?instagram\.com\/[a-zA-Z0-9_.]+$/,
      twitter: /^https?:\/\/(www\.)?twitter\.com\/[a-zA-Z0-9_]+$/,
    };
    
    return patterns[platform].test(url);
  },
};
