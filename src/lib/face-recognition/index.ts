import * as faceapi from 'face-api.js';
import { FaceDetectionResult } from '@/types';

// Face recognition threshold for matching
const FACE_MATCH_THRESHOLD = parseFloat(process.env.FACE_RECOGNITION_THRESHOLD || '0.4');

// Initialize face-api.js models
export async function initializeFaceAPI(): Promise<void> {
  try {
    // Load models from public directory
    await faceapi.nets.ssdMobilenetv1.loadFromUri('/models');
    await faceapi.nets.faceLandmark68Net.loadFromUri('/models');
    await faceapi.nets.faceRecognitionNet.loadFromUri('/models');
    console.log('Face-api.js models loaded successfully');
  } catch (error) {
    console.error('Error loading face-api.js models:', error);
    throw error;
  }
}

// Detect faces in an image and return descriptors
export async function detectFacesInImage(imageElement: HTMLImageElement): Promise<FaceDetectionResult[]> {
  try {
    const detections = await faceapi
      .detectAllFaces(imageElement)
      .withFaceLandmarks()
      .withFaceDescriptors();

    const results: FaceDetectionResult[] = detections.map((detection) => ({
      descriptor: Array.from(detection.descriptor),
      confidence: detection.detection.score,
      boundingBox: {
        x: detection.detection.box.x,
        y: detection.detection.box.y,
        width: detection.detection.box.width,
        height: detection.detection.box.height,
      },
    }));

    return results;
  } catch (error) {
    console.error('Error detecting faces:', error);
    return [];
  }
}

// Compare two face descriptors and return similarity score
export function compareFaceDescriptors(descriptor1: number[], descriptor2: number[]): number {
  if (descriptor1.length !== descriptor2.length) {
    throw new Error('Face descriptors must have the same length');
  }

  // Calculate Euclidean distance
  let sum = 0;
  for (let i = 0; i < descriptor1.length; i++) {
    const diff = descriptor1[i] - descriptor2[i];
    sum += diff * diff;
  }
  
  return Math.sqrt(sum);
}

// Check if two faces match based on threshold
export function doFacesMatch(descriptor1: number[], descriptor2: number[]): boolean {
  const distance = compareFaceDescriptors(descriptor1, descriptor2);
  return distance < FACE_MATCH_THRESHOLD;
}

// Find best matching face from a list of descriptors
export function findBestFaceMatch(
  targetDescriptor: number[],
  candidateDescriptors: { id: string; descriptor: number[]; clientId: string }[]
): { match: { id: string; descriptor: number[]; clientId: string } | null; confidence: number } {
  let bestMatch = null;
  let bestDistance = Infinity;

  for (const candidate of candidateDescriptors) {
    const distance = compareFaceDescriptors(targetDescriptor, candidate.descriptor);
    if (distance < bestDistance && distance < FACE_MATCH_THRESHOLD) {
      bestDistance = distance;
      bestMatch = candidate;
    }
  }

  return {
    match: bestMatch,
    confidence: bestMatch ? 1 - (bestDistance / FACE_MATCH_THRESHOLD) : 0,
  };
}

// Process uploaded image for face detection (server-side)
export async function processImageForFaces(imagePath: string): Promise<FaceDetectionResult[]> {
  try {
    // This would typically use a server-side image processing library
    // For now, we'll return a placeholder implementation
    // In a real implementation, you might use:
    // - Canvas API with node-canvas
    // - Sharp for image processing
    // - TensorFlow.js Node.js backend
    
    console.log(`Processing image for faces: ${imagePath}`);
    
    // Placeholder - in real implementation, load image and detect faces
    return [];
  } catch (error) {
    console.error('Error processing image for faces:', error);
    return [];
  }
}

// Validate face descriptor format
export function isValidFaceDescriptor(descriptor: any): descriptor is number[] {
  return (
    Array.isArray(descriptor) &&
    descriptor.length === 128 && // Face-api.js descriptors are 128-dimensional
    descriptor.every((value) => typeof value === 'number' && !isNaN(value))
  );
}

// Create face matcher for client verification
export class ClientFaceMatcher {
  private descriptors: Map<string, number[][]> = new Map();

  constructor(clientDescriptors: { clientId: string; descriptor: number[] }[]) {
    // Group descriptors by client ID
    for (const { clientId, descriptor } of clientDescriptors) {
      if (!this.descriptors.has(clientId)) {
        this.descriptors.set(clientId, []);
      }
      this.descriptors.get(clientId)!.push(descriptor);
    }
  }

  // Find matching client for a given face descriptor
  findMatchingClient(targetDescriptor: number[]): { clientId: string; confidence: number } | null {
    let bestMatch = null;
    let bestConfidence = 0;

    for (const [clientId, descriptors] of this.descriptors.entries()) {
      for (const descriptor of descriptors) {
        const distance = compareFaceDescriptors(targetDescriptor, descriptor);
        if (distance < FACE_MATCH_THRESHOLD) {
          const confidence = 1 - (distance / FACE_MATCH_THRESHOLD);
          if (confidence > bestConfidence) {
            bestConfidence = confidence;
            bestMatch = { clientId, confidence };
          }
        }
      }
    }

    return bestMatch;
  }

  // Add new descriptor for a client
  addClientDescriptor(clientId: string, descriptor: number[]): void {
    if (!this.descriptors.has(clientId)) {
      this.descriptors.set(clientId, []);
    }
    this.descriptors.get(clientId)!.push(descriptor);
  }

  // Remove all descriptors for a client
  removeClient(clientId: string): void {
    this.descriptors.delete(clientId);
  }
}

// Utility functions for face recognition in browser
export const browserFaceUtils = {
  // Load image from URL or file
  loadImage: (src: string | File): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      img.onload = () => resolve(img);
      img.onerror = reject;
      
      if (typeof src === 'string') {
        img.src = src;
      } else {
        const reader = new FileReader();
        reader.onload = (e) => {
          img.src = e.target?.result as string;
        };
        reader.readAsDataURL(src);
      }
    });
  },

  // Get video stream from camera
  getVideoStream: async (): Promise<MediaStream> => {
    try {
      return await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 640 },
          height: { ideal: 480 },
          facingMode: 'user',
        },
      });
    } catch (error) {
      console.error('Error accessing camera:', error);
      throw error;
    }
  },

  // Stop video stream
  stopVideoStream: (stream: MediaStream): void => {
    stream.getTracks().forEach((track) => track.stop());
  },
};
