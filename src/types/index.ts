// User Types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'studio' | 'client';
  createdAt: Date;
  updatedAt: Date;
}

export interface Admin extends User {
  role: 'admin';
}

export interface Studio extends User {
  role: 'studio';
  businessName?: string;
  phone?: string;
  address?: string;
  logo?: string;
  isActive: boolean;
  isApproved: boolean;
  subscriptionId?: string;
  storageUsed: bigint;
  subscription?: Subscription;
}

export interface Client extends User {
  role: 'client';
  phone?: string;
  studioId: string;
  qrCode: string;
  accessLink: string;
  isActive: boolean;
  lastAccess?: Date;
  studio: Studio;
}

// Subscription Types
export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  billingCycle: 'monthly' | 'yearly';
  storageLimit: bigint;
  downloadQuality: 'low' | 'medium' | 'high' | 'original';
  customBranding: boolean;
  watermark: boolean;
  videoSupport: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Subscription {
  id: string;
  studioId: string;
  planId: string;
  status: 'active' | 'cancelled' | 'expired';
  startDate: Date;
  endDate: Date;
  autoRenew: boolean;
  paymentMethod?: string;
  plan: SubscriptionPlan;
  createdAt: Date;
  updatedAt: Date;
}

// Photo & Event Types
export interface Event {
  id: string;
  name: string;
  description?: string;
  date: Date;
  location?: string;
  studioId: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  photos: Photo[];
}

export interface Photo {
  id: string;
  filename: string;
  originalName: string;
  path: string;
  size: bigint;
  mimeType: string;
  studioId: string;
  clientId?: string;
  eventId?: string;
  isMatched: boolean;
  isProcessed: boolean;
  uploadedAt: Date;
  processedAt?: Date;
  faceDescriptors: FaceDescriptor[];
  favorites: Favorite[];
  comments: Comment[];
}

// Face Recognition Types
export interface FaceDescriptor {
  id: string;
  photoId: string;
  clientId: string;
  descriptor: number[];
  confidence: number;
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  createdAt: Date;
}

export interface FaceDetectionResult {
  descriptor: number[];
  confidence: number;
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

// Interaction Types
export interface Favorite {
  id: string;
  photoId: string;
  clientId: string;
  photo: Photo;
  client: Client;
}

export interface Comment {
  id: string;
  photoId: string;
  clientId: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
  photo: Photo;
  client: Client;
}

// Billing Types
export interface BillingHistory {
  id: string;
  studioId: string;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed';
  paymentId?: string;
  description?: string;
  createdAt: Date;
  studio: Studio;
}

// Notification Types
export interface Notification {
  id: string;
  studioId?: string;
  clientId?: string;
  type: 'email' | 'whatsapp' | 'system';
  title: string;
  message: string;
  isRead: boolean;
  sentAt?: Date;
  createdAt: Date;
}

// Analytics Types
export interface AccessLog {
  id: string;
  clientId: string;
  ipAddress: string;
  userAgent?: string;
  action: string;
  createdAt: Date;
  client: Client;
}

export interface DashboardStats {
  totalStudios: number;
  totalClients: number;
  totalPhotos: number;
  totalStorage: bigint;
  activeSubscriptions: number;
  monthlyRevenue: number;
}

export interface StudioStats {
  storageUsed: bigint;
  storageLimit: bigint;
  totalClients: number;
  totalPhotos: number;
  matchedPhotos: number;
  subscriptionStatus: string;
  recentUploads: number;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  businessName?: string;
  phone?: string;
}

export interface ClientForm {
  name: string;
  email: string;
  password: string;
  phone?: string;
}

export interface EventForm {
  name: string;
  description?: string;
  date: Date;
  location?: string;
}

// Upload Types
export interface UploadProgress {
  filename: string;
  progress: number;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;
}

export interface FileUploadResponse {
  success: boolean;
  filename: string;
  path: string;
  size: number;
  message?: string;
}

// System Settings
export interface SystemSettings {
  id: string;
  key: string;
  value: string;
  type: 'string' | 'number' | 'boolean' | 'json';
}
