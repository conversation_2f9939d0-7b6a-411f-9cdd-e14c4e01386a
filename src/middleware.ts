import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { verifyToken } from '@/lib/auth';
import { SecurityService } from '@/lib/security';
import { BotProtectionService } from '@/lib/security/bot-protection';

// Define protected routes
const protectedRoutes = {
  admin: ['/admin'],
  studio: ['/studio'],
  client: ['/client/gallery', '/client/verify'],
};

const publicRoutes = [
  '/',
  '/auth/login',
  '/auth/register',
  '/client/access',
  '/test',
];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Allow public routes
  if (publicRoutes.some(route => pathname.startsWith(route))) {
    return NextResponse.next();
  }

  // Allow API routes (they handle their own auth)
  if (pathname.startsWith('/api/')) {
    return NextResponse.next();
  }

  // Allow static files
  if (pathname.startsWith('/_next/') || pathname.startsWith('/favicon.ico')) {
    return NextResponse.next();
  }

  // Bot protection for all requests
  const botCheck = BotProtectionService.analyzeRequest(request);
  if (!botCheck.success && botCheck.requiresChallenge) {
    return NextResponse.json({
      error: botCheck.message,
      requiresChallenge: true,
      challenge: botCheck.challenge,
    }, { status: 403 });
  }

  // Check for authentication token
  const token = request.cookies.get('token')?.value ||
                request.headers.get('authorization')?.replace('Bearer ', '');

  // Get session token from cookie
  const sessionToken = request.cookies.get('client-session')?.value;

  if (!token) {
    // Redirect to appropriate login page based on route
    if (pathname.startsWith('/admin')) {
      return NextResponse.redirect(new URL('/auth/login', request.url));
    }
    if (pathname.startsWith('/studio')) {
      return NextResponse.redirect(new URL('/auth/login', request.url));
    }
    if (pathname.startsWith('/client')) {
      return NextResponse.redirect(new URL('/client/access', request.url));
    }

    return NextResponse.redirect(new URL('/auth/login', request.url));
  }

  try {
    // Verify token
    const payload = verifyToken(token);

    if (!payload) {
      throw new Error('Invalid token payload');
    }

    // Validate client session for client routes
    if (payload.role === 'client' && sessionToken && pathname.startsWith('/client')) {
      const sessionValidation = await SecurityService.validateClientSession(sessionToken);
      if (!sessionValidation.valid) {
        // Clear invalid session cookie and redirect
        const response = NextResponse.redirect(new URL('/client/access', request.url));
        response.cookies.delete('client-session');
        response.cookies.delete('token');
        return response;
      }
    }

    // Check role-based access
    if (pathname.startsWith('/admin') && payload.role !== 'admin') {
      return NextResponse.redirect(new URL('/auth/login', request.url));
    }

    if (pathname.startsWith('/studio') && payload.role !== 'studio') {
      return NextResponse.redirect(new URL('/auth/login', request.url));
    }

    if (pathname.startsWith('/client') && payload.role !== 'client') {
      return NextResponse.redirect(new URL('/client/access', request.url));
    }

    // Add user info and security context to headers
    const response = NextResponse.next();
    response.headers.set('x-user-id', payload.userId);
    response.headers.set('x-user-role', payload.role);
    response.headers.set('x-user-email', payload.email);

    // Add security context for client routes
    if (payload.role === 'client') {
      const context = SecurityService.extractSecurityContext(request);
      response.headers.set('x-client-ip', context.ipAddress);
      response.headers.set('x-device-id', context.deviceId);
      response.headers.set('x-browser-fingerprint', context.browserFingerprint);
    }

    return response;
  } catch (error) {
    // Invalid token, redirect to login
    console.error('Token verification failed:', error);

    if (pathname.startsWith('/client')) {
      return NextResponse.redirect(new URL('/client/access', request.url));
    }

    return NextResponse.redirect(new URL('/auth/login', request.url));
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
