import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { verifyToken } from '@/lib/auth';

// Define protected routes
const protectedRoutes = {
  admin: ['/admin'],
  studio: ['/studio'],
  client: ['/client/gallery', '/client/verify'],
};

const publicRoutes = [
  '/',
  '/auth/login',
  '/auth/register',
  '/client/access',
  '/test',
];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Allow public routes
  if (publicRoutes.some(route => pathname.startsWith(route))) {
    return NextResponse.next();
  }

  // Allow API routes (they handle their own auth)
  if (pathname.startsWith('/api/')) {
    return NextResponse.next();
  }

  // Allow static files
  if (pathname.startsWith('/_next/') || pathname.startsWith('/favicon.ico')) {
    return NextResponse.next();
  }

  // Check for authentication token
  const token = request.cookies.get('token')?.value || 
                request.headers.get('authorization')?.replace('Bearer ', '');

  if (!token) {
    // Redirect to appropriate login page based on route
    if (pathname.startsWith('/admin')) {
      return NextResponse.redirect(new URL('/auth/login', request.url));
    }
    if (pathname.startsWith('/studio')) {
      return NextResponse.redirect(new URL('/auth/login', request.url));
    }
    if (pathname.startsWith('/client')) {
      return NextResponse.redirect(new URL('/client/access', request.url));
    }
    
    return NextResponse.redirect(new URL('/auth/login', request.url));
  }

  try {
    // Verify token
    const payload = verifyToken(token);
    
    // Check role-based access
    if (pathname.startsWith('/admin') && payload.role !== 'admin') {
      return NextResponse.redirect(new URL('/auth/login', request.url));
    }
    
    if (pathname.startsWith('/studio') && payload.role !== 'studio') {
      return NextResponse.redirect(new URL('/auth/login', request.url));
    }
    
    if (pathname.startsWith('/client') && payload.role !== 'client') {
      return NextResponse.redirect(new URL('/client/access', request.url));
    }

    // Add user info to headers for API routes
    const response = NextResponse.next();
    response.headers.set('x-user-id', payload.userId);
    response.headers.set('x-user-role', payload.role);
    response.headers.set('x-user-email', payload.email);

    return response;
  } catch (error) {
    // Invalid token, redirect to login
    console.error('Token verification failed:', error);
    
    if (pathname.startsWith('/client')) {
      return NextResponse.redirect(new URL('/client/access', request.url));
    }
    
    return NextResponse.redirect(new URL('/auth/login', request.url));
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
