"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { 
  Heart,
  Download,
  Share2,
  Star,
  Grid,
  List,
  Search,
  Filter,
  Eye,
  ShoppingCart,
  User,
  Calendar,
  MapPin,
  Clock,
  Lock,
  Unlock,
  Image as ImageIcon,
  Video,
  ZoomIn,
  ZoomOut,
  RotateCw,
  X,
  ChevronLeft,
  ChevronRight,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Maximize,
  Settings,
  MessageSquare,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Textarea } from "@/components/ui/textarea";
import { formatDate, timeAgo, cn } from "@/lib/utils";

interface GalleryPhoto {
  id: string;
  filename: string;
  url: string;
  thumbnailUrl: string;
  type: 'image' | 'video';
  size: number;
  dimensions: {
    width: number;
    height: number;
  };
  isSelected: boolean;
  isFavorited: boolean;
  downloadCount: number;
  tags: string[];
  metadata: {
    camera?: string;
    lens?: string;
    settings?: string;
    location?: string;
    timestamp: string;
  };
}

interface Gallery {
  id: string;
  title: string;
  description: string;
  eventType: string;
  eventDate: string;
  location: string;
  photographerName: string;
  photographerAvatar: string;
  coverPhoto: string;
  totalPhotos: number;
  totalVideos: number;
  isPasswordProtected: boolean;
  expiryDate: string;
  allowDownloads: boolean;
  allowComments: boolean;
  allowSelections: boolean;
  watermarkEnabled: boolean;
  status: 'active' | 'expired' | 'private';
  createdAt: string;
  lastViewed?: string;
  viewCount: number;
}

interface Comment {
  id: string;
  photoId?: string;
  authorName: string;
  authorEmail: string;
  content: string;
  timestamp: string;
  isPhotographer: boolean;
}

export default function GalleryPage() {
  const params = useParams();
  const galleryId = params.id as string;
  
  const [activeTab, setActiveTab] = useState("photos");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedPhotos, setSelectedPhotos] = useState<string[]>([]);
  const [favoritedPhotos, setFavoritedPhotos] = useState<string[]>([]);
  const [lightboxPhoto, setLightboxPhoto] = useState<string | null>(null);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [newComment, setNewComment] = useState("");

  const [gallery] = useState<Gallery>({
    id: galleryId,
    title: 'Sarah & John Wedding Gallery',
    description: 'Beautiful moments from your special day captured with love and care. Thank you for letting us be part of your celebration!',
    eventType: 'Wedding',
    eventDate: '2024-02-15',
    location: 'Grand Ballroom, Downtown Hotel',
    photographerName: 'PhotoStudio Pro',
    photographerAvatar: '/api/placeholder/40/40',
    coverPhoto: '/api/placeholder/800/600',
    totalPhotos: 150,
    totalVideos: 8,
    isPasswordProtected: false,
    expiryDate: '2024-03-15T00:00:00Z',
    allowDownloads: true,
    allowComments: true,
    allowSelections: true,
    watermarkEnabled: true,
    status: 'active',
    createdAt: '2024-02-20T10:00:00Z',
    lastViewed: '2024-01-20T14:30:00Z',
    viewCount: 45
  });

  const [photos] = useState<GalleryPhoto[]>([
    {
      id: '1',
      filename: 'wedding_ceremony_001.jpg',
      url: '/api/placeholder/800/600',
      thumbnailUrl: '/api/placeholder/300/200',
      type: 'image',
      size: 8500000,
      dimensions: { width: 4000, height: 3000 },
      isSelected: false,
      isFavorited: false,
      downloadCount: 12,
      tags: ['ceremony', 'bride', 'groom', 'altar'],
      metadata: {
        camera: 'Canon EOS R5',
        lens: '85mm f/1.4',
        settings: 'f/2.8, 1/250s, ISO 400',
        location: 'Main Altar',
        timestamp: '2024-02-15T15:30:00Z'
      }
    },
    {
      id: '2',
      filename: 'wedding_reception_001.jpg',
      url: '/api/placeholder/800/600',
      thumbnailUrl: '/api/placeholder/300/200',
      type: 'image',
      size: 7200000,
      dimensions: { width: 4000, height: 3000 },
      isSelected: false,
      isFavorited: true,
      downloadCount: 8,
      tags: ['reception', 'dancing', 'celebration'],
      metadata: {
        camera: 'Canon EOS R5',
        lens: '24-70mm f/2.8',
        settings: 'f/4, 1/125s, ISO 800',
        location: 'Reception Hall',
        timestamp: '2024-02-15T20:15:00Z'
      }
    },
    {
      id: '3',
      filename: 'wedding_highlights.mp4',
      url: '/api/placeholder/800/600',
      thumbnailUrl: '/api/placeholder/300/200',
      type: 'video',
      size: 125000000,
      dimensions: { width: 1920, height: 1080 },
      isSelected: false,
      isFavorited: false,
      downloadCount: 25,
      tags: ['highlights', 'video', 'ceremony', 'reception'],
      metadata: {
        camera: 'Sony A7S III',
        lens: '24-105mm f/4',
        settings: '4K 24fps',
        location: 'Various',
        timestamp: '2024-02-15T16:00:00Z'
      }
    }
  ]);

  const [comments] = useState<Comment[]>([
    {
      id: '1',
      authorName: 'Sarah Johnson',
      authorEmail: '<EMAIL>',
      content: 'These photos are absolutely stunning! Thank you so much for capturing our special day so beautifully. We\'re going to treasure these forever.',
      timestamp: '2024-02-21T10:30:00Z',
      isPhotographer: false
    },
    {
      id: '2',
      photoId: '1',
      authorName: 'John Smith',
      authorEmail: '<EMAIL>',
      content: 'This moment was so perfect - you captured the emotion beautifully!',
      timestamp: '2024-02-21T14:15:00Z',
      isPhotographer: false
    },
    {
      id: '3',
      authorName: 'PhotoStudio Pro',
      authorEmail: '<EMAIL>',
      content: 'Thank you both! It was such a joy to photograph your wedding. The love between you two made every shot magical.',
      timestamp: '2024-02-21T16:45:00Z',
      isPhotographer: true
    }
  ]);

  const filteredPhotos = photos.filter(photo =>
    photo.filename.toLowerCase().includes(searchTerm.toLowerCase()) ||
    photo.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const selectedCount = selectedPhotos.length;
  const favoritedCount = favoritedPhotos.length;

  const togglePhotoSelection = (photoId: string) => {
    setSelectedPhotos(prev =>
      prev.includes(photoId)
        ? prev.filter(id => id !== photoId)
        : [...prev, photoId]
    );
  };

  const togglePhotoFavorite = (photoId: string) => {
    setFavoritedPhotos(prev =>
      prev.includes(photoId)
        ? prev.filter(id => id !== photoId)
        : [...prev, photoId]
    );
  };

  const handleDownloadSelected = () => {
    if (selectedPhotos.length === 0) return;
    // In a real app, this would trigger the download
    console.log('Downloading photos:', selectedPhotos);
  };

  const handleAddComment = () => {
    if (!newComment.trim()) return;
    // In a real app, this would send the comment to the backend
    console.log('Adding comment:', newComment);
    setNewComment("");
  };

  const currentPhotoIndex = lightboxPhoto ? photos.findIndex(p => p.id === lightboxPhoto) : -1;
  const currentPhoto = currentPhotoIndex >= 0 ? photos[currentPhotoIndex] : null;

  const navigateLightbox = (direction: 'prev' | 'next') => {
    if (currentPhotoIndex < 0) return;
    
    let newIndex;
    if (direction === 'prev') {
      newIndex = currentPhotoIndex > 0 ? currentPhotoIndex - 1 : photos.length - 1;
    } else {
      newIndex = currentPhotoIndex < photos.length - 1 ? currentPhotoIndex + 1 : 0;
    }
    
    setLightboxPhoto(photos[newIndex].id);
  };

  // Check if gallery requires password
  useEffect(() => {
    if (gallery.isPasswordProtected && !isAuthenticated) {
      setShowPasswordModal(true);
    }
  }, [gallery.isPasswordProtected, isAuthenticated]);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Gallery Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-6">
              <div className="w-24 h-24 bg-gray-200 dark:bg-gray-600 rounded-lg overflow-hidden">
                <img
                  src={gallery.coverPhoto}
                  alt="Gallery cover"
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    {gallery.title}
                  </h1>
                  <Badge variant={gallery.status === 'active' ? 'success' : 'secondary'}>
                    {gallery.status}
                  </Badge>
                  {gallery.isPasswordProtected && (
                    <Badge variant="secondary">
                      <Lock className="h-3 w-3 mr-1" />
                      Protected
                    </Badge>
                  )}
                </div>
                <p className="text-gray-600 dark:text-gray-400 mb-4 max-w-2xl">
                  {gallery.description}
                </p>
                <div className="flex items-center gap-6 text-sm text-gray-600 dark:text-gray-400">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span>{gallery.photographerName}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(gallery.eventDate)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    <span>{gallery.location}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    <span>{gallery.viewCount} views</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              {gallery.allowSelections && selectedCount > 0 && (
                <Button onClick={handleDownloadSelected}>
                  <Download className="h-4 w-4 mr-2" />
                  Download Selected ({selectedCount})
                </Button>
              )}
              <Button variant="outline">
                <Share2 className="h-4 w-4 mr-2" />
                Share Gallery
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Gallery Stats */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="border-0 shadow-sm bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 bg-blue-600 rounded-lg flex items-center justify-center">
                  <ImageIcon className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-xl font-bold text-blue-900 dark:text-blue-100">
                    {gallery.totalPhotos}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Photos</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-sm bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 bg-purple-600 rounded-lg flex items-center justify-center">
                  <Video className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-xl font-bold text-purple-900 dark:text-purple-100">
                    {gallery.totalVideos}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Videos</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-sm bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 bg-green-600 rounded-lg flex items-center justify-center">
                  <Heart className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-xl font-bold text-green-900 dark:text-green-100">
                    {favoritedCount}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Favorites</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-sm bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 bg-orange-600 rounded-lg flex items-center justify-center">
                  <Clock className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-xl font-bold text-orange-900 dark:text-orange-100">
                    {Math.ceil((new Date(gallery.expiryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))}
                  </p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">Days Left</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <div className="flex items-center justify-between">
            <TabsList className="grid w-auto grid-cols-3">
              <TabsTrigger value="photos">Photos & Videos</TabsTrigger>
              <TabsTrigger value="favorites">Favorites</TabsTrigger>
              <TabsTrigger value="comments">Comments</TabsTrigger>
            </TabsList>
            
            <div className="flex items-center gap-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search photos..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 w-64"
                />
              </div>
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Photos & Videos Tab */}
          <TabsContent value="photos">
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                {filteredPhotos.map(photo => (
                  <div key={photo.id} className="group relative">
                    <div className="aspect-square bg-gray-200 dark:bg-gray-600 rounded-lg overflow-hidden">
                      <img
                        src={photo.thumbnailUrl}
                        alt={photo.filename}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200 cursor-pointer"
                        onClick={() => setLightboxPhoto(photo.id)}
                      />
                      {photo.type === 'video' && (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="bg-black bg-opacity-50 rounded-full p-3">
                            <Play className="h-6 w-6 text-white" />
                          </div>
                        </div>
                      )}
                    </div>
                    
                    {/* Photo Actions */}
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="flex items-center gap-1">
                        {gallery.allowSelections && (
                          <Button
                            size="sm"
                            variant={selectedPhotos.includes(photo.id) ? 'default' : 'secondary'}
                            onClick={() => togglePhotoSelection(photo.id)}
                            className="h-8 w-8 p-0"
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant={favoritedPhotos.includes(photo.id) ? 'default' : 'secondary'}
                          onClick={() => togglePhotoFavorite(photo.id)}
                          className="h-8 w-8 p-0"
                        >
                          <Heart className={cn(
                            "h-4 w-4",
                            favoritedPhotos.includes(photo.id) && "fill-current text-red-500"
                          )} />
                        </Button>
                      </div>
                    </div>

                    {/* Photo Info */}
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-3 opacity-0 group-hover:opacity-100 transition-opacity">
                      <p className="text-white text-sm font-medium truncate">
                        {photo.filename}
                      </p>
                      <div className="flex items-center gap-2 text-xs text-gray-300 mt-1">
                        {photo.type === 'video' ? (
                          <Video className="h-3 w-3" />
                        ) : (
                          <ImageIcon className="h-3 w-3" />
                        )}
                        <span>{photo.dimensions.width}×{photo.dimensions.height}</span>
                        <span>•</span>
                        <span>{photo.downloadCount} downloads</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {filteredPhotos.map(photo => (
                  <Card key={photo.id} className="border-0 shadow-sm">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-4">
                        <div className="w-20 h-20 bg-gray-200 dark:bg-gray-600 rounded-lg overflow-hidden flex-shrink-0">
                          <img
                            src={photo.thumbnailUrl}
                            alt={photo.filename}
                            className="w-full h-full object-cover cursor-pointer"
                            onClick={() => setLightboxPhoto(photo.id)}
                          />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h3 className="font-semibold text-gray-900 dark:text-white">
                              {photo.filename}
                            </h3>
                            <div className="flex items-center gap-2">
                              {gallery.allowSelections && (
                                <Button
                                  size="sm"
                                  variant={selectedPhotos.includes(photo.id) ? 'default' : 'outline'}
                                  onClick={() => togglePhotoSelection(photo.id)}
                                >
                                  <CheckCircle className="h-4 w-4 mr-2" />
                                  {selectedPhotos.includes(photo.id) ? 'Selected' : 'Select'}
                                </Button>
                              )}
                              <Button
                                size="sm"
                                variant={favoritedPhotos.includes(photo.id) ? 'default' : 'outline'}
                                onClick={() => togglePhotoFavorite(photo.id)}
                              >
                                <Heart className={cn(
                                  "h-4 w-4 mr-2",
                                  favoritedPhotos.includes(photo.id) && "fill-current text-red-500"
                                )} />
                                Favorite
                              </Button>
                              {gallery.allowDownloads && (
                                <Button size="sm" variant="outline">
                                  <Download className="h-4 w-4 mr-2" />
                                  Download
                                </Button>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mt-2">
                            <span>{photo.dimensions.width}×{photo.dimensions.height}</span>
                            <span>•</span>
                            <span>{(photo.size / 1024 / 1024).toFixed(1)} MB</span>
                            <span>•</span>
                            <span>{photo.downloadCount} downloads</span>
                            <span>•</span>
                            <span>{formatDate(photo.metadata.timestamp)}</span>
                          </div>
                          {photo.metadata.camera && (
                            <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-500 mt-1">
                              <span>{photo.metadata.camera}</span>
                              {photo.metadata.lens && (
                                <>
                                  <span>•</span>
                                  <span>{photo.metadata.lens}</span>
                                </>
                              )}
                              {photo.metadata.settings && (
                                <>
                                  <span>•</span>
                                  <span>{photo.metadata.settings}</span>
                                </>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          {/* Favorites Tab */}
          <TabsContent value="favorites">
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="h-5 w-5 text-red-500" />
                  Your Favorite Photos
                </CardTitle>
                <CardDescription>
                  Photos you've marked as favorites
                </CardDescription>
              </CardHeader>
              <CardContent>
                {favoritedCount > 0 ? (
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                    {photos.filter(photo => favoritedPhotos.includes(photo.id)).map(photo => (
                      <div key={photo.id} className="group relative">
                        <div className="aspect-square bg-gray-200 dark:bg-gray-600 rounded-lg overflow-hidden">
                          <img
                            src={photo.thumbnailUrl}
                            alt={photo.filename}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200 cursor-pointer"
                            onClick={() => setLightboxPhoto(photo.id)}
                          />
                        </div>
                        <div className="absolute top-2 right-2">
                          <Button
                            size="sm"
                            variant="default"
                            onClick={() => togglePhotoFavorite(photo.id)}
                            className="h-8 w-8 p-0"
                          >
                            <Heart className="h-4 w-4 fill-current text-red-500" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Heart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      No favorites yet
                    </h3>
                    <p className="text-gray-500 dark:text-gray-400">
                      Click the heart icon on photos to add them to your favorites
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Comments Tab */}
          <TabsContent value="comments">
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5 text-blue-600" />
                  Comments & Feedback
                </CardTitle>
                <CardDescription>
                  Share your thoughts about the photos
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Add Comment */}
                {gallery.allowComments && (
                  <div className="space-y-3">
                    <Textarea
                      placeholder="Share your thoughts about the photos..."
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                      className="min-h-[100px]"
                    />
                    <div className="flex justify-end">
                      <Button onClick={handleAddComment} disabled={!newComment.trim()}>
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Add Comment
                      </Button>
                    </div>
                  </div>
                )}

                {/* Comments List */}
                <div className="space-y-4">
                  {comments.map(comment => (
                    <div key={comment.id} className="flex gap-4">
                      <Avatar className="h-10 w-10">
                        <AvatarFallback className={cn(
                          comment.isPhotographer 
                            ? "bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300"
                            : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
                        )}>
                          {comment.authorName.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <p className="font-semibold text-gray-900 dark:text-white">
                            {comment.authorName}
                          </p>
                          {comment.isPhotographer && (
                            <Badge variant="secondary" className="text-xs">
                              Photographer
                            </Badge>
                          )}
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {timeAgo(comment.timestamp)}
                          </span>
                        </div>
                        <p className="text-gray-700 dark:text-gray-300">
                          {comment.content}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Lightbox */}
      {lightboxPhoto && currentPhoto && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center">
          <div className="relative w-full h-full flex items-center justify-center p-4">
            {/* Navigation */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigateLightbox('prev')}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:bg-white hover:bg-opacity-20"
            >
              <ChevronLeft className="h-6 w-6" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigateLightbox('next')}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:bg-white hover:bg-opacity-20"
            >
              <ChevronRight className="h-6 w-6" />
            </Button>

            {/* Close Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setLightboxPhoto(null)}
              className="absolute top-4 right-4 text-white hover:bg-white hover:bg-opacity-20"
            >
              <X className="h-6 w-6" />
            </Button>

            {/* Photo */}
            <div className="max-w-full max-h-full">
              {currentPhoto.type === 'video' ? (
                <video
                  src={currentPhoto.url}
                  controls
                  className="max-w-full max-h-full"
                  autoPlay
                />
              ) : (
                <img
                  src={currentPhoto.url}
                  alt={currentPhoto.filename}
                  className="max-w-full max-h-full object-contain"
                />
              )}
            </div>

            {/* Photo Info */}
            <div className="absolute bottom-4 left-4 right-4 bg-black bg-opacity-50 rounded-lg p-4 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold">{currentPhoto.filename}</h3>
                  <p className="text-sm text-gray-300">
                    {currentPhoto.dimensions.width}×{currentPhoto.dimensions.height} • {(currentPhoto.size / 1024 / 1024).toFixed(1)} MB
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant={favoritedPhotos.includes(currentPhoto.id) ? 'default' : 'secondary'}
                    onClick={() => togglePhotoFavorite(currentPhoto.id)}
                  >
                    <Heart className={cn(
                      "h-4 w-4",
                      favoritedPhotos.includes(currentPhoto.id) && "fill-current text-red-500"
                    )} />
                  </Button>
                  {gallery.allowDownloads && (
                    <Button size="sm" variant="secondary">
                      <Download className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
