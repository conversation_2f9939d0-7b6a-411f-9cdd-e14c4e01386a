import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { authenticateRequest, authorizeRole } from '@/lib/auth';
import { AdminService } from '@/lib/services';

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'all';

    const params = {
      page,
      limit,
      search,
      status,
    };

    const result = await AdminService.getStudios(params);

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error fetching studios:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Approve or reject studio
export async function PUT(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { studioId, action } = body;

    if (!studioId || !action) {
      return NextResponse.json(
        { success: false, message: 'Studio ID and action are required' },
        { status: 400 }
      );
    }

    if (action === 'approve') {
      const result = await AdminService.approveStudio(studioId);
      return NextResponse.json({
        success: result.success,
        message: result.message,
      });
    } else if (action === 'reject') {
      const result = await AdminService.rejectStudio(studioId);
      return NextResponse.json({
        success: result.success,
        message: result.message,
      });
    } else {
      return NextResponse.json(
        { success: false, message: 'Invalid action' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error updating studio:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    if (status) {
      if (status === 'pending') {
        where.isApproved = false;
      } else if (status === 'approved') {
        where.isApproved = true;
        where.isActive = true;
      } else if (status === 'blocked') {
        where.isActive = false;
      }
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { businessName: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [studios, total] = await Promise.all([
      prisma.studio.findMany({
        where,
        skip,
        take: limit,
        include: {
          subscription: {
            include: {
              plan: true,
            },
          },
          _count: {
            select: {
              clients: true,
              photos: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.studio.count({ where }),
    ]);

    // Transform BigInt values to strings for JSON serialization
    const transformedStudios = studios.map(studio => ({
      ...studio,
      storageUsed: studio.storageUsed.toString(),
      subscription: studio.subscription ? {
        ...studio.subscription,
        plan: studio.subscription.plan ? {
          ...studio.subscription.plan,
          storageLimit: studio.subscription.plan.storageLimit.toString(),
        } : null,
      } : null,
    }));

    return NextResponse.json({
      success: true,
      data: transformedStudios,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching studios:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { studioId, action } = body;

    if (!studioId || !action) {
      return NextResponse.json(
        { success: false, message: 'Studio ID and action are required' },
        { status: 400 }
      );
    }

    let updateData: any = {};

    switch (action) {
      case 'approve':
        updateData = { isApproved: true, isActive: true };
        break;
      case 'reject':
        updateData = { isApproved: false, isActive: false };
        break;
      case 'block':
        updateData = { isActive: false };
        break;
      case 'unblock':
        updateData = { isActive: true };
        break;
      default:
        return NextResponse.json(
          { success: false, message: 'Invalid action' },
          { status: 400 }
        );
    }

    const studio = await prisma.studio.update({
      where: { id: studioId },
      data: updateData,
      include: {
        subscription: {
          include: {
            plan: true,
          },
        },
      },
    });

    // Transform BigInt values to strings for JSON serialization
    const transformedStudio = {
      ...studio,
      storageUsed: studio.storageUsed.toString(),
      subscription: studio.subscription ? {
        ...studio.subscription,
        plan: studio.subscription.plan ? {
          ...studio.subscription.plan,
          storageLimit: studio.subscription.plan.storageLimit.toString(),
        } : null,
      } : null,
    };

    return NextResponse.json({
      success: true,
      message: `Studio ${action}ed successfully`,
      data: transformedStudio,
    });
  } catch (error) {
    console.error('Error updating studio:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
