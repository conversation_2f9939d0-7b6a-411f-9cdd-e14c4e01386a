import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, authorize<PERSON><PERSON> } from '@/lib/auth';
import { prisma } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'system';
    const period = parseInt(searchParams.get('period') || '24'); // hours

    const startDate = new Date(Date.now() - period * 60 * 60 * 1000);

    let monitoringData;

    switch (type) {
      case 'system':
        // System health metrics
        const [
          totalUploads,
          failedUploads,
          activeConnections,
          avgResponseTime,
        ] = await Promise.all([
          prisma.photo.count({
            where: { uploadedAt: { gte: startDate } },
          }),
          prisma.photo.count({
            where: {
              uploadedAt: { gte: startDate },
              processingStatus: 'failed',
            },
          }),
          prisma.studio.count({
            where: {
              lastLogin: { gte: new Date(Date.now() - 30 * 60 * 1000) }, // Last 30 minutes
            },
          }),
          // This would come from application metrics
          Math.random() * 500 + 100, // Placeholder
        ]);

        monitoringData = {
          system: {
            totalUploads,
            failedUploads,
            uploadSuccessRate: totalUploads > 0 
              ? ((totalUploads - failedUploads) / totalUploads * 100).toFixed(2)
              : 100,
            activeConnections,
            avgResponseTime: Math.round(avgResponseTime),
          },
          period,
        };
        break;

      case 'performance':
        // Performance metrics
        const performanceMetrics = {
          database: {
            connectionPool: Math.floor(Math.random() * 20) + 5,
            queryTime: Math.random() * 100 + 10,
            slowQueries: Math.floor(Math.random() * 5),
          },
          storage: {
            diskUsage: Math.random() * 80 + 10,
            ioOperations: Math.floor(Math.random() * 1000) + 100,
            bandwidth: Math.random() * 100 + 20,
          },
          memory: {
            usage: Math.random() * 70 + 20,
            available: Math.random() * 30 + 10,
            cached: Math.random() * 40 + 10,
          },
        };

        monitoringData = { performance: performanceMetrics };
        break;

      case 'errors':
        // Error logs and monitoring
        const errorLogs = await prisma.errorLog.findMany({
          where: {
            createdAt: { gte: startDate },
          },
          orderBy: { createdAt: 'desc' },
          take: 50,
          include: {
            studio: {
              select: {
                name: true,
                email: true,
              },
            },
          },
        });

        monitoringData = { errors: errorLogs };
        break;

      default:
        monitoringData = { message: 'Invalid monitoring type' };
    }

    return NextResponse.json({
      success: true,
      data: monitoringData,
      type,
      period,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error fetching monitoring data:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Create alert or notification
export async function POST(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { type, severity, message, metadata } = body;

    if (!type || !severity || !message) {
      return NextResponse.json(
        { success: false, message: 'Type, severity, and message are required' },
        { status: 400 }
      );
    }

    // Create monitoring alert
    const alert = await prisma.monitoringAlert.create({
      data: {
        type,
        severity,
        message,
        metadata: metadata || {},
        createdBy: user.id,
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Monitoring alert created successfully',
      data: alert,
    });
  } catch (error) {
    console.error('Error creating monitoring alert:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
