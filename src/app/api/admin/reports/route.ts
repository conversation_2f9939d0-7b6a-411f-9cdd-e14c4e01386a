import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, authorizeR<PERSON> } from '@/lib/auth';
import { AnalyticsService } from '@/lib/services';

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const reportType = searchParams.get('type') || 'overview';
    const period = parseInt(searchParams.get('period') || '30');
    const format = searchParams.get('format') || 'json';

    let reportData;

    switch (reportType) {
      case 'studios':
        reportData = await AnalyticsService.getStudioReport(period);
        break;
      case 'revenue':
        reportData = await AnalyticsService.getRevenueReport(period);
        break;
      case 'storage':
        reportData = await AnalyticsService.getStorageReport(period);
        break;
      case 'clients':
        reportData = await AnalyticsService.getClientReport(period);
        break;
      case 'overview':
      default:
        reportData = await AnalyticsService.getAdminAnalytics(period);
        break;
    }

    if (format === 'csv') {
      const csvData = await AnalyticsService.exportAdminAnalytics('csv', period);
      return new NextResponse(csvData.data, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="${reportType}-report-${period}days.csv"`,
        },
      });
    }

    return NextResponse.json({
      success: true,
      data: reportData,
      reportType,
      period,
      generatedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error generating report:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Schedule report generation
export async function POST(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { reportType, period, frequency, email } = body;

    if (!reportType || !period || !frequency) {
      return NextResponse.json(
        { success: false, message: 'Report type, period, and frequency are required' },
        { status: 400 }
      );
    }

    // This would typically integrate with a job scheduler
    // For now, we'll just return a success response
    return NextResponse.json({
      success: true,
      message: 'Report scheduled successfully',
      data: {
        reportType,
        period,
        frequency,
        email,
        scheduledAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Error scheduling report:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
