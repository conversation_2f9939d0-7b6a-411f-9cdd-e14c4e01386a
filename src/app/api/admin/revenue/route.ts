import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, authorizeR<PERSON> } from '@/lib/auth';
import { BillingService } from '@/lib/services';

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const period = parseInt(searchParams.get('period') || '30');
    const breakdown = searchParams.get('breakdown') || 'daily';

    const revenueData = await BillingService.getRevenueAnalytics(period, breakdown);

    return NextResponse.json({
      success: true,
      data: revenueData,
    });
  } catch (error) {
    console.error('Error fetching revenue data:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Update revenue record
export async function PUT(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { billingId, amount, status, notes } = body;

    if (!billingId) {
      return NextResponse.json(
        { success: false, message: 'Billing ID is required' },
        { status: 400 }
      );
    }

    const updatedRecord = await BillingService.updateRevenueRecord(
      billingId,
      { amount, status, notes }
    );

    return NextResponse.json({
      success: true,
      message: 'Revenue record updated successfully',
      data: updatedRecord,
    });
  } catch (error) {
    console.error('Error updating revenue record:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
