import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, authorize<PERSON><PERSON> } from '@/lib/auth';
import { BillingService } from '@/lib/services';

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeR<PERSON>(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const period = parseInt(searchParams.get('period') || '30');

    const analytics = await BillingService.getBillingAnalytics(period);

    return NextResponse.json({
      success: true,
      data: analytics,
    });
  } catch (error) {
    console.error('Error fetching billing analytics:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Update billing record status
export async function PUT(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { billingId, status, paymentId } = body;

    if (!billingId || !status) {
      return NextResponse.json(
        { success: false, message: 'Billing ID and status are required' },
        { status: 400 }
      );
    }

    if (!['pending', 'completed', 'failed'].includes(status)) {
      return NextResponse.json(
        { success: false, message: 'Invalid status' },
        { status: 400 }
      );
    }

    const updatedRecord = await BillingService.updateBillingStatus(
      billingId,
      status,
      paymentId
    );

    return NextResponse.json({
      success: true,
      message: 'Billing record updated successfully',
      data: updatedRecord,
    });
  } catch (error) {
    console.error('Error updating billing record:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
