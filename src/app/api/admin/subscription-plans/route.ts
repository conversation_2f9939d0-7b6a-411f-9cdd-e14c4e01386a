import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { authenticateRequest, authorizeRole } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const plans = await prisma.subscriptionPlan.findMany({
      include: {
        _count: {
          select: {
            subscriptions: true,
          },
        },
      },
      orderBy: { price: 'asc' },
    });

    // Transform BigInt values to strings for JSON serialization
    const transformedPlans = plans.map(plan => ({
      ...plan,
      storageLimit: plan.storageLimit.toString(),
    }));

    return NextResponse.json({
      success: true,
      data: transformedPlans,
    });
  } catch (error) {
    console.error('Error fetching subscription plans:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      name,
      price,
      billingCycle,
      storageLimit,
      downloadQuality,
      customBranding,
      watermark,
      videoSupport,
    } = body;

    // Validate required fields
    if (!name || price === undefined || !billingCycle || !storageLimit || !downloadQuality) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    const plan = await prisma.subscriptionPlan.create({
      data: {
        name,
        price: parseFloat(price),
        billingCycle,
        storageLimit: BigInt(storageLimit),
        downloadQuality,
        customBranding: customBranding || false,
        watermark: watermark || false,
        videoSupport: videoSupport || false,
      },
    });

    // Transform BigInt values to strings for JSON serialization
    const transformedPlan = {
      ...plan,
      storageLimit: plan.storageLimit.toString(),
    };

    return NextResponse.json({
      success: true,
      message: 'Subscription plan created successfully',
      data: transformedPlan,
    });
  } catch (error) {
    console.error('Error creating subscription plan:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      id,
      name,
      price,
      billingCycle,
      storageLimit,
      downloadQuality,
      customBranding,
      watermark,
      videoSupport,
      isActive,
    } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, message: 'Plan ID is required' },
        { status: 400 }
      );
    }

    const plan = await prisma.subscriptionPlan.update({
      where: { id },
      data: {
        name,
        price: price ? parseFloat(price) : undefined,
        billingCycle,
        storageLimit: storageLimit ? BigInt(storageLimit) : undefined,
        downloadQuality,
        customBranding,
        watermark,
        videoSupport,
        isActive,
      },
    });

    // Transform BigInt values to strings for JSON serialization
    const transformedPlan = {
      ...plan,
      storageLimit: plan.storageLimit.toString(),
    };

    return NextResponse.json({
      success: true,
      message: 'Subscription plan updated successfully',
      data: transformedPlan,
    });
  } catch (error) {
    console.error('Error updating subscription plan:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
