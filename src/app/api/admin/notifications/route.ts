import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, authorizeR<PERSON> } from '@/lib/auth';
import { NotificationService } from '@/lib/services';

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const type = searchParams.get('type') as 'email' | 'whatsapp' | 'system' | null;
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') as 'asc' | 'desc' || 'desc';

    const params = {
      page,
      limit,
      ...(type && { type }),
      sortBy,
      sortOrder,
    };

    const result = await NotificationService.getAdminNotifications(params);

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error fetching admin notifications:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Send broadcast notification
export async function POST(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { type, title, message, studioIds } = body;

    if (!type || !title || !message) {
      return NextResponse.json(
        { success: false, message: 'Type, title, and message are required' },
        { status: 400 }
      );
    }

    if (!['email', 'whatsapp', 'system'].includes(type)) {
      return NextResponse.json(
        { success: false, message: 'Invalid notification type' },
        { status: 400 }
      );
    }

    const result = await NotificationService.sendBroadcastNotification({
      type,
      title,
      message,
      studioIds,
    });

    return NextResponse.json({
      success: result.success,
      message: result.message,
      data: { sentCount: result.sentCount },
    });
  } catch (error) {
    console.error('Error sending broadcast notification:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Delete notification
export async function DELETE(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const notificationId = searchParams.get('notificationId');
    const notificationIds = searchParams.get('notificationIds');

    if (notificationIds) {
      // Bulk delete
      const ids = notificationIds.split(',');
      const result = await NotificationService.bulkDeleteNotifications(ids);

      return NextResponse.json({
        success: result.success,
        message: result.message,
        data: { deletedCount: result.deletedCount },
      });
    } else if (notificationId) {
      // Single delete
      const result = await NotificationService.deleteNotification(notificationId);

      return NextResponse.json({
        success: result.success,
        message: result.message,
      });
    } else {
      return NextResponse.json(
        { success: false, message: 'Notification ID(s) required' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error deleting notification:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
