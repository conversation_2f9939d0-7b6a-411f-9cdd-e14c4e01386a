import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, authorize<PERSON><PERSON> } from '@/lib/auth';
import { AnalyticsService } from '@/lib/services';

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeR<PERSON>(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const period = parseInt(searchParams.get('period') || '30');

    const analytics = await AnalyticsService.getAdminAnalytics(period);

    return NextResponse.json({
      success: true,
      data: analytics,
    });
  } catch (error) {
    console.error('Error fetching admin analytics:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Export analytics data
export async function POST(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorize<PERSON><PERSON>(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { format = 'csv', period = 30 } = body;

    const result = await AnalyticsService.exportAdminAnalytics(format, period);

    if (format === 'csv') {
      return new NextResponse(result.data, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="admin-analytics-${period}days.csv"`,
        },
      });
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      exportedAt: new Date().toISOString(),
      period,
    });
  } catch (error) {
    console.error('Error exporting analytics:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
