import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, authorize<PERSON><PERSON> } from '@/lib/auth';
import { AdminService } from '@/lib/services';

// Get system information and health status
export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const result = await AdminService.getSystemInfo();

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error fetching system info:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Perform system maintenance actions
export async function POST(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, parameters } = body;

    if (!action) {
      return NextResponse.json(
        { success: false, message: 'Action is required' },
        { status: 400 }
      );
    }

    const ipAddress = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    let result;

    switch (action) {
      case 'cleanup-storage':
        result = await AdminService.cleanupSystemStorage(user.id, ipAddress, userAgent);
        break;
      case 'optimize-database':
        result = await AdminService.optimizeDatabase(user.id, ipAddress, userAgent);
        break;
      case 'clear-cache':
        result = await AdminService.clearSystemCache(user.id, ipAddress, userAgent);
        break;
      case 'backup-database':
        result = await AdminService.backupDatabase(user.id, ipAddress, userAgent);
        break;
      case 'update-settings':
        if (!parameters) {
          return NextResponse.json(
            { success: false, message: 'Settings parameters are required' },
            { status: 400 }
          );
        }
        result = await AdminService.updateSystemSettings(parameters, user.id, ipAddress, userAgent);
        break;
      default:
        return NextResponse.json(
          { success: false, message: 'Invalid action' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      message: result.message,
      data: result.data,
    });
  } catch (error) {
    console.error('Error performing system action:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
