import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, authorize<PERSON><PERSON> } from '@/lib/auth';
import { prisma } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'overview';
    const period = parseInt(searchParams.get('period') || '7');

    const startDate = new Date(Date.now() - period * 24 * 60 * 60 * 1000);

    let securityData;

    switch (type) {
      case 'login-attempts':
        securityData = await prisma.loginAttempt.findMany({
          where: {
            createdAt: { gte: startDate },
          },
          include: {
            studio: {
              select: {
                name: true,
                email: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          take: 100,
        });
        break;

      case 'suspicious-activity':
        // Get failed login attempts, multiple access attempts, etc.
        const [failedLogins, suspiciousAccess] = await Promise.all([
          prisma.loginAttempt.findMany({
            where: {
              success: false,
              createdAt: { gte: startDate },
            },
            include: {
              studio: {
                select: {
                  name: true,
                  email: true,
                },
              },
            },
            orderBy: { createdAt: 'desc' },
          }),
          prisma.clientAccessLog.findMany({
            where: {
              createdAt: { gte: startDate },
              // Multiple access attempts from same IP
            },
            include: {
              client: {
                select: {
                  name: true,
                  email: true,
                  studio: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
            orderBy: { createdAt: 'desc' },
            take: 50,
          }),
        ]);

        securityData = {
          failedLogins,
          suspiciousAccess,
        };
        break;

      case 'overview':
      default:
        const [
          totalLoginAttempts,
          failedLoginAttempts,
          activeStudios,
          blockedIPs,
        ] = await Promise.all([
          prisma.loginAttempt.count({
            where: { createdAt: { gte: startDate } },
          }),
          prisma.loginAttempt.count({
            where: {
              success: false,
              createdAt: { gte: startDate },
            },
          }),
          prisma.studio.count({
            where: {
              isActive: true,
              lastLogin: { gte: startDate },
            },
          }),
          // This would come from a security service/firewall
          0, // Placeholder for blocked IPs count
        ]);

        securityData = {
          overview: {
            totalLoginAttempts,
            failedLoginAttempts,
            successRate: totalLoginAttempts > 0 
              ? ((totalLoginAttempts - failedLoginAttempts) / totalLoginAttempts * 100).toFixed(2)
              : 100,
            activeStudios,
            blockedIPs,
          },
          period,
        };
        break;
    }

    return NextResponse.json({
      success: true,
      data: securityData,
      type,
      period,
    });
  } catch (error) {
    console.error('Error fetching security data:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Security actions (block IP, reset password, etc.)
export async function POST(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, targetId, reason } = body;

    if (!action || !targetId) {
      return NextResponse.json(
        { success: false, message: 'Action and target ID are required' },
        { status: 400 }
      );
    }

    let result;

    switch (action) {
      case 'block-studio':
        result = await prisma.studio.update({
          where: { id: targetId },
          data: { 
            isActive: false,
            blockedAt: new Date(),
            blockReason: reason,
          },
        });
        break;

      case 'unblock-studio':
        result = await prisma.studio.update({
          where: { id: targetId },
          data: { 
            isActive: true,
            blockedAt: null,
            blockReason: null,
          },
        });
        break;

      case 'force-password-reset':
        // This would typically send a password reset email
        result = { message: 'Password reset email sent' };
        break;

      default:
        return NextResponse.json(
          { success: false, message: 'Invalid action' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      message: `Security action '${action}' completed successfully`,
      data: result,
    });
  } catch (error) {
    console.error('Error performing security action:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
