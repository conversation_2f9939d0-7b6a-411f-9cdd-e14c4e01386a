import { NextRequest, NextResponse } from 'next/server';
import { SessionCleanupService } from '@/lib/security/session-cleanup';
import { verifyToken } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const decoded = verifyToken(token);
    if (!decoded || decoded.role !== 'admin') {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get report type from query params
    const { searchParams } = new URL(request.url);
    const reportType = searchParams.get('type') || 'summary';

    let result;

    switch (reportType) {
      case 'summary':
        result = await SessionCleanupService.generateSecurityReport();
        break;
      case 'anomalies':
        result = await SessionCleanupService.checkSecurityAnomalies();
        break;
      case 'both':
        const [summary, anomalies] = await Promise.all([
          SessionCleanupService.generateSecurityReport(),
          SessionCleanupService.checkSecurityAnomalies(),
        ]);
        result = { summary, anomalies };
        break;
      default:
        return NextResponse.json(
          { success: false, message: 'Invalid report type' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      reportType,
      data: result,
      generatedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Security report error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to generate security report' },
      { status: 500 }
    );
  }
}
