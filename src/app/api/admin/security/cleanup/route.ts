import { NextRequest, NextResponse } from 'next/server';
import { SessionCleanupService } from '@/lib/security/session-cleanup';
import { verifyToken } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const decoded = verifyToken(token);
    if (!decoded || decoded.role !== 'admin') {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      );
    }

    // Run cleanup
    const result = await SessionCleanupService.cleanupExpiredData();

    return NextResponse.json({
      success: true,
      message: 'Security cleanup completed',
      result,
    });
  } catch (error) {
    console.error('Security cleanup error:', error);
    return NextResponse.json(
      { success: false, message: 'Cleanup failed' },
      { status: 500 }
    );
  }
}

// Allow cron job access with API key
export async function GET(request: NextRequest) {
  try {
    // Check for cron API key
    const apiKey = request.headers.get('x-api-key');
    const cronApiKey = process.env.CRON_API_KEY;
    
    if (!cronApiKey || apiKey !== cronApiKey) {
      return NextResponse.json(
        { success: false, message: 'Invalid API key' },
        { status: 401 }
      );
    }

    // Run cleanup
    const result = await SessionCleanupService.cleanupExpiredData();

    return NextResponse.json({
      success: true,
      message: 'Automated security cleanup completed',
      result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Automated security cleanup error:', error);
    return NextResponse.json(
      { success: false, message: 'Automated cleanup failed' },
      { status: 500 }
    );
  }
}
