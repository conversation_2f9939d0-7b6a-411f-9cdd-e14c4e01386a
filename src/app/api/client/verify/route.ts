import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, authorize<PERSON><PERSON> } from '@/lib/auth';
import { ClientService } from '@/lib/services';

export async function POST(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeR<PERSON>(user, ['client'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { faceDescriptor } = body;

    if (!faceDescriptor || !Array.isArray(faceDescriptor)) {
      return NextResponse.json(
        { success: false, message: 'Valid face descriptor is required' },
        { status: 400 }
      );
    }

    const ipAddress = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    const result = await ClientService.verifyFace(
      user.id,
      faceDescriptor,
      ipAddress,
      userAgent
    );

    return NextResponse.json({
      success: true,
      verified: result.verified,
      message: result.message,
      confidence: result.confidence,
      requiresEnrollment: result.requiresEnrollment,
    });
  } catch (error) {
    console.error('Face verification error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Enroll a new face descriptor for the client
export async function PUT(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['client'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { faceDescriptor, boundingBox, confidence } = body;

    if (!faceDescriptor || !Array.isArray(faceDescriptor)) {
      return NextResponse.json(
        { success: false, message: 'Valid face descriptor is required' },
        { status: 400 }
      );
    }

    const ipAddress = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    const enrollmentData = {
      faceDescriptor,
      boundingBox: boundingBox || { x: 0, y: 0, width: 100, height: 100 },
      confidence: confidence || 0.9,
    };

    const result = await ClientService.enrollFace(
      user.id,
      enrollmentData,
      ipAddress,
      userAgent
    );

    return NextResponse.json({
      success: true,
      message: 'Face profile enrolled successfully',
      descriptorId: result.descriptorId,
    });
  } catch (error) {
    console.error('Face enrollment error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
