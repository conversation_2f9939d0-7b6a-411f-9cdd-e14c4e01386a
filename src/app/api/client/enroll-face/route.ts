import { NextRequest, NextResponse } from 'next/server';
import { ClientService } from '@/lib/services/client.service';
import { BotProtectionService } from '@/lib/security/bot-protection';
import { verifyToken } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      faceDescriptor, 
      boundingBox,
      confidence,
      behaviorData,
      honeypotFields 
    } = body;

    // Verify authentication token
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const decoded = verifyToken(token);
    if (!decoded || decoded.role !== 'client') {
      return NextResponse.json(
        { success: false, message: 'Invalid authentication' },
        { status: 401 }
      );
    }

    const clientId = decoded.userId;

    // Validate required fields
    if (!Array.isArray(faceDescriptor) || faceDescriptor.length !== 128) {
      return NextResponse.json(
        { success: false, message: 'Invalid face descriptor format' },
        { status: 400 }
      );
    }

    if (!boundingBox || typeof boundingBox !== 'object') {
      return NextResponse.json(
        { success: false, message: 'Bounding box is required' },
        { status: 400 }
      );
    }

    if (typeof confidence !== 'number' || confidence < 0 || confidence > 1) {
      return NextResponse.json(
        { success: false, message: 'Valid confidence score is required' },
        { status: 400 }
      );
    }

    // Verify honeypot fields (bot protection)
    if (honeypotFields && !BotProtectionService.verifyHoneypot(honeypotFields)) {
      return NextResponse.json(
        { success: false, message: 'Security verification failed' },
        { status: 403 }
      );
    }

    // Bot protection for enrollment
    if (behaviorData) {
      const behaviorCheck = BotProtectionService.analyzeBehavior(behaviorData);
      if (!behaviorCheck.success) {
        return NextResponse.json({
          success: false,
          message: behaviorCheck.message,
          requiresChallenge: behaviorCheck.requiresChallenge,
          challenge: behaviorCheck.challenge,
        }, { status: 403 });
      }
    }

    // Rate limiting for face enrollment
    const context = SecurityService.extractSecurityContext(request);
    const rateCheck = await BotProtectionService.checkRateLimit(
      clientId, 
      'face_scan', 
      context.ipAddress
    );
    if (!rateCheck.success) {
      return NextResponse.json({
        success: false,
        message: rateCheck.message,
      }, { status: 429 });
    }

    // Minimum confidence threshold for enrollment
    const minConfidence = parseFloat(process.env.FACE_ENROLLMENT_MIN_CONFIDENCE || '0.8');
    if (confidence < minConfidence) {
      return NextResponse.json(
        { 
          success: false, 
          message: `Face quality too low. Minimum confidence required: ${minConfidence}`,
          currentConfidence: confidence,
        },
        { status: 400 }
      );
    }

    const result = await ClientService.enrollFace(
      clientId,
      {
        faceDescriptor,
        boundingBox,
        confidence,
      },
      request
    );

    if (!result.success) {
      return NextResponse.json(
        { success: false, message: result.message },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: result.message,
      descriptorId: result.descriptorId,
    });
  } catch (error) {
    console.error('Face enrollment error:', error);
    return NextResponse.json(
      { success: false, message: 'Face enrollment failed' },
      { status: 500 }
    );
  }
}

// Get enrollment requirements and challenges
export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const decoded = verifyToken(token);
    if (!decoded || decoded.role !== 'client') {
      return NextResponse.json(
        { success: false, message: 'Invalid authentication' },
        { status: 401 }
      );
    }

    // Generate honeypot fields
    const honeypotFields = BotProtectionService.generateHoneypotFields();

    return NextResponse.json({
      success: true,
      requirements: {
        minConfidence: parseFloat(process.env.FACE_ENROLLMENT_MIN_CONFIDENCE || '0.8'),
        descriptorLength: 128,
        requiredFields: ['faceDescriptor', 'boundingBox', 'confidence'],
      },
      honeypotFields,
      timestamp: Date.now(),
    });
  } catch (error) {
    console.error('Enrollment requirements error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get enrollment requirements' },
      { status: 500 }
    );
  }
}
