import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, authorizeR<PERSON> } from '@/lib/auth';
import { ClientService } from '@/lib/services';

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['client'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    const params = {
      page,
      limit,
    };

    const result = await ClientService.getFavoritePhotos(user.id, params);

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error fetching favorite photos:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Bulk favorite/unfavorite operations
export async function POST(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['client'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { photoIds, action } = body;

    if (!photoIds || !Array.isArray(photoIds) || photoIds.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Photo IDs array is required' },
        { status: 400 }
      );
    }

    if (!['favorite', 'unfavorite'].includes(action)) {
      return NextResponse.json(
        { success: false, message: 'Invalid action' },
        { status: 400 }
      );
    }

    const ipAddress = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    const result = await ClientService.bulkToggleFavorites(
      user.id,
      photoIds,
      action,
      ipAddress,
      userAgent
    );

    return NextResponse.json({
      success: result.success,
      message: result.message,
      data: { processedCount: result.processedCount },
    });
  } catch (error) {
    console.error('Error bulk updating favorites:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
