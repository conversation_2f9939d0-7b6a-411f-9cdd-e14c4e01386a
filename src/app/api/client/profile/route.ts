import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, authorize<PERSON><PERSON> } from '@/lib/auth';
import { ClientService } from '@/lib/services';

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeR<PERSON>(user, ['client'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const profile = await ClientService.getClientProfile(user.id);

    if (!profile) {
      return NextResponse.json(
        { success: false, message: 'Profile not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: profile,
    });
  } catch (error) {
    console.error('Error fetching client profile:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Update client profile
export async function PUT(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['client'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, phone, preferences } = body;

    const updateData = {
      ...(name && { name }),
      ...(phone && { phone }),
      ...(preferences && { preferences }),
    };

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { success: false, message: 'No valid fields to update' },
        { status: 400 }
      );
    }

    const result = await ClientService.updateClientProfile(user.id, updateData);

    if (!result.success) {
      return NextResponse.json(
        { success: false, message: result.message },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Profile updated successfully',
      data: result.data,
    });
  } catch (error) {
    console.error('Error updating client profile:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
