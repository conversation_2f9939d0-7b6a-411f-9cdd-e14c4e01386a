import { NextRequest, NextResponse } from 'next/server';
import { ClientService } from '@/lib/services/client.service';
import { BotProtectionService } from '@/lib/security/bot-protection';
import { SecurityService } from '@/lib/security';
import { verifyToken } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      faceDescriptor, 
      behaviorData,
      challengeResponse,
      timingData 
    } = body;

    // Verify authentication token
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const decoded = verifyToken(token);
    if (!decoded || decoded.role !== 'client') {
      return NextResponse.json(
        { success: false, message: 'Invalid authentication' },
        { status: 401 }
      );
    }

    const clientId = decoded.userId;

    // Validate face descriptor
    if (!Array.isArray(faceDescriptor) || faceDescriptor.length !== 128) {
      return NextResponse.json(
        { success: false, message: 'Invalid face descriptor format' },
        { status: 400 }
      );
    }

    // Verify timing challenge if provided
    if (timingData) {
      const { challenge, startTime, endTime, minTime } = timingData;
      if (!BotProtectionService.verifyTimingChallenge(challenge, startTime, endTime, minTime)) {
        return NextResponse.json(
          { success: false, message: 'Timing verification failed' },
          { status: 403 }
        );
      }
    }

    // Check for bot signatures
    const botSignatures = BotProtectionService.checkBotSignatures(request);
    if (botSignatures.length > 2) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Automated access detected',
          requiresChallenge: true,
          challenge: BotProtectionService.generateChallenge()
        },
        { status: 403 }
      );
    }

    const result = await ClientService.verifyFace(
      clientId,
      faceDescriptor,
      request,
      behaviorData
    );

    if (!result.success) {
      const status = result.blocked ? 423 : 400;
      const response: any = { 
        success: false, 
        message: result.message,
        confidence: result.confidence,
        attemptsRemaining: result.attemptsRemaining
      };
      
      // Include challenge if required
      if (result.requiresChallenge) {
        response.requiresChallenge = true;
        response.challenge = result.challenge;
      }
      
      // Include blocking information
      if (result.blocked) {
        response.blocked = true;
        response.blockedUntil = result.blockedUntil;
      }

      return NextResponse.json(response, { status });
    }

    return NextResponse.json({
      success: true,
      message: result.message,
      confidence: result.confidence,
      attemptsRemaining: result.attemptsRemaining,
    });
  } catch (error) {
    console.error('Face verification error:', error);
    return NextResponse.json(
      { success: false, message: 'Face verification failed' },
      { status: 500 }
    );
  }
}

// Generate timing challenge for face verification
export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const decoded = verifyToken(token);
    if (!decoded || decoded.role !== 'client') {
      return NextResponse.json(
        { success: false, message: 'Invalid authentication' },
        { status: 401 }
      );
    }

    // Generate timing challenge
    const timingChallenge = BotProtectionService.generateTimingChallenge();
    
    // Generate honeypot fields
    const honeypotFields = BotProtectionService.generateHoneypotFields();

    return NextResponse.json({
      success: true,
      timingChallenge,
      honeypotFields,
      timestamp: Date.now(),
    });
  } catch (error) {
    console.error('Challenge generation error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to generate challenge' },
      { status: 500 }
    );
  }
}
