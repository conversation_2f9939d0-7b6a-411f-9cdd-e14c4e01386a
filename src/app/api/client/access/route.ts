import { NextRequest, NextResponse } from 'next/server';
import { ClientService } from '@/lib/services';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { accessCode, password } = body;

    if (!accessCode || !password) {
      return NextResponse.json(
        { success: false, message: 'Access code and password are required' },
        { status: 400 }
      );
    }

    const ipAddress = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    const result = await ClientService.authenticateClient(
      accessCode,
      password,
      ipAddress,
      userAgent
    );

    if (!result.success) {
      return NextResponse.json(
        { success: false, message: result.message },
        { status: result.status || 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Access granted',
      token: result.token,
      client: result.client,
    });
  } catch (error) {
    console.error('Client access error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
