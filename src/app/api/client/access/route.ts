import { NextRequest, NextResponse } from 'next/server';
import { ClientService } from '@/lib/services/client.service';
import { BotProtectionService } from '@/lib/security/bot-protection';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      accessCode,
      password,
      behaviorData,
      challengeResponse,
      honeypotFields
    } = body;

    if (!accessCode || !password) {
      return NextResponse.json(
        { success: false, message: 'Access code and password are required' },
        { status: 400 }
      );
    }

    // Verify honeypot fields (bot protection)
    if (honeypotFields && !BotProtectionService.verifyHoneypot(honeypotFields)) {
      return NextResponse.json(
        { success: false, message: 'Security verification failed' },
        { status: 403 }
      );
    }

    const result = await ClientService.authenticateClient(
      accessCode,
      password,
      request,
      behaviorData,
      challengeResponse
    );

    if (!result.success) {
      const status = result.status || 401;
      const response: any = {
        success: false,
        message: result.message
      };

      // Include challenge if required
      if (result.requiresChallenge) {
        response.requiresChallenge = true;
        response.challenge = result.challenge;
      }

      // Include blocking information
      if (result.blocked) {
        response.blocked = true;
        response.blockedUntil = result.blockedUntil;
      }

      return NextResponse.json(response, { status });
    }

    // Set secure session cookie
    const response = NextResponse.json({
      success: true,
      message: 'Access granted',
      client: result.client,
      token: result.token,
    });

    // Set HTTP-only session cookie
    if (result.sessionToken) {
      response.cookies.set('client-session', result.sessionToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 60 * 60, // 1 hour
        path: '/',
      });
    }

    return response;
  } catch (error) {
    console.error('Client access error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
