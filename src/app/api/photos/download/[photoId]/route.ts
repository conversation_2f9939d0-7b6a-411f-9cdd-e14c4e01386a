import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { authenticateRequest, authorizeRole } from '@/lib/auth';
import fs from 'fs';
import path from 'path';

export async function GET(
  request: NextRequest,
  { params }: { params: { photoId: string } }
) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['client', 'studio', 'admin'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { photoId } = params;

    // Get photo details
    const photo = await prisma.photo.findUnique({
      where: { id: photoId },
      include: {
        studio: {
          select: {
            name: true,
          }
        }
      }
    });

    if (!photo) {
      return NextResponse.json(
        { success: false, message: 'Photo not found' },
        { status: 404 }
      );
    }

    // Check access permissions
    if (user.role === 'client') {
      // Client can only download their own matched photos
      if (photo.clientId !== user.id || !photo.isMatched) {
        return NextResponse.json(
          { success: false, message: 'Access denied' },
          { status: 403 }
        );
      }
    } else if (user.role === 'studio') {
      // Studio can download photos from their own studio
      if (photo.studioId !== user.id) {
        return NextResponse.json(
          { success: false, message: 'Access denied' },
          { status: 403 }
        );
      }
    }
    // Admin can download any photo

    // Check if file exists
    const filePath = path.join(process.cwd(), 'uploads', photo.path);
    if (!fs.existsSync(filePath)) {
      return NextResponse.json(
        { success: false, message: 'File not found on server' },
        { status: 404 }
      );
    }

    // Read file
    const fileBuffer = fs.readFileSync(filePath);
    
    // Log download activity
    await prisma.accessLog.create({
      data: {
        clientId: user.role === 'client' ? user.id : photo.clientId,
        action: 'download',
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      }
    });

    // Return file with appropriate headers
    return new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': photo.mimeType,
        'Content-Length': photo.size.toString(),
        'Content-Disposition': `attachment; filename="${photo.filename}"`,
        'Cache-Control': 'private, no-cache',
      },
    });
  } catch (error) {
    console.error('Error downloading photo:', error);
    return NextResponse.json(
      { success: false, message: 'Download failed' },
      { status: 500 }
    );
  }
}
