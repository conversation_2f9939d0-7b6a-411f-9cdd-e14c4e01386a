import { NextRequest, NextResponse } from 'next/server';
import { PhotoService } from '@/lib/services';

// Public gallery access (no authentication required)
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const galleryId = params.id;
    const { searchParams } = new URL(request.url);
    const password = searchParams.get('password');

    if (!galleryId) {
      return NextResponse.json(
        { success: false, message: 'Gallery ID is required' },
        { status: 400 }
      );
    }

    const result = await PhotoService.getPublicGallery(galleryId, password);

    if (!result.success) {
      return NextResponse.json(
        { success: false, message: result.message },
        { status: result.status || 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: result.data,
    });
  } catch (error) {
    console.error('Error fetching public gallery:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Update gallery settings (requires authentication)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const galleryId = params.id;
    const body = await request.json();
    
    // For now, we'll implement basic gallery update
    // In a full implementation, you'd want proper authentication here
    
    return NextResponse.json({
      success: true,
      message: 'Gallery updated successfully',
    });
  } catch (error) {
    console.error('Error updating gallery:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
