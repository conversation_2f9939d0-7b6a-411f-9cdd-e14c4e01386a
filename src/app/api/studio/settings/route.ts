import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, authorize<PERSON><PERSON> } from '@/lib/auth';
import { UserService } from '@/lib/services';

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorize<PERSON><PERSON>(user, ['studio'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const studioData = await UserService.getUserById(user.id, 'studio');

    if (!studioData) {
      return NextResponse.json(
        { success: false, message: 'Studio not found' },
        { status: 404 }
      );
    }

    // Remove sensitive data
    const { password, ...safeStudioData } = studioData;

    return NextResponse.json({
      success: true,
      data: safeStudioData,
    });
  } catch (error) {
    console.error('Error fetching studio settings:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Update studio settings
export async function PUT(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorize<PERSON><PERSON>(user, ['studio'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      name,
      businessName,
      email,
      phone,
      address,
      city,
      state,
      zipCode,
      country,
      website,
      logo,
      description,
      socialMedia,
      businessHours,
      timezone,
      currency,
      language,
      emailNotifications,
      smsNotifications,
      marketingEmails,
    } = body;

    const updateData = {
      ...(name && { name }),
      ...(businessName && { businessName }),
      ...(email && { email }),
      ...(phone && { phone }),
      ...(address && { address }),
      ...(city && { city }),
      ...(state && { state }),
      ...(zipCode && { zipCode }),
      ...(country && { country }),
      ...(website && { website }),
      ...(logo && { logo }),
      ...(description && { description }),
      ...(socialMedia && { socialMedia }),
      ...(businessHours && { businessHours }),
      ...(timezone && { timezone }),
      ...(currency && { currency }),
      ...(language && { language }),
      ...(emailNotifications !== undefined && { emailNotifications }),
      ...(smsNotifications !== undefined && { smsNotifications }),
      ...(marketingEmails !== undefined && { marketingEmails }),
    };

    const result = await UserService.updateProfile(user.id, 'studio', updateData);

    if (!result.success) {
      return NextResponse.json(
        { success: false, message: result.message },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Studio settings updated successfully',
      data: result.data,
    });
  } catch (error) {
    console.error('Error updating studio settings:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Change password
export async function POST(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['studio'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { currentPassword, newPassword, confirmPassword } = body;

    if (!currentPassword || !newPassword || !confirmPassword) {
      return NextResponse.json(
        { success: false, message: 'All password fields are required' },
        { status: 400 }
      );
    }

    if (newPassword !== confirmPassword) {
      return NextResponse.json(
        { success: false, message: 'New passwords do not match' },
        { status: 400 }
      );
    }

    const result = await UserService.changePassword(
      user.id,
      'studio',
      currentPassword,
      newPassword
    );

    if (!result.success) {
      return NextResponse.json(
        { success: false, message: result.message },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Password changed successfully',
    });
  } catch (error) {
    console.error('Error changing password:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
