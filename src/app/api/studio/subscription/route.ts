import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { authenticateRequest, authorizeRole } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['studio'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get current subscription
    const subscription = await prisma.subscription.findFirst({
      where: { studioId: user.id },
      include: {
        plan: true,
        billingHistory: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
      },
    });

    // Get available plans
    const availablePlans = await prisma.subscriptionPlan.findMany({
      where: { isActive: true },
      orderBy: { price: 'asc' },
    });

    return NextResponse.json({
      success: true,
      data: {
        currentSubscription: subscription,
        availablePlans,
      },
    });
  } catch (error) {
    console.error('Error fetching subscription:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['studio'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { planId, paymentMethod } = body;

    if (!planId) {
      return NextResponse.json(
        { success: false, message: 'Plan ID is required' },
        { status: 400 }
      );
    }

    // Get the plan
    const plan = await prisma.subscriptionPlan.findUnique({
      where: { id: planId },
    });

    if (!plan || !plan.isActive) {
      return NextResponse.json(
        { success: false, message: 'Plan not found or inactive' },
        { status: 404 }
      );
    }

    // Check if studio already has an active subscription
    const existingSubscription = await prisma.subscription.findFirst({
      where: { studioId: user.id },
    });

    if (existingSubscription) {
      // Update existing subscription
      const updatedSubscription = await prisma.subscription.update({
        where: { id: existingSubscription.id },
        data: {
          planId,
          status: 'active',
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + (plan.billingCycle === 'yearly' ? 365 : 30) * 24 * 60 * 60 * 1000),
        },
        include: {
          plan: true,
        },
      });

      // Create billing record
      await prisma.billingHistory.create({
        data: {
          subscriptionId: updatedSubscription.id,
          amount: plan.price,
          currency: 'INR',
          status: 'paid',
          paymentMethod: paymentMethod || 'card',
          description: `Subscription upgrade to ${plan.name}`,
        },
      });

      return NextResponse.json({
        success: true,
        message: 'Subscription updated successfully',
        subscription: updatedSubscription,
      });
    } else {
      // Create new subscription
      const newSubscription = await prisma.subscription.create({
        data: {
          studioId: user.id,
          planId,
          status: 'active',
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + (plan.billingCycle === 'yearly' ? 365 : 30) * 24 * 60 * 60 * 1000),
        },
        include: {
          plan: true,
        },
      });

      // Create billing record
      await prisma.billingHistory.create({
        data: {
          subscriptionId: newSubscription.id,
          amount: plan.price,
          currency: 'INR',
          status: 'paid',
          paymentMethod: paymentMethod || 'card',
          description: `New subscription to ${plan.name}`,
        },
      });

      return NextResponse.json({
        success: true,
        message: 'Subscription created successfully',
        subscription: newSubscription,
      });
    }
  } catch (error) {
    console.error('Error creating/updating subscription:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['studio'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Cancel subscription
    const subscription = await prisma.subscription.findFirst({
      where: { studioId: user.id },
    });

    if (!subscription) {
      return NextResponse.json(
        { success: false, message: 'No active subscription found' },
        { status: 404 }
      );
    }

    await prisma.subscription.update({
      where: { id: subscription.id },
      data: {
        status: 'cancelled',
        cancelledAt: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Subscription cancelled successfully',
    });
  } catch (error) {
    console.error('Error cancelling subscription:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
