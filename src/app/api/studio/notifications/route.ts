import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, authorizeR<PERSON> } from '@/lib/auth';
import { NotificationService } from '@/lib/services';

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['studio'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const type = searchParams.get('type') as 'email' | 'whatsapp' | 'system' | null;
    const isRead = searchParams.get('isRead') === 'true' ? true : 
                   searchParams.get('isRead') === 'false' ? false : null;

    const params = {
      page,
      limit,
      ...(type && { type }),
      ...(isRead !== null && { isRead }),
    };

    const result = await NotificationService.getStudioNotifications(user.id, params);

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error fetching notifications:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Mark notifications as read
export async function PUT(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['studio'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { notificationIds, markAllAsRead } = body;

    let result;

    if (markAllAsRead) {
      result = await NotificationService.markAllAsRead(user.id, 'studio');
    } else if (notificationIds && Array.isArray(notificationIds)) {
      result = await NotificationService.markAsRead(notificationIds);
    } else {
      return NextResponse.json(
        { success: false, message: 'Notification IDs or markAllAsRead flag required' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: result.success,
      message: result.message,
      data: result.data,
    });
  } catch (error) {
    console.error('Error updating notifications:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Send notification to clients
export async function POST(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['studio'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { type, title, message, clientIds, eventId } = body;

    if (!type || !title || !message) {
      return NextResponse.json(
        { success: false, message: 'Type, title, and message are required' },
        { status: 400 }
      );
    }

    if (!['email', 'whatsapp', 'system'].includes(type)) {
      return NextResponse.json(
        { success: false, message: 'Invalid notification type' },
        { status: 400 }
      );
    }

    const notificationData = {
      type,
      title,
      message,
      studioId: user.id,
      clientIds,
      eventId,
    };

    const result = await NotificationService.sendToClients(notificationData);

    return NextResponse.json({
      success: result.success,
      message: result.message,
      data: { sentCount: result.sentCount },
    });
  } catch (error) {
    console.error('Error sending notification:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
