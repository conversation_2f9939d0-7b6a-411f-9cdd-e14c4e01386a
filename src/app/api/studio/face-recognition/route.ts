import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, authorize<PERSON><PERSON> } from '@/lib/auth';
import { PhotoService } from '@/lib/services';

// Get face recognition status and statistics
export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeR<PERSON>(user, ['studio'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const result = await PhotoService.getFaceRecognitionStats(user.id);

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error fetching face recognition stats:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Process face recognition for photos
export async function POST(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['studio'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, photoIds, eventId, clientId } = body;

    if (!action) {
      return NextResponse.json(
        { success: false, message: 'Action is required' },
        { status: 400 }
      );
    }

    let result;

    switch (action) {
      case 'process-photos':
        if (!photoIds || !Array.isArray(photoIds)) {
          return NextResponse.json(
            { success: false, message: 'Photo IDs array is required' },
            { status: 400 }
          );
        }
        result = await PhotoService.processFaceRecognition(user.id, photoIds);
        break;

      case 'process-event':
        if (!eventId) {
          return NextResponse.json(
            { success: false, message: 'Event ID is required' },
            { status: 400 }
          );
        }
        result = await PhotoService.processEventPhotos(user.id, eventId);
        break;

      case 'match-client':
        if (!clientId) {
          return NextResponse.json(
            { success: false, message: 'Client ID is required' },
            { status: 400 }
          );
        }
        result = await PhotoService.matchClientPhotos(user.id, clientId);
        break;

      case 'rematch-all':
        result = await PhotoService.rematchAllPhotos(user.id);
        break;

      default:
        return NextResponse.json(
          { success: false, message: 'Invalid action' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      message: result.message,
      data: result.data,
    });
  } catch (error) {
    console.error('Error processing face recognition:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Update face recognition settings
export async function PUT(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['studio'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { threshold, autoProcess, batchSize } = body;

    const settings = {
      ...(typeof threshold === 'number' && { threshold }),
      ...(typeof autoProcess === 'boolean' && { autoProcess }),
      ...(typeof batchSize === 'number' && { batchSize }),
    };

    if (Object.keys(settings).length === 0) {
      return NextResponse.json(
        { success: false, message: 'No valid settings to update' },
        { status: 400 }
      );
    }

    const result = await PhotoService.updateFaceRecognitionSettings(user.id, settings);

    return NextResponse.json({
      success: true,
      message: 'Face recognition settings updated successfully',
      data: result,
    });
  } catch (error) {
    console.error('Error updating face recognition settings:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
