import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { authenticateRequest, authorizeRole } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['studio']) || !user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30'; // days
    const startDate = new Date(Date.now() - parseInt(period) * 24 * 60 * 60 * 1000);

    // Studio-specific analytics
    const [
      totalClients,
      activeClients,
      totalPhotos,
      matchedPhotos,
      totalDownloads,
      totalFavorites,
      storageUsed,
      clientEngagement,
      photosByEvent,
      clientActivity,
      uploadTrends,
      topClients,
    ] = await Promise.all([
      // Total clients
      prisma.client.count({
        where: { studioId: user.id },
      }),
      
      // Active clients (accessed in last 30 days)
      prisma.client.count({
        where: {
          studioId: user.id,
          lastAccess: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          },
        },
      }),
      
      // Total photos
      prisma.photo.count({
        where: { studioId: user.id },
      }),
      
      // Matched photos
      prisma.photo.count({
        where: { studioId: user.id, isMatched: true },
      }),
      
      // Total downloads (from access logs)
      prisma.accessLog.count({
        where: {
          client: { studioId: user.id },
          action: 'download_photo',
        },
      }),
      
      // Total favorites
      prisma.favorite.count({
        where: {
          client: { studioId: user.id },
        },
      }),
      
      // Storage usage
      prisma.studio.findUnique({
        where: { id: user.id },
        select: { storageUsed: true },
      }),
      
      // Client engagement (photos viewed, favorites, downloads)
      prisma.client.findMany({
        where: { studioId: user.id },
        select: {
          id: true,
          name: true,
          email: true,
          lastAccess: true,
          _count: {
            select: {
              photos: true,
              favorites: true,
              accessLogs: true,
            },
          },
        },
        orderBy: {
          lastAccess: 'desc',
        },
        take: 10,
      }),
      
      // Photos by event
      prisma.event.findMany({
        where: { studioId: user.id },
        select: {
          id: true,
          name: true,
          date: true,
          _count: {
            select: {
              photos: true,
            },
          },
        },
        orderBy: {
          date: 'desc',
        },
      }),
      
      // Client activity in the period
      prisma.accessLog.findMany({
        where: {
          client: { studioId: user.id },
          createdAt: { gte: startDate },
        },
        select: {
          action: true,
          createdAt: true,
          client: {
            select: {
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 50,
      }),
      
      // Upload trends (photos uploaded per day) - using Prisma aggregation
      prisma.photo.findMany({
        where: {
          studioId: user.id,
          uploadedAt: { gte: startDate },
        },
        select: {
          uploadedAt: true,
        },
        orderBy: {
          uploadedAt: 'asc',
        },
      }),
      
      // Top clients by engagement
      prisma.client.findMany({
        where: { studioId: user.id },
        select: {
          id: true,
          name: true,
          email: true,
          _count: {
            select: {
              photos: true,
              favorites: true,
              accessLogs: true,
            },
          },
        },
        orderBy: [
          { favorites: { _count: 'desc' } },
          { accessLogs: { _count: 'desc' } },
        ],
        take: 5,
      }),
    ]);

    // Calculate engagement metrics
    const engagementRate = totalClients > 0 ? (activeClients / totalClients) * 100 : 0;
    const matchingRate = totalPhotos > 0 ? (matchedPhotos / totalPhotos) * 100 : 0;
    const favoriteRate = matchedPhotos > 0 ? (totalFavorites / matchedPhotos) * 100 : 0;

    const analytics = {
      overview: {
        totalClients,
        activeClients,
        totalPhotos,
        matchedPhotos,
        unmatchedPhotos: totalPhotos - matchedPhotos,
        totalDownloads,
        totalFavorites,
        storageUsed: (storageUsed?.storageUsed || BigInt(0)).toString(),
      },
      metrics: {
        engagementRate: Math.round(engagementRate * 100) / 100,
        matchingRate: Math.round(matchingRate * 100) / 100,
        favoriteRate: Math.round(favoriteRate * 100) / 100,
        avgPhotosPerClient: totalClients > 0 ? Math.round(matchedPhotos / totalClients) : 0,
      },
      clientEngagement,
      photosByEvent,
      clientActivity,
      uploadTrends,
      topClients,
      periodStats: {
        period: parseInt(period),
        startDate: startDate.toISOString(),
        endDate: new Date().toISOString(),
      },
    };

    return NextResponse.json({
      success: true,
      data: analytics,
    });
  } catch (error) {
    console.error('Error fetching studio analytics:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Export studio analytics
export async function POST(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['studio']) || !user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { format = 'csv', period = '30', reportType = 'clients' } = body;

    const startDate = new Date(Date.now() - parseInt(period) * 24 * 60 * 60 * 1000);

    if (reportType === 'clients') {
      // Export client engagement report
      const clients = await prisma.client.findMany({
        where: { studioId: user.id },
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          createdAt: true,
          lastAccess: true,
          isActive: true,
          _count: {
            select: {
              photos: true,
              favorites: true,
              accessLogs: true,
            },
          },
        },
      });

      if (format === 'csv') {
        const csvHeaders = [
          'Client ID',
          'Name',
          'Email',
          'Phone',
          'Registration Date',
          'Last Access',
          'Status',
          'Photos Count',
          'Favorites Count',
          'Total Access Count',
        ];

        const csvRows = clients.map(client => [
          client.id,
          client.name,
          client.email,
          client.phone || '',
          client.createdAt.toISOString().split('T')[0],
          client.lastAccess ? client.lastAccess.toISOString().split('T')[0] : 'Never',
          client.isActive ? 'Active' : 'Inactive',
          client._count.photos,
          client._count.favorites,
          client._count.accessLogs,
        ]);

        const csvContent = [csvHeaders, ...csvRows]
          .map(row => row.map(field => `"${field}"`).join(','))
          .join('\n');

        return new NextResponse(csvContent, {
          headers: {
            'Content-Type': 'text/csv',
            'Content-Disposition': `attachment; filename="client-report-${period}days.csv"`,
          },
        });
      }

      return NextResponse.json({
        success: true,
        data: clients,
        reportType: 'clients',
        exportedAt: new Date().toISOString(),
        period: parseInt(period),
      });
    }

    if (reportType === 'photos') {
      // Export photo analytics report
      const photos = await prisma.photo.findMany({
        where: {
          studioId: user.id,
          uploadedAt: { gte: startDate },
        },
        select: {
          id: true,
          filename: true,
          originalName: true,
          uploadedAt: true,
          isMatched: true,
          isProcessed: true,
          size: true,
          client: {
            select: {
              name: true,
              email: true,
            },
          },
          event: {
            select: {
              name: true,
            },
          },
          _count: {
            select: {
              favorites: true,
            },
          },
        },
      });

      if (format === 'csv') {
        const csvHeaders = [
          'Photo ID',
          'Filename',
          'Original Name',
          'Upload Date',
          'Matched',
          'Processed',
          'Size (MB)',
          'Client Name',
          'Client Email',
          'Event',
          'Favorites Count',
        ];

        const csvRows = photos.map(photo => [
          photo.id,
          photo.filename,
          photo.originalName,
          photo.uploadedAt.toISOString().split('T')[0],
          photo.isMatched ? 'Yes' : 'No',
          photo.isProcessed ? 'Yes' : 'No',
          (Number(photo.size) / (1024 * 1024)).toFixed(2),
          photo.client?.name || 'Unmatched',
          photo.client?.email || '',
          photo.event?.name || '',
          photo._count.favorites,
        ]);

        const csvContent = [csvHeaders, ...csvRows]
          .map(row => row.map(field => `"${field}"`).join(','))
          .join('\n');

        return new NextResponse(csvContent, {
          headers: {
            'Content-Type': 'text/csv',
            'Content-Disposition': `attachment; filename="photo-report-${period}days.csv"`,
          },
        });
      }

      // Transform BigInt values to strings for JSON serialization
      const transformedPhotos = photos.map(photo => ({
        ...photo,
        size: photo.size.toString(),
      }));

      return NextResponse.json({
        success: true,
        data: transformedPhotos,
        reportType: 'photos',
        exportedAt: new Date().toISOString(),
        period: parseInt(period),
      });
    }

    return NextResponse.json(
      { success: false, message: 'Invalid report type' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error exporting studio analytics:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
