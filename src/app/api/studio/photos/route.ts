import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, authorize<PERSON><PERSON> } from '@/lib/auth';
import { PhotoService } from '@/lib/services';

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['studio'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const eventId = searchParams.get('eventId') || undefined;
    const isMatched = searchParams.get('isMatched') === 'true' ? true : 
                     searchParams.get('isMatched') === 'false' ? false : undefined;
    const isProcessed = searchParams.get('isProcessed') === 'true' ? true : 
                       searchParams.get('isProcessed') === 'false' ? false : undefined;
    const search = searchParams.get('search') || undefined;
    const sortBy = (searchParams.get('sortBy') as 'uploadedAt' | 'filename' | 'size') || 'uploadedAt';
    const sortOrder = (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc';

    const params = {
      page,
      limit,
      eventId,
      isMatched,
      isProcessed,
      search,
      sortBy,
      sortOrder,
    };

    const result = await PhotoService.getStudioPhotos(user.id, params);

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error fetching studio photos:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Update photo details
export async function PUT(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['studio'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { photoId, originalName, eventId, clientId, isMatched, isProcessed } = body;

    if (!photoId) {
      return NextResponse.json(
        { success: false, message: 'Photo ID is required' },
        { status: 400 }
      );
    }

    const updateData = {
      ...(originalName && { originalName }),
      ...(eventId && { eventId }),
      ...(clientId && { clientId }),
      ...(typeof isMatched === 'boolean' && { isMatched }),
      ...(typeof isProcessed === 'boolean' && { isProcessed }),
    };

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { success: false, message: 'No valid fields to update' },
        { status: 400 }
      );
    }

    const result = await PhotoService.updatePhoto(photoId, user.id, updateData);

    return NextResponse.json({
      success: true,
      message: 'Photo updated successfully',
      data: result,
    });
  } catch (error) {
    console.error('Error updating photo:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Delete photos
export async function DELETE(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    if (!authorizeRole(user, ['studio'])) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const photoIds = searchParams.get('photoIds')?.split(',') || [];

    if (photoIds.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Photo IDs are required' },
        { status: 400 }
      );
    }

    const result = await PhotoService.deletePhotos(photoIds, user.id);

    return NextResponse.json({
      success: true,
      message: `${result.deletedCount} photos deleted successfully`,
      data: { deletedCount: result.deletedCount },
    });
  } catch (error) {
    console.error('Error deleting photos:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
