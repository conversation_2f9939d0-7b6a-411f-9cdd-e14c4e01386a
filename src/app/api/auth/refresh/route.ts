import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { UserService } from '@/lib/services';

export async function POST(request: NextRequest) {
  try {
    const user = await authenticateRequest(request);
    
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    // Generate new token
    const result = await UserService.refreshToken(
      user.id,
      user.role
    );

    if (result.success) {
      // Create response with new token
      const response = NextResponse.json({
        success: true,
        message: 'Token refreshed successfully',
        token: result.token,
        user: result.user,
      });

      // Set new HTTP-only cookie
      if (result.token) {
        response.cookies.set('token', result.token, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
          maxAge: 7 * 24 * 60 * 60, // 7 days
        });
      }

      return response;
    } else {
      return NextResponse.json(
        { success: false, message: result.message },
        { status: result.status || 401 }
      );
    }
  } catch (error) {
    console.error('Token refresh error:', error);
    return NextResponse.json(
      { success: false, message: 'Token refresh failed' },
      { status: 500 }
    );
  }
}
