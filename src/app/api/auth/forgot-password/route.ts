import { NextRequest, NextResponse } from 'next/server';
import { UserService } from '@/lib/services';
import { isValidEmail } from '@/lib/utils';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, userType } = body;

    if (!email || !userType) {
      return NextResponse.json(
        { success: false, message: 'Email and user type are required' },
        { status: 400 }
      );
    }

    if (!isValidEmail(email)) {
      return NextResponse.json(
        { success: false, message: 'Invalid email format' },
        { status: 400 }
      );
    }

    if (!['admin', 'studio'].includes(userType)) {
      return NextResponse.json(
        { success: false, message: 'Invalid user type' },
        { status: 400 }
      );
    }

    const result = await UserService.initiatePasswordReset(
      email,
      userType
    );

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Password reset instructions have been sent to your email',
      });
    } else {
      return NextResponse.json(
        { success: false, message: result.message },
        { status: result.status || 400 }
      );
    }
  } catch (error) {
    console.error('Forgot password error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
