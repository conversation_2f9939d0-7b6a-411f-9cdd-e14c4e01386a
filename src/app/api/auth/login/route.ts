import { NextRequest, NextResponse } from 'next/server';
import { loginUser } from '@/lib/auth';
import { serializeBigInt } from '@/lib/utils';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password, userType } = body;

    // Validate input
    if (!email || !password || !userType) {
      return NextResponse.json(
        { success: false, message: 'Email, password, and user type are required' },
        { status: 400 }
      );
    }

    // Validate user type (clients use separate access system)
    if (!['admin', 'studio'].includes(userType)) {
      return NextResponse.json(
        { success: false, message: 'Invalid user type. Clients should use the client access portal.' },
        { status: 400 }
      );
    }

    // Attempt login
    const result = await loginUser(
      email,
      password,
      userType
    );

    if (result.success) {
      // Create response with token (serialize BigInt values)
      const responseData = {
        success: true,
        message: 'Login successful',
        user: serializeBigInt(result.user),
        token: result.token,
      };
      const response = NextResponse.json(responseData);

      // Set HTTP-only cookie for additional security
      if (result.token) {
        response.cookies.set('token', result.token, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
          maxAge: 7 * 24 * 60 * 60, // 7 days
        });
      }

      return response;
    } else {
      return NextResponse.json(
        { success: false, message: result.message },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
