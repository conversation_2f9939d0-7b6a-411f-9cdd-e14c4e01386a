"use client";

import { useState, useEffect } from "react";
import { 
  Shield,
  Key,
  Smartphone,
  Eye,
  EyeOff,
  Clock,
  MapPin,
  Monitor,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Download,
  Settings,
  Lock,
  Unlock,
  Globe,
  Activity
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import StudioLayout from "@/components/studio/StudioLayout";
import { formatDateTime, timeAgo, cn } from "@/lib/utils";

interface LoginActivity {
  id: string;
  timestamp: string;
  ipAddress: string;
  location: string;
  device: string;
  browser: string;
  status: 'success' | 'failed' | 'blocked';
}

interface SecuritySettings {
  twoFactorEnabled: boolean;
  loginNotifications: boolean;
  sessionTimeout: number;
  ipWhitelist: string[];
  maxLoginAttempts: number;
  passwordExpiry: number;
}

export default function SecurityPage() {
  const [securityScore, setSecurityScore] = useState(85);
  const [showPassword, setShowPassword] = useState(false);
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [twoFactorSecret, setTwoFactorSecret] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [loading, setLoading] = useState(false);

  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
    twoFactorEnabled: false,
    loginNotifications: true,
    sessionTimeout: 30,
    ipWhitelist: [],
    maxLoginAttempts: 5,
    passwordExpiry: 90,
  });

  const [loginActivity, setLoginActivity] = useState<LoginActivity[]>([
    {
      id: '1',
      timestamp: '2024-01-15T10:30:00Z',
      ipAddress: '*************',
      location: 'Mumbai, India',
      device: 'MacBook Pro',
      browser: 'Chrome 120.0',
      status: 'success'
    },
    {
      id: '2',
      timestamp: '2024-01-15T08:15:00Z',
      ipAddress: '*************',
      location: 'Delhi, India',
      device: 'iPhone 15',
      browser: 'Safari 17.0',
      status: 'success'
    },
    {
      id: '3',
      timestamp: '2024-01-14T22:45:00Z',
      ipAddress: '************',
      location: 'Unknown',
      device: 'Windows PC',
      browser: 'Firefox 121.0',
      status: 'failed'
    },
    {
      id: '4',
      timestamp: '2024-01-14T16:20:00Z',
      ipAddress: '*************',
      location: 'Mumbai, India',
      device: 'MacBook Pro',
      browser: 'Chrome 120.0',
      status: 'success'
    },
    {
      id: '5',
      timestamp: '2024-01-14T12:00:00Z',
      ipAddress: '************',
      location: 'Bangalore, India',
      device: 'Android Phone',
      browser: 'Chrome Mobile',
      status: 'blocked'
    }
  ]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return CheckCircle;
      case 'failed': return XCircle;
      case 'blocked': return Shield;
      default: return Activity;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'failed': return 'text-red-600';
      case 'blocked': return 'text-orange-600';
      default: return 'text-gray-600';
    }
  };

  const getSecurityScoreColor = () => {
    if (securityScore >= 80) return 'text-green-600';
    if (securityScore >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const handlePasswordChange = async () => {
    if (newPassword !== confirmPassword) {
      alert("Passwords don't match");
      return;
    }
    
    setLoading(true);
    // TODO: Implement password change API call
    setTimeout(() => {
      setLoading(false);
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
      alert("Password changed successfully");
    }, 2000);
  };

  const handleEnable2FA = async () => {
    setLoading(true);
    // TODO: Implement 2FA setup API call
    setTimeout(() => {
      setLoading(false);
      setSecuritySettings(prev => ({ ...prev, twoFactorEnabled: true }));
      setSecurityScore(prev => prev + 10);
    }, 2000);
  };

  const exportLoginLogs = () => {
    const csvContent = [
      ['Timestamp', 'IP Address', 'Location', 'Device', 'Browser', 'Status'],
      ...loginActivity.map(log => [
        formatDateTime(log.timestamp),
        log.ipAddress,
        log.location,
        log.device,
        log.browser,
        log.status
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'login-activity.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <StudioLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <Shield className="h-8 w-8 text-blue-600" />
              Security & Privacy
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Manage your account security settings and monitor login activity
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Badge variant={securityScore >= 80 ? 'success' : securityScore >= 60 ? 'warning' : 'destructive'}>
              Security Score: {securityScore}%
            </Badge>
          </div>
        </div>

        {/* Security Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-green-700 dark:text-green-300">Security Score</CardTitle>
              <div className="h-8 w-8 bg-green-600 rounded-lg flex items-center justify-center">
                <Shield className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className={cn("text-2xl font-bold", getSecurityScoreColor())}>
                {securityScore}%
              </div>
              <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                {securityScore >= 80 ? 'Excellent' : securityScore >= 60 ? 'Good' : 'Needs Improvement'}
              </p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">Active Sessions</CardTitle>
              <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <Monitor className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                3
              </div>
              <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                Devices logged in
              </p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-orange-700 dark:text-orange-300">Failed Attempts</CardTitle>
              <div className="h-8 w-8 bg-orange-600 rounded-lg flex items-center justify-center">
                <AlertTriangle className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                2
              </div>
              <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                Last 24 hours
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Security Settings */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Two-Factor Authentication */}
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Smartphone className="h-5 w-5 text-blue-600" />
                Two-Factor Authentication
              </CardTitle>
              <CardDescription>
                Add an extra layer of security to your account
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Enable 2FA</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Require a verification code from your phone
                  </p>
                </div>
                <Switch
                  checked={securitySettings.twoFactorEnabled}
                  onCheckedChange={(checked) => {
                    if (checked && !securitySettings.twoFactorEnabled) {
                      handleEnable2FA();
                    } else {
                      setSecuritySettings(prev => ({ ...prev, twoFactorEnabled: checked }));
                    }
                  }}
                />
              </div>
              
              {!securitySettings.twoFactorEnabled && (
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <p className="text-sm text-blue-800 dark:text-blue-200 mb-3">
                    Scan this QR code with your authenticator app:
                  </p>
                  <div className="w-32 h-32 bg-white border-2 border-blue-200 rounded-lg flex items-center justify-center mb-3">
                    <span className="text-xs text-gray-500">QR Code</span>
                  </div>
                  <Input
                    placeholder="Enter verification code"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                    className="mb-3"
                  />
                  <Button onClick={handleEnable2FA} disabled={loading} size="sm">
                    {loading ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : <Key className="h-4 w-4 mr-2" />}
                    Enable 2FA
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Password Change */}
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5 text-green-600" />
                Change Password
              </CardTitle>
              <CardDescription>
                Update your password to keep your account secure
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="relative">
                  <Input
                    type={showPassword ? "text" : "password"}
                    placeholder="Current password"
                    value={currentPassword}
                    onChange={(e) => setCurrentPassword(e.target.value)}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <Input
                  type="password"
                  placeholder="New password"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                />
                <Input
                  type="password"
                  placeholder="Confirm new password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                />
              </div>
              <Button 
                onClick={handlePasswordChange} 
                disabled={loading || !currentPassword || !newPassword || !confirmPassword}
                className="w-full"
              >
                {loading ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : <Lock className="h-4 w-4 mr-2" />}
                Change Password
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Login Activity */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5 text-purple-600" />
                  Login Activity
                </CardTitle>
                <CardDescription>
                  Recent login attempts and device access history
                </CardDescription>
              </div>
              <Button variant="outline" size="sm" onClick={exportLoginLogs}>
                <Download className="h-4 w-4 mr-2" />
                Export Logs
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {loginActivity.map((log) => {
                const StatusIcon = getStatusIcon(log.status);
                return (
                  <div key={log.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className={cn("p-2 rounded-lg", 
                        log.status === 'success' ? 'bg-green-100 dark:bg-green-900/20' :
                        log.status === 'failed' ? 'bg-red-100 dark:bg-red-900/20' :
                        'bg-orange-100 dark:bg-orange-900/20'
                      )}>
                        <StatusIcon className={cn("h-4 w-4", getStatusColor(log.status))} />
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <p className="font-medium text-gray-900 dark:text-white">
                            {log.device} • {log.browser}
                          </p>
                          <Badge variant={log.status === 'success' ? 'success' : log.status === 'failed' ? 'destructive' : 'warning'}>
                            {log.status}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                          <span className="flex items-center gap-1">
                            <Globe className="h-3 w-3" />
                            {log.ipAddress}
                          </span>
                          <span className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {log.location}
                          </span>
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {timeAgo(log.timestamp)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </StudioLayout>
  );
}
