"use client";

import { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  User,
  <PERSON>,
  Shield,
  Palette,
  Globe,
  Camera,
  Mail,
  Phone,
  MapPin,
  Building,
  Save,
  Eye,
  EyeOff,
  Upload,
  Trash2,
  Edit,
  Key,
  Smartphone,
  Monitor,
  Sun,
  Moon,
  Laptop,
  Users,
  Lock,
  Unlock,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info,
  Star,
  Heart,
  Calendar,
  Clock,
  Zap,
  Activity,
  TrendingUp,
  BarChart3,
  Target,
  Lightbulb,
  Layers,
  RefreshCw,
  Download,
  Share2,
  Copy,
  Link,
  QrCode,
  Image,
  Type,
  Layout,
  Code,
  Tablet,
  Brush,
  Paintbrush
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Textarea } from "@/components/ui/textarea";
import StudioLayout from "@/components/studio/StudioLayout";
import { formatDate, timeAgo, cn } from "@/lib/utils";

interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: 'owner' | 'admin' | 'photographer' | 'editor' | 'viewer';
  avatar?: string;
  isActive: boolean;
  lastLogin: string;
  permissions: {
    canUpload: boolean;
    canEdit: boolean;
    canDelete: boolean;
    canManageClients: boolean;
    canViewAnalytics: boolean;
    canManageSettings: boolean;
    canManageTeam: boolean;
    canManageBilling: boolean;
  };
  joinedAt: string;
  invitedBy?: string;
}

interface AccessLog {
  id: string;
  userId: string;
  userName: string;
  action: string;
  resource: string;
  timestamp: string;
  ipAddress: string;
  userAgent: string;
  success: boolean;
}

interface StudioSettings {
  profile: {
    studioName: string;
    ownerName: string;
    email: string;
    phone: string;
    address: string;
    city: string;
    country: string;
    website: string;
    bio: string;
    logo: string;
  };
  notifications: {
    emailNotifications: boolean;
    pushNotifications: boolean;
    smsNotifications: boolean;
    newClientSignup: boolean;
    photoUploaded: boolean;
    downloadAlert: boolean;
    weeklyReport: boolean;
    securityAlerts: boolean;
  };
  privacy: {
    profileVisibility: 'public' | 'private';
    showContactInfo: boolean;
    allowClientReviews: boolean;
    dataRetention: number; // days
    autoDeleteInactive: boolean;
  };
  appearance: {
    theme: 'light' | 'dark' | 'system';
    accentColor: string;
    compactMode: boolean;
    showAnimations: boolean;
  };
  watermark: {
    enabled: boolean;
    text: string;
    position: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' | 'center';
    opacity: number;
    fontSize: number;
  };
}

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('profile');
  const [showPassword, setShowPassword] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [selectedMember, setSelectedMember] = useState<string | null>(null);
  const [settings, setSettings] = useState<StudioSettings>({
    profile: {
      studioName: 'PhotoStudio Pro',
      ownerName: 'John Doe',
      email: '<EMAIL>',
      phone: '****** 567 8900',
      address: '123 Photography Street',
      city: 'New York',
      country: 'United States',
      website: 'https://photostudio.com',
      bio: 'Professional wedding and event photographer with 10+ years of experience.',
      logo: '/api/placeholder/150/150'
    },
    notifications: {
      emailNotifications: true,
      pushNotifications: true,
      smsNotifications: false,
      newClientSignup: true,
      photoUploaded: true,
      downloadAlert: false,
      weeklyReport: true,
      securityAlerts: true
    },
    privacy: {
      profileVisibility: 'public',
      showContactInfo: true,
      allowClientReviews: true,
      dataRetention: 365,
      autoDeleteInactive: false
    },
    appearance: {
      theme: 'system',
      accentColor: '#3B82F6',
      compactMode: false,
      showAnimations: true
    },
    watermark: {
      enabled: true,
      text: 'PhotoStudio Pro',
      position: 'bottom-right',
      opacity: 50,
      fontSize: 16
    }
  });

  const [teamMembers] = useState<TeamMember[]>([
    {
      id: '1',
      name: 'John Smith',
      email: '<EMAIL>',
      role: 'owner',
      avatar: '/api/placeholder/40/40',
      isActive: true,
      lastLogin: '2024-01-21T18:30:00Z',
      permissions: {
        canUpload: true,
        canEdit: true,
        canDelete: true,
        canManageClients: true,
        canViewAnalytics: true,
        canManageSettings: true,
        canManageTeam: true,
        canManageBilling: true
      },
      joinedAt: '2024-01-01T10:00:00Z'
    },
    {
      id: '2',
      name: 'Emma Wilson',
      email: '<EMAIL>',
      role: 'photographer',
      avatar: '/api/placeholder/40/40',
      isActive: true,
      lastLogin: '2024-01-21T16:45:00Z',
      permissions: {
        canUpload: true,
        canEdit: true,
        canDelete: false,
        canManageClients: true,
        canViewAnalytics: false,
        canManageSettings: false,
        canManageTeam: false,
        canManageBilling: false
      },
      joinedAt: '2024-01-05T14:20:00Z',
      invitedBy: 'John Smith'
    },
    {
      id: '3',
      name: 'Mike Davis',
      email: '<EMAIL>',
      role: 'editor',
      isActive: false,
      lastLogin: '2024-01-18T12:30:00Z',
      permissions: {
        canUpload: false,
        canEdit: true,
        canDelete: false,
        canManageClients: false,
        canViewAnalytics: false,
        canManageSettings: false,
        canManageTeam: false,
        canManageBilling: false
      },
      joinedAt: '2024-01-10T09:15:00Z',
      invitedBy: 'John Smith'
    }
  ]);

  const filteredMembers = teamMembers.filter(member =>
    member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.role.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'owner': return 'bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300';
      case 'admin': return 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300';
      case 'photographer': return 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300';
      case 'editor': return 'bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300';
      case 'viewer': return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'team', label: 'Team Management', icon: Users },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'privacy', label: 'Privacy', icon: Shield },
    { id: 'appearance', label: 'Appearance', icon: Palette },
    { id: 'watermark', label: 'Watermark', icon: Camera }
  ];

  const updateSettings = (section: keyof StudioSettings, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));
  };

  const handleSave = () => {
    // Save settings logic here
    console.log('Saving settings:', settings);
  };

  const renderProfileTab = () => (
    <div className="space-y-6">
      <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5 text-blue-600" />
            Studio Profile
          </CardTitle>
          <CardDescription>
            Update your studio information and contact details
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Logo Upload */}
          <div className="flex items-center gap-6">
            <div className="relative">
              <img
                src={settings.profile.logo}
                alt="Studio Logo"
                className="w-24 h-24 rounded-lg object-cover border-2 border-gray-200 dark:border-gray-600"
              />
              <Button
                size="sm"
                className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full p-0"
              >
                <Upload className="h-4 w-4" />
              </Button>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white">Studio Logo</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                Upload a logo for your studio (recommended: 200x200px)
              </p>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Upload className="h-4 w-4 mr-2" />
                  Upload New
                </Button>
                <Button variant="outline" size="sm">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Remove
                </Button>
              </div>
            </div>
          </div>

          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Studio Name
              </label>
              <Input
                value={settings.profile.studioName}
                onChange={(e) => updateSettings('profile', 'studioName', e.target.value)}
                placeholder="Your Studio Name"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Owner Name
              </label>
              <Input
                value={settings.profile.ownerName}
                onChange={(e) => updateSettings('profile', 'ownerName', e.target.value)}
                placeholder="Your Full Name"
              />
            </div>
          </div>

          {/* Contact Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Email Address
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <Input
                  type="email"
                  value={settings.profile.email}
                  onChange={(e) => updateSettings('profile', 'email', e.target.value)}
                  className="pl-10"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Phone Number
              </label>
              <div className="relative">
                <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <Input
                  type="tel"
                  value={settings.profile.phone}
                  onChange={(e) => updateSettings('profile', 'phone', e.target.value)}
                  className="pl-10"
                  placeholder="****** 567 8900"
                />
              </div>
            </div>
          </div>

          {/* Address */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Address
            </label>
            <div className="relative">
              <MapPin className="absolute left-3 top-3 text-gray-400 h-5 w-5" />
              <Input
                value={settings.profile.address}
                onChange={(e) => updateSettings('profile', 'address', e.target.value)}
                className="pl-10"
                placeholder="123 Photography Street"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                City
              </label>
              <Input
                value={settings.profile.city}
                onChange={(e) => updateSettings('profile', 'city', e.target.value)}
                placeholder="New York"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Country
              </label>
              <Input
                value={settings.profile.country}
                onChange={(e) => updateSettings('profile', 'country', e.target.value)}
                placeholder="United States"
              />
            </div>
          </div>

          {/* Website */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Website
            </label>
            <div className="relative">
              <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                type="url"
                value={settings.profile.website}
                onChange={(e) => updateSettings('profile', 'website', e.target.value)}
                className="pl-10"
                placeholder="https://yourstudio.com"
              />
            </div>
          </div>

          {/* Bio */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Bio
            </label>
            <textarea
              value={settings.profile.bio}
              onChange={(e) => updateSettings('profile', 'bio', e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none"
              placeholder="Tell clients about your studio and experience..."
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderNotificationsTab = () => (
    <div className="space-y-6">
      <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5 text-green-600" />
            Notification Preferences
          </CardTitle>
          <CardDescription>
            Choose how you want to be notified about important events
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Notification Channels */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Notification Channels
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Mail className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">Email Notifications</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Receive notifications via email
                    </p>
                  </div>
                </div>
                <Switch
                  checked={settings.notifications.emailNotifications}
                  onCheckedChange={(checked) => updateSettings('notifications', 'emailNotifications', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Bell className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">Push Notifications</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Receive push notifications in browser
                    </p>
                  </div>
                </div>
                <Switch
                  checked={settings.notifications.pushNotifications}
                  onCheckedChange={(checked) => updateSettings('notifications', 'pushNotifications', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Smartphone className="h-5 w-5 text-purple-600" />
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">SMS Notifications</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Receive important alerts via SMS
                    </p>
                  </div>
                </div>
                <Switch
                  checked={settings.notifications.smsNotifications}
                  onCheckedChange={(checked) => updateSettings('notifications', 'smsNotifications', checked)}
                />
              </div>
            </div>
          </div>

          {/* Event Notifications */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Event Notifications
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">New Client Signup</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    When a new client creates an account
                  </p>
                </div>
                <Switch
                  checked={settings.notifications.newClientSignup}
                  onCheckedChange={(checked) => updateSettings('notifications', 'newClientSignup', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">Photo Uploaded</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    When photos are successfully uploaded
                  </p>
                </div>
                <Switch
                  checked={settings.notifications.photoUploaded}
                  onCheckedChange={(checked) => updateSettings('notifications', 'photoUploaded', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">Download Alert</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    When clients download photos
                  </p>
                </div>
                <Switch
                  checked={settings.notifications.downloadAlert}
                  onCheckedChange={(checked) => updateSettings('notifications', 'downloadAlert', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">Weekly Report</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Weekly summary of studio activity
                  </p>
                </div>
                <Switch
                  checked={settings.notifications.weeklyReport}
                  onCheckedChange={(checked) => updateSettings('notifications', 'weeklyReport', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">Security Alerts</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Important security notifications
                  </p>
                </div>
                <Switch
                  checked={settings.notifications.securityAlerts}
                  onCheckedChange={(checked) => updateSettings('notifications', 'securityAlerts', checked)}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderAppearanceTab = () => (
    <div className="space-y-6">
      <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5 text-purple-600" />
            Appearance Settings
          </CardTitle>
          <CardDescription>
            Customize the look and feel of your dashboard
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Theme Selection */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Theme
            </h3>
            <div className="grid grid-cols-3 gap-4">
              {[
                { id: 'light', label: 'Light', icon: Sun },
                { id: 'dark', label: 'Dark', icon: Moon },
                { id: 'system', label: 'System', icon: Laptop }
              ].map((theme) => {
                const Icon = theme.icon;
                return (
                  <button
                    key={theme.id}
                    onClick={() => updateSettings('appearance', 'theme', theme.id)}
                    className={cn(
                      "p-4 border-2 rounded-lg transition-all duration-200 flex flex-col items-center gap-2",
                      settings.appearance.theme === theme.id
                        ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                        : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                    )}
                  >
                    <Icon className="h-6 w-6 text-gray-600 dark:text-gray-400" />
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {theme.label}
                    </span>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Accent Color */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Accent Color
            </h3>
            <div className="flex items-center gap-4">
              <input
                type="color"
                value={settings.appearance.accentColor}
                onChange={(e) => updateSettings('appearance', 'accentColor', e.target.value)}
                className="w-12 h-12 rounded-lg border-2 border-gray-200 dark:border-gray-600 cursor-pointer"
              />
              <div>
                <p className="font-medium text-gray-900 dark:text-white">
                  {settings.appearance.accentColor}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Choose your preferred accent color
                </p>
              </div>
            </div>
          </div>

          {/* Display Options */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Display Options
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">Compact Mode</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Reduce spacing and padding for more content
                  </p>
                </div>
                <Switch
                  checked={settings.appearance.compactMode}
                  onCheckedChange={(checked) => updateSettings('appearance', 'compactMode', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">Show Animations</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Enable smooth transitions and animations
                  </p>
                </div>
                <Switch
                  checked={settings.appearance.showAnimations}
                  onCheckedChange={(checked) => updateSettings('appearance', 'showAnimations', checked)}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <StudioLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <Settings className="h-8 w-8 text-blue-600" />
              Settings
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Manage your studio preferences and configuration
            </p>
          </div>
          <Button onClick={handleSave} className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Settings Navigation */}
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800 lg:col-span-1">
            <CardContent className="p-4">
              <nav className="space-y-2">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={cn(
                        "w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors",
                        activeTab === tab.id
                          ? "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300"
                          : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                      )}
                    >
                      <Icon className="h-5 w-5" />
                      {tab.label}
                    </button>
                  );
                })}
              </nav>
            </CardContent>
          </Card>

          {/* Settings Content */}
          <div className="lg:col-span-3">
            {activeTab === 'profile' && renderProfileTab()}
            {activeTab === 'team' && (
              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        <Users className="h-5 w-5 text-blue-600" />
                        Team Management
                      </CardTitle>
                      <CardDescription>
                        Manage team members, roles, and permissions
                      </CardDescription>
                    </div>
                    <Button onClick={() => setShowInviteModal(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Invite Member
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Search */}
                  <div className="mb-6">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                      <Input
                        placeholder="Search team members..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  {/* Team Members List */}
                  <div className="space-y-4">
                    {filteredMembers.map((member) => (
                      <div key={member.id} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <img
                              src={member.avatar || '/api/placeholder/40/40'}
                              alt={member.name}
                              className="w-10 h-10 rounded-full"
                            />
                            <div>
                              <div className="flex items-center gap-2">
                                <h3 className="font-semibold text-gray-900 dark:text-white">
                                  {member.name}
                                </h3>
                                <Badge className={getRoleColor(member.role)}>
                                  {member.role}
                                </Badge>
                                {!member.isActive && (
                                  <Badge variant="secondary">Inactive</Badge>
                                )}
                              </div>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                {member.email}
                              </p>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                Last login: {timeAgo(member.lastLogin)} • Joined: {formatDate(member.joinedAt)}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </Button>
                            {member.role !== 'owner' && (
                              <Button variant="outline" size="sm">
                                <Trash2 className="h-4 w-4 mr-2" />
                                Remove
                              </Button>
                            )}
                          </div>
                        </div>

                        {/* Permissions */}
                        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                            {Object.entries(member.permissions).map(([permission, hasPermission]) => (
                              <div key={permission} className="flex items-center gap-2 text-sm">
                                {hasPermission ? (
                                  <CheckCircle className="h-4 w-4 text-green-600" />
                                ) : (
                                  <XCircle className="h-4 w-4 text-gray-400" />
                                )}
                                <span className={hasPermission ? 'text-gray-900 dark:text-white' : 'text-gray-500'}>
                                  {permission.replace('can', '').replace(/([A-Z])/g, ' $1').trim()}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
            {activeTab === 'notifications' && renderNotificationsTab()}
            {activeTab === 'appearance' && renderAppearanceTab()}
            {activeTab === 'privacy' && (
              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5 text-red-600" />
                    Privacy Settings
                  </CardTitle>
                  <CardDescription>
                    Control your privacy and data retention preferences
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 dark:text-gray-400">
                    Privacy settings content will be implemented here...
                  </p>
                </CardContent>
              </Card>
            )}
            {activeTab === 'watermark' && (
              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Camera className="h-5 w-5 text-orange-600" />
                    Watermark Settings
                  </CardTitle>
                  <CardDescription>
                    Configure watermarks for your photos
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 dark:text-gray-400">
                    Watermark settings content will be implemented here...
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </StudioLayout>
  );
}
