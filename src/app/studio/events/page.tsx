"use client";

import { useState } from "react";
import { 
  Calendar,
  Clock,
  MapPin,
  Users,
  Camera,
  Plus,
  Edit,
  Trash2,
  Eye,
  Search,
  Filter,
  MoreHorizontal,
  CheckCircle,
  AlertTriangle,
  Star,
  FileText,
  Image,
  Video,
  Download,
  Share2,
  Settings,
  CalendarDays,
  Timer,
  User,
  Phone,
  Mail,
  DollarSign,
  Package
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import StudioLayout from "@/components/studio/StudioLayout";
import { formatDate, formatCurrency, timeAgo, cn } from "@/lib/utils";

interface Event {
  id: string;
  title: string;
  type: 'wedding' | 'birthday' | 'corporate' | 'engagement' | 'family' | 'graduation' | 'other';
  date: string;
  startTime: string;
  endTime: string;
  location: string;
  clientId: string;
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  status: 'upcoming' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high';
  packageType: string;
  price: number;
  deposit: number;
  balance: number;
  notes: string;
  requirements: string[];
  equipment: string[];
  crew: string[];
  photosCount?: number;
  videosCount?: number;
  deliveryDate?: string;
  isStarred: boolean;
  createdAt: string;
  updatedAt: string;
}

interface EventStats {
  totalEvents: number;
  upcomingEvents: number;
  completedEvents: number;
  totalRevenue: number;
  pendingBalance: number;
}

export default function EventsPage() {
  const [activeTab, setActiveTab] = useState("calendar");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [viewMode, setViewMode] = useState<"list" | "grid">("list");

  const [events] = useState<Event[]>([
    {
      id: '1',
      title: 'Sarah & John Wedding',
      type: 'wedding',
      date: '2024-02-15',
      startTime: '14:00',
      endTime: '22:00',
      location: 'Grand Ballroom, Downtown Hotel',
      clientId: 'client1',
      clientName: 'Sarah Johnson',
      clientEmail: '<EMAIL>',
      clientPhone: '+****************',
      status: 'upcoming',
      priority: 'high',
      packageType: 'Premium Wedding Package',
      price: 2500,
      deposit: 1000,
      balance: 1500,
      notes: 'Outdoor ceremony, indoor reception. Client wants candid shots during cocktail hour.',
      requirements: ['Drone photography', 'Second shooter', 'Same-day highlights'],
      equipment: ['Canon R5', 'Drone', 'Lighting kit', 'Backup cameras'],
      crew: ['Main photographer', 'Second shooter', 'Videographer'],
      deliveryDate: '2024-03-01',
      isStarred: true,
      createdAt: '2024-01-10T10:00:00Z',
      updatedAt: '2024-01-20T14:30:00Z'
    },
    {
      id: '2',
      title: 'Mike\'s 30th Birthday',
      type: 'birthday',
      date: '2024-01-25',
      startTime: '18:00',
      endTime: '23:00',
      location: 'Private Residence, 123 Oak Street',
      clientId: 'client2',
      clientName: 'Mike Davis',
      clientEmail: '<EMAIL>',
      clientPhone: '+****************',
      status: 'completed',
      priority: 'medium',
      packageType: 'Party Photography',
      price: 800,
      deposit: 300,
      balance: 0,
      notes: 'Surprise party theme. Focus on candid moments and group shots.',
      requirements: ['Party atmosphere shots', 'Group photos'],
      equipment: ['Canon R6', 'Flash kit', 'Backup lens'],
      crew: ['Main photographer'],
      photosCount: 150,
      videosCount: 5,
      deliveryDate: '2024-02-05',
      isStarred: false,
      createdAt: '2024-01-05T15:20:00Z',
      updatedAt: '2024-01-26T09:15:00Z'
    },
    {
      id: '3',
      title: 'Emma & David Engagement',
      type: 'engagement',
      date: '2024-01-20',
      startTime: '16:00',
      endTime: '18:00',
      location: 'Central Park, New York',
      clientId: 'client3',
      clientName: 'Emma Wilson',
      clientEmail: '<EMAIL>',
      clientPhone: '+****************',
      status: 'completed',
      priority: 'low',
      packageType: 'Engagement Session',
      price: 500,
      deposit: 200,
      balance: 0,
      notes: 'Golden hour session. Couple prefers natural, candid style.',
      requirements: ['Outdoor session', 'Natural lighting'],
      equipment: ['Canon R5', 'Prime lenses', 'Reflector'],
      crew: ['Main photographer'],
      photosCount: 80,
      deliveryDate: '2024-01-30',
      isStarred: false,
      createdAt: '2024-01-08T11:45:00Z',
      updatedAt: '2024-01-21T16:20:00Z'
    },
    {
      id: '4',
      title: 'TechCorp Annual Meeting',
      type: 'corporate',
      date: '2024-02-20',
      startTime: '09:00',
      endTime: '17:00',
      location: 'TechCorp Headquarters, Conference Center',
      clientId: 'client4',
      clientName: 'Jennifer Smith',
      clientEmail: '<EMAIL>',
      clientPhone: '+****************',
      status: 'upcoming',
      priority: 'medium',
      packageType: 'Corporate Event Coverage',
      price: 1200,
      deposit: 600,
      balance: 600,
      notes: 'Professional headshots needed for executives. Event documentation required.',
      requirements: ['Headshots', 'Event documentation', 'Group photos'],
      equipment: ['Canon R6', 'Studio lighting', 'Backdrop'],
      crew: ['Main photographer', 'Assistant'],
      deliveryDate: '2024-02-25',
      isStarred: false,
      createdAt: '2024-01-15T13:30:00Z',
      updatedAt: '2024-01-18T10:45:00Z'
    }
  ]);

  const eventStats: EventStats = {
    totalEvents: events.length,
    upcomingEvents: events.filter(e => e.status === 'upcoming').length,
    completedEvents: events.filter(e => e.status === 'completed').length,
    totalRevenue: events.reduce((sum, e) => sum + (e.price - e.balance), 0),
    pendingBalance: events.reduce((sum, e) => sum + e.balance, 0)
  };

  const filteredEvents = events.filter(event => {
    const matchesSearch = event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.location.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || event.status === statusFilter;
    const matchesType = typeFilter === "all" || event.type === typeFilter;
    return matchesSearch && matchesStatus && matchesType;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming': return 'default';
      case 'in_progress': return 'warning';
      case 'completed': return 'success';
      case 'cancelled': return 'destructive';
      default: return 'secondary';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'wedding': return 'bg-pink-100 dark:bg-pink-900/20 text-pink-700 dark:text-pink-300';
      case 'birthday': return 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300';
      case 'corporate': return 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300';
      case 'engagement': return 'bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300';
      case 'family': return 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300';
      case 'graduation': return 'bg-indigo-100 dark:bg-indigo-900/20 text-indigo-700 dark:text-indigo-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
      case 'low': return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-700';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'wedding': return '💒';
      case 'birthday': return '🎂';
      case 'corporate': return '🏢';
      case 'engagement': return '💍';
      case 'family': return '👨‍👩‍👧‍👦';
      case 'graduation': return '🎓';
      default: return '📸';
    }
  };

  return (
    <StudioLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <Calendar className="h-8 w-8 text-blue-600" />
              Event Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Organize, track, and manage your photography events and bookings
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
              <Plus className="h-4 w-4 mr-2" />
              New Event
            </Button>
          </div>
        </div>

        {/* Event Stats */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <Calendar className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {eventStats.totalEvents}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Total Events</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-orange-600 rounded-xl flex items-center justify-center">
                  <Clock className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {eventStats.upcomingEvents}
                  </p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">Upcoming</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {eventStats.completedEvents}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Completed</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {formatCurrency(eventStats.totalRevenue)}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Revenue</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-red-600 rounded-xl flex items-center justify-center">
                  <AlertTriangle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-red-900 dark:text-red-100">
                    {formatCurrency(eventStats.pendingBalance)}
                  </p>
                  <p className="text-sm text-red-600 dark:text-red-400">Pending</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="calendar">Calendar View</TabsTrigger>
            <TabsTrigger value="events">All Events</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          {/* Calendar View Tab */}
          <TabsContent value="calendar" className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CalendarDays className="h-5 w-5 text-blue-600" />
                  Event Calendar
                </CardTitle>
                <CardDescription>
                  Visual overview of your scheduled events
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <Calendar className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Calendar Integration
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-4">
                    Interactive calendar view would be implemented here with a calendar library
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-8">
                    {events.filter(e => e.status === 'upcoming').map(event => (
                      <div key={event.id} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-lg">{getTypeIcon(event.type)}</span>
                          <h4 className="font-semibold text-gray-900 dark:text-white">
                            {event.title}
                          </h4>
                        </div>
                        <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                          <div className="flex items-center gap-2">
                            <Calendar className="h-3 w-3" />
                            <span>{formatDate(event.date)}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Clock className="h-3 w-3" />
                            <span>{event.startTime} - {event.endTime}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <MapPin className="h-3 w-3" />
                            <span className="truncate">{event.location}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* All Events Tab */}
          <TabsContent value="events" className="space-y-6">
            {/* Filters */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                      <Input
                        placeholder="Search events by title, client, or location..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 h-11 bg-gray-50 dark:bg-gray-700"
                      />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="all">All Status</option>
                      <option value="upcoming">Upcoming</option>
                      <option value="in_progress">In Progress</option>
                      <option value="completed">Completed</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                    <select
                      value={typeFilter}
                      onChange={(e) => setTypeFilter(e.target.value)}
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="all">All Types</option>
                      <option value="wedding">Wedding</option>
                      <option value="birthday">Birthday</option>
                      <option value="corporate">Corporate</option>
                      <option value="engagement">Engagement</option>
                      <option value="family">Family</option>
                      <option value="graduation">Graduation</option>
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Events List */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="space-y-4">
                  {filteredEvents.map(event => (
                    <div key={event.id} className="p-6 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-4">
                          <div className="text-3xl">{getTypeIcon(event.type)}</div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                {event.title}
                              </h3>
                              {event.isStarred && (
                                <Star className="h-4 w-4 text-yellow-500 fill-current" />
                              )}
                              <Badge variant={getStatusColor(event.status) as any}>
                                {event.status.replace('_', ' ')}
                              </Badge>
                              <Badge className={cn("text-xs", getTypeColor(event.type))}>
                                {event.type}
                              </Badge>
                              <Badge className={cn("text-xs", getPriorityColor(event.priority))}>
                                {event.priority} priority
                              </Badge>
                            </div>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600 dark:text-gray-400">
                              <div className="flex items-center gap-2">
                                <Calendar className="h-4 w-4" />
                                <span>{formatDate(event.date)}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Clock className="h-4 w-4" />
                                <span>{event.startTime} - {event.endTime}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <User className="h-4 w-4" />
                                <span>{event.clientName}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <DollarSign className="h-4 w-4" />
                                <span>{formatCurrency(event.price)}</span>
                              </div>
                            </div>

                            <div className="mt-3">
                              <div className="flex items-center gap-2 mb-1">
                                <MapPin className="h-4 w-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">
                                  {event.location}
                                </span>
                              </div>
                              {event.notes && (
                                <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                                  {event.notes}
                                </p>
                              )}
                            </div>

                            {/* Payment Status */}
                            {event.balance > 0 && (
                              <div className="mt-3">
                                <div className="flex items-center justify-between text-sm mb-1">
                                  <span className="text-gray-600 dark:text-gray-400">Payment Progress</span>
                                  <span className="text-gray-600 dark:text-gray-400">
                                    {formatCurrency(event.price - event.balance)} / {formatCurrency(event.price)}
                                  </span>
                                </div>
                                <Progress 
                                  value={((event.price - event.balance) / event.price) * 100} 
                                  className="h-2"
                                />
                                <p className="text-xs text-red-600 mt-1">
                                  Balance due: {formatCurrency(event.balance)}
                                </p>
                              </div>
                            )}

                            {/* Deliverables */}
                            {event.photosCount && (
                              <div className="flex items-center gap-4 mt-3 text-sm text-gray-600 dark:text-gray-400">
                                <div className="flex items-center gap-1">
                                  <Image className="h-4 w-4" />
                                  <span>{event.photosCount} photos</span>
                                </div>
                                {event.videosCount && (
                                  <div className="flex items-center gap-1">
                                    <Video className="h-4 w-4" />
                                    <span>{event.videosCount} videos</span>
                                  </div>
                                )}
                                {event.deliveryDate && (
                                  <div className="flex items-center gap-1">
                                    <Timer className="h-4 w-4" />
                                    <span>Delivered {formatDate(event.deliveryDate)}</span>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            View
                          </Button>
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </Button>
                          <Button variant="outline" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle>Event Types Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {['wedding', 'birthday', 'corporate', 'engagement', 'family'].map(type => {
                      const count = events.filter(e => e.type === type).length;
                      const percentage = events.length > 0 ? (count / events.length) * 100 : 0;
                      return (
                        <div key={type} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="text-lg">{getTypeIcon(type)}</span>
                            <span className="text-sm font-medium capitalize">{type}</span>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-bold">{count}</p>
                            <p className="text-xs text-gray-500">{percentage.toFixed(1)}%</p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle>Revenue by Event Type</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {['wedding', 'birthday', 'corporate', 'engagement', 'family'].map(type => {
                      const typeEvents = events.filter(e => e.type === type);
                      const revenue = typeEvents.reduce((sum, e) => sum + (e.price - e.balance), 0);
                      return (
                        <div key={type} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="text-lg">{getTypeIcon(type)}</span>
                            <span className="text-sm font-medium capitalize">{type}</span>
                          </div>
                          <span className="text-sm font-bold">{formatCurrency(revenue)}</span>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </StudioLayout>
  );
}
