"use client";

import { useState } from "react";
import { 
  Smartphone,
  Download,
  QrCode,
  Share2,
  Bell,
  Settings,
  Users,
  Camera,
  Image,
  Upload,
  Wifi,
  WifiOff,
  Battery,
  Signal,
  MapPin,
  Clock,
  Star,
  Heart,
  MessageSquare,
  Send,
  Eye,
  Edit,
  Trash2,
  Plus,
  Search,
  Filter,
  Grid,
  List,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Zap,
  Shield,
  Lock,
  Unlock,
  RefreshCw,
  Activity,
  TrendingUp,
  BarChart3,
  PieChart,
  Calendar,
  User,
  Mail,
  Phone
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import StudioLayout from "@/components/studio/StudioLayout";
import { formatDate, timeAgo, cn } from "@/lib/utils";

interface MobileSession {
  id: string;
  clientName: string;
  eventType: string;
  location: string;
  startTime: string;
  endTime?: string;
  status: 'scheduled' | 'active' | 'completed' | 'cancelled';
  photosCount: number;
  uploadedCount: number;
  deviceInfo: {
    model: string;
    os: string;
    battery: number;
    storage: { used: number; total: number };
    signal: number;
    isOnline: boolean;
  };
  gpsLocation?: {
    lat: number;
    lng: number;
    address: string;
  };
}

interface MobileUser {
  id: string;
  name: string;
  email: string;
  role: 'photographer' | 'assistant' | 'client';
  avatar?: string;
  isOnline: boolean;
  lastSeen: string;
  permissions: string[];
  deviceId: string;
  appVersion: string;
}

interface RemoteCommand {
  id: string;
  type: 'sync_photos' | 'backup_session' | 'update_settings' | 'send_notification' | 'lock_device';
  targetDeviceId: string;
  status: 'pending' | 'sent' | 'acknowledged' | 'completed' | 'failed';
  createdAt: string;
  executedAt?: string;
  params?: any;
}

interface AppAnalytics {
  totalSessions: number;
  activeUsers: number;
  photosUploaded: number;
  storageUsed: number;
  averageSessionDuration: number;
  popularFeatures: {
    feature: string;
    usage: number;
  }[];
  deviceBreakdown: {
    platform: string;
    count: number;
    percentage: number;
  }[];
}

export default function MobileCompanionPage() {
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedSession, setSelectedSession] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  const [mobileSessions] = useState<MobileSession[]>([
    {
      id: '1',
      clientName: 'Sarah Johnson',
      eventType: 'Wedding',
      location: 'Central Park, NYC',
      startTime: '2024-01-20T14:00:00Z',
      endTime: '2024-01-20T20:00:00Z',
      status: 'completed',
      photosCount: 450,
      uploadedCount: 450,
      deviceInfo: {
        model: 'iPhone 15 Pro',
        os: 'iOS 17.2',
        battery: 85,
        storage: { used: 128, total: 256 },
        signal: 4,
        isOnline: true
      },
      gpsLocation: {
        lat: 40.7829,
        lng: -73.9654,
        address: 'Central Park, New York, NY'
      }
    },
    {
      id: '2',
      clientName: 'Mike Davis',
      eventType: 'Birthday Party',
      location: 'Studio Location',
      startTime: '2024-01-21T16:00:00Z',
      status: 'active',
      photosCount: 120,
      uploadedCount: 85,
      deviceInfo: {
        model: 'Samsung Galaxy S24',
        os: 'Android 14',
        battery: 65,
        storage: { used: 64, total: 128 },
        signal: 3,
        isOnline: true
      }
    }
  ]);

  const [mobileUsers] = useState<MobileUser[]>([
    {
      id: '1',
      name: 'John Photographer',
      email: '<EMAIL>',
      role: 'photographer',
      avatar: '/api/placeholder/40/40',
      isOnline: true,
      lastSeen: '2024-01-21T18:30:00Z',
      permissions: ['capture', 'upload', 'edit', 'share'],
      deviceId: 'device_001',
      appVersion: '2.1.0'
    },
    {
      id: '2',
      name: 'Emma Assistant',
      email: '<EMAIL>',
      role: 'assistant',
      isOnline: false,
      lastSeen: '2024-01-21T15:45:00Z',
      permissions: ['capture', 'upload'],
      deviceId: 'device_002',
      appVersion: '2.0.8'
    },
    {
      id: '3',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      role: 'client',
      isOnline: true,
      lastSeen: '2024-01-21T18:25:00Z',
      permissions: ['view', 'favorite', 'download'],
      deviceId: 'device_003',
      appVersion: '2.1.0'
    }
  ]);

  const [remoteCommands] = useState<RemoteCommand[]>([
    {
      id: '1',
      type: 'sync_photos',
      targetDeviceId: 'device_001',
      status: 'completed',
      createdAt: '2024-01-21T17:00:00Z',
      executedAt: '2024-01-21T17:02:00Z'
    },
    {
      id: '2',
      type: 'backup_session',
      targetDeviceId: 'device_002',
      status: 'pending',
      createdAt: '2024-01-21T18:15:00Z'
    }
  ]);

  const [appAnalytics] = useState<AppAnalytics>({
    totalSessions: 156,
    activeUsers: 12,
    photosUploaded: 15420,
    storageUsed: 2.4, // TB
    averageSessionDuration: 4.2, // hours
    popularFeatures: [
      { feature: 'Photo Capture', usage: 95 },
      { feature: 'Auto Upload', usage: 87 },
      { feature: 'GPS Tagging', usage: 72 },
      { feature: 'Client Sharing', usage: 68 },
      { feature: 'Offline Mode', usage: 45 }
    ],
    deviceBreakdown: [
      { platform: 'iOS', count: 8, percentage: 67 },
      { platform: 'Android', count: 4, percentage: 33 }
    ]
  });

  const filteredSessions = mobileSessions.filter(session => {
    const matchesSearch = session.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         session.eventType.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         session.location.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || session.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'secondary';
      case 'active': return 'default';
      case 'completed': return 'default';
      case 'cancelled': return 'destructive';
      case 'pending': return 'secondary';
      case 'sent': return 'default';
      case 'acknowledged': return 'default';
      case 'completed': return 'default';
      case 'failed': return 'destructive';
      default: return 'secondary';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'photographer': return 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300';
      case 'assistant': return 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300';
      case 'client': return 'bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  const sendRemoteCommand = (deviceId: string, commandType: string) => {
    console.log(`Sending ${commandType} command to device ${deviceId}`);
    // In a real app, this would send the command to the mobile device
  };

  return (
    <StudioLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <Smartphone className="h-8 w-8 text-blue-600" />
              Mobile Companion
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Manage mobile sessions, remote devices, and companion app features
            </p>
          </div>

        {/* Mobile Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <Activity className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {appAnalytics.activeUsers}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Active Users</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <Camera className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {appAnalytics.totalSessions}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Total Sessions</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <Image className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {appAnalytics.photosUploaded.toLocaleString()}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Photos Uploaded</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-orange-600 rounded-xl flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {appAnalytics.averageSessionDuration}h
                  </p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">Avg Session</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="sessions">Live Sessions</TabsTrigger>
            <TabsTrigger value="users">Mobile Users</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Quick Actions */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-yellow-600" />
                  Quick Actions
                </CardTitle>
                <CardDescription>
                  Common mobile companion management tasks
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                    <QrCode className="h-6 w-6 text-blue-600" />
                    <span className="text-sm font-medium">Generate QR</span>
                    <span className="text-xs text-gray-500">Client access</span>
                  </Button>
                  <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                    <Send className="h-6 w-6 text-green-600" />
                    <span className="text-sm font-medium">Send Command</span>
                    <span className="text-xs text-gray-500">Remote control</span>
                  </Button>
                  <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                    <Upload className="h-6 w-6 text-purple-600" />
                    <span className="text-sm font-medium">Sync Photos</span>
                    <span className="text-xs text-gray-500">Bulk upload</span>
                  </Button>
                  <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                    <Bell className="h-6 w-6 text-orange-600" />
                    <span className="text-sm font-medium">Send Alert</span>
                    <span className="text-xs text-gray-500">Notifications</span>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Recent Commands */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5 text-gray-600" />
                  Recent Remote Commands
                </CardTitle>
                <CardDescription>
                  Latest commands sent to mobile devices
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {remoteCommands.map((command) => (
                    <div key={command.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="h-8 w-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                          <Send className="h-4 w-4 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {command.type.replace('_', ' ').toUpperCase()}
                          </p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            Device: {command.targetDeviceId} • {timeAgo(command.createdAt)}
                          </p>
                        </div>
                      </div>
                      <Badge variant={getStatusColor(command.status)}>
                        {command.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Live Sessions Tab */}
          <TabsContent value="sessions" className="space-y-6">
            {/* Filters */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                      <Input
                        placeholder="Search sessions by client, event, or location..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 h-11 bg-gray-50 dark:bg-gray-700"
                      />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="all">All Status</option>
                      <option value="scheduled">Scheduled</option>
                      <option value="active">Active</option>
                      <option value="completed">Completed</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Sessions List */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Camera className="h-5 w-5 text-green-600" />
                  Mobile Photography Sessions
                </CardTitle>
                <CardDescription>
                  Monitor active and recent mobile photography sessions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredSessions.map((session) => (
                    <div key={session.id} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-4">
                          <div className="h-12 w-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                            <Camera className="h-6 w-6 text-white" />
                          </div>
                          <div>
                            <div className="flex items-center gap-2">
                              <h3 className="font-semibold text-gray-900 dark:text-white">
                                {session.clientName} - {session.eventType}
                              </h3>
                              <Badge variant={getStatusColor(session.status)}>
                                {session.status}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mt-1">
                              <div className="flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                <span>{session.location}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                <span>{formatDate(session.startTime)}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Image className="h-3 w-3" />
                                <span>{session.photosCount} photos</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            View
                          </Button>
                          <Button variant="outline" size="sm">
                            <Send className="h-4 w-4 mr-2" />
                            Command
                          </Button>
                        </div>
                      </div>

                      {/* Device Info */}
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-3 bg-white dark:bg-gray-800 rounded-lg">
                        <div className="flex items-center gap-2">
                          <Smartphone className="h-4 w-4 text-gray-600" />
                          <div>
                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                              {session.deviceInfo.model}
                            </p>
                            <p className="text-xs text-gray-500">{session.deviceInfo.os}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Battery className="h-4 w-4 text-green-600" />
                          <div>
                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                              {session.deviceInfo.battery}%
                            </p>
                            <p className="text-xs text-gray-500">Battery</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Signal className="h-4 w-4 text-blue-600" />
                          <div>
                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                              {session.deviceInfo.signal}/5
                            </p>
                            <p className="text-xs text-gray-500">Signal</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {session.deviceInfo.isOnline ? (
                            <Wifi className="h-4 w-4 text-green-600" />
                          ) : (
                            <WifiOff className="h-4 w-4 text-red-600" />
                          )}
                          <div>
                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                              {session.deviceInfo.isOnline ? 'Online' : 'Offline'}
                            </p>
                            <p className="text-xs text-gray-500">Status</p>
                          </div>
                        </div>
                      </div>

                      {/* Upload Progress */}
                      {session.status === 'active' && (
                        <div className="mt-4">
                          <div className="flex items-center justify-between text-sm mb-2">
                            <span className="text-gray-600 dark:text-gray-400">Upload Progress</span>
                            <span className="font-medium">{session.uploadedCount}/{session.photosCount} photos</span>
                          </div>
                          <Progress value={(session.uploadedCount / session.photosCount) * 100} className="h-2" />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </StudioLayout>
  );
}
          <div className="flex items-center gap-3">
            <Button variant="outline">
              <QrCode className="h-4 w-4 mr-2" />
              Generate QR Code
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Download App
            </Button>
            <Button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
              <Plus className="h-4 w-4 mr-2" />
              New Session
            </Button>
          </div>
        </div>
