"use client";

import { useState } from "react";
import {
  FileText,
  Download,
  Calendar,
  Filter,
  BarChart3,
  Users,
  Camera,
  TrendingUp,
  Eye,
  Share2,
  FileSpreadsheet,
  FileImage,
  Mail,
  Clock,
  Target,
  DollarSign,
  Activity,
  Zap,
  Star
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import StudioLayout from "@/components/studio/StudioLayout";
import { formatDate, formatCurrency, cn } from "@/lib/utils";

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  category: 'analytics' | 'clients' | 'photos' | 'billing' | 'activity';
  icon: any;
  formats: string[];
  lastGenerated?: string;
  isPopular?: boolean;
}

interface GeneratedReport {
  id: string;
  name: string;
  type: string;
  format: string;
  size: string;
  generatedAt: string;
  downloadUrl: string;
  status: 'generating' | 'ready' | 'expired';
}

export default function ReportsPage() {
  const [dateRange, setDateRange] = useState("30");
  const [selectedCategory, setSelectedCategory] = useState("all");
  
  const [reportTemplates] = useState<ReportTemplate[]>([
    {
      id: '1',
      name: 'Client Activity Report',
      description: 'Detailed report of client engagement, photo views, and downloads',
      category: 'clients',
      icon: Users,
      formats: ['PDF', 'Excel', 'CSV'],
      lastGenerated: '2024-01-19T10:30:00Z',
      isPopular: true
    },
    {
      id: '2',
      name: 'Photo Analytics Summary',
      description: 'Photo upload trends, face recognition stats, and storage usage',
      category: 'photos',
      icon: Camera,
      formats: ['PDF', 'Excel'],
      lastGenerated: '2024-01-18T14:20:00Z',
      isPopular: true
    },
    {
      id: '3',
      name: 'Revenue & Billing Report',
      description: 'Subscription details, payment history, and revenue analysis',
      category: 'billing',
      icon: DollarSign,
      formats: ['PDF', 'Excel'],
      lastGenerated: '2024-01-15T09:15:00Z'
    },
    {
      id: '4',
      name: 'Studio Performance Dashboard',
      description: 'Comprehensive overview of studio metrics and KPIs',
      category: 'analytics',
      icon: BarChart3,
      formats: ['PDF', 'PowerPoint'],
      isPopular: true
    },
    {
      id: '5',
      name: 'Daily Activity Log',
      description: 'Detailed log of all studio activities and user actions',
      category: 'activity',
      icon: Activity,
      formats: ['CSV', 'Excel'],
      lastGenerated: '2024-01-20T08:00:00Z'
    },
    {
      id: '6',
      name: 'Client Engagement Metrics',
      description: 'Client interaction patterns, session duration, and engagement rates',
      category: 'clients',
      icon: Target,
      formats: ['PDF', 'Excel']
    },
    {
      id: '7',
      name: 'Photo Gallery Performance',
      description: 'Gallery views, download rates, and popular photo analysis',
      category: 'photos',
      icon: Eye,
      formats: ['PDF', 'Excel', 'CSV']
    },
    {
      id: '8',
      name: 'Storage Utilization Report',
      description: 'Storage usage breakdown, file types, and optimization recommendations',
      category: 'photos',
      icon: FileImage,
      formats: ['PDF', 'Excel']
    }
  ]);

  const [generatedReports] = useState<GeneratedReport[]>([
    {
      id: '1',
      name: 'Client Activity Report - January 2024',
      type: 'Client Activity Report',
      format: 'PDF',
      size: '2.4 MB',
      generatedAt: '2024-01-19T10:30:00Z',
      downloadUrl: '/reports/client-activity-jan-2024.pdf',
      status: 'ready'
    },
    {
      id: '2',
      name: 'Photo Analytics Summary - Q4 2023',
      type: 'Photo Analytics Summary',
      format: 'Excel',
      size: '1.8 MB',
      generatedAt: '2024-01-18T14:20:00Z',
      downloadUrl: '/reports/photo-analytics-q4-2023.xlsx',
      status: 'ready'
    },
    {
      id: '3',
      name: 'Studio Performance Dashboard - December',
      type: 'Studio Performance Dashboard',
      format: 'PDF',
      size: '3.2 MB',
      generatedAt: '2024-01-15T09:15:00Z',
      downloadUrl: '/reports/performance-dashboard-dec.pdf',
      status: 'ready'
    },
    {
      id: '4',
      name: 'Daily Activity Log - Today',
      type: 'Daily Activity Log',
      format: 'CSV',
      size: '856 KB',
      generatedAt: '2024-01-20T08:00:00Z',
      downloadUrl: '/reports/activity-log-today.csv',
      status: 'generating'
    }
  ]);

  const filteredTemplates = reportTemplates.filter(template => 
    selectedCategory === "all" || template.category === selectedCategory
  );

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'analytics': return 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300';
      case 'clients': return 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300';
      case 'photos': return 'bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300';
      case 'billing': return 'bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300';
      case 'activity': return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  const getFormatIcon = (format: string) => {
    switch (format.toLowerCase()) {
      case 'pdf': return FileText;
      case 'excel':
      case 'csv': return FileSpreadsheet;
      case 'powerpoint': return FileImage;
      default: return FileText;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ready': return 'success';
      case 'generating': return 'warning';
      case 'expired': return 'secondary';
      default: return 'secondary';
    }
  };

  return (
    <StudioLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <FileText className="h-8 w-8 text-blue-600" />
              Reports & Analytics
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Generate comprehensive reports and export your studio data
            </p>
          </div>
          <div className="flex items-center gap-3">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="90">Last 90 days</option>
              <option value="365">Last year</option>
              <option value="custom">Custom range</option>
            </select>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <FileText className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {reportTemplates.length}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Report Types</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <Download className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {generatedReports.filter(r => r.status === 'ready').length}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Ready to Download</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <Clock className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {generatedReports.filter(r => r.status === 'generating').length}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Generating</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-orange-600 rounded-xl flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {reportTemplates.filter(r => r.isPopular).length}
                  </p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">Popular Reports</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Report Templates */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-blue-600" />
                  Report Templates
                </CardTitle>
                <CardDescription>
                  Choose from pre-built report templates or create custom reports
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">All Categories</option>
                  <option value="analytics">Analytics</option>
                  <option value="clients">Clients</option>
                  <option value="photos">Photos</option>
                  <option value="billing">Billing</option>
                  <option value="activity">Activity</option>
                </select>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTemplates.map((template) => {
                const IconComponent = template.icon;
                return (
                  <Card key={template.id} className="border border-gray-200 dark:border-gray-600 hover:shadow-lg transition-all duration-200 cursor-pointer">
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        {/* Template Header */}
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-3">
                            <div className={cn(
                              "h-10 w-10 rounded-lg flex items-center justify-center",
                              getCategoryColor(template.category)
                            )}>
                              <IconComponent className="h-5 w-5" />
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900 dark:text-white">
                                {template.name}
                              </h3>
                              {template.isPopular && (
                                <Badge variant="success" className="text-xs mt-1">
                                  <Star className="h-3 w-3 mr-1" />
                                  Popular
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Description */}
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {template.description}
                        </p>

                        {/* Available Formats */}
                        <div>
                          <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Available formats:
                          </p>
                          <div className="flex flex-wrap gap-2">
                            {template.formats.map((format, index) => {
                              const FormatIcon = getFormatIcon(format);
                              return (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  <FormatIcon className="h-3 w-3 mr-1" />
                                  {format}
                                </Badge>
                              );
                            })}
                          </div>
                        </div>

                        {/* Last Generated */}
                        {template.lastGenerated && (
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Last generated: {formatDate(template.lastGenerated)}
                          </div>
                        )}

                        {/* Generate Button */}
                        <Button className="w-full">
                          <Download className="h-4 w-4 mr-2" />
                          Generate Report
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Generated Reports */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="h-5 w-5 text-green-600" />
              Generated Reports
            </CardTitle>
            <CardDescription>
              Download your previously generated reports
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {generatedReports.map((report) => {
                const FormatIcon = getFormatIcon(report.format);
                return (
                  <div key={report.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="h-10 w-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                        <FormatIcon className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-semibold text-gray-900 dark:text-white">
                          {report.name}
                        </p>
                        <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                          <span>{report.format}</span>
                          <span>•</span>
                          <span>{report.size}</span>
                          <span>•</span>
                          <span>Generated {formatDate(report.generatedAt)}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Badge variant={getStatusColor(report.status) as any}>
                        {report.status}
                      </Badge>
                      {report.status === 'ready' ? (
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            Preview
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4 mr-2" />
                            Download
                          </Button>
                          <Button variant="outline" size="sm">
                            <Share2 className="h-4 w-4 mr-2" />
                            Share
                          </Button>
                        </div>
                      ) : report.status === 'generating' ? (
                        <div className="flex items-center gap-2">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                          <span className="text-sm text-gray-600 dark:text-gray-400">Generating...</span>
                        </div>
                      ) : (
                        <Button variant="outline" size="sm" disabled>
                          Expired
                        </Button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Quick Export Options */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-orange-600" />
              Quick Export
            </CardTitle>
            <CardDescription>
              Instantly export specific data sets
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                <Users className="h-6 w-6 text-blue-600" />
                <span className="text-sm">Export Clients</span>
              </Button>
              <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                <Camera className="h-6 w-6 text-green-600" />
                <span className="text-sm">Export Photos</span>
              </Button>
              <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                <Activity className="h-6 w-6 text-purple-600" />
                <span className="text-sm">Export Activity</span>
              </Button>
              <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                <DollarSign className="h-6 w-6 text-orange-600" />
                <span className="text-sm">Export Billing</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </StudioLayout>
  );
}
