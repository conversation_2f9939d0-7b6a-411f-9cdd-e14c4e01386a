"use client";

import { useState } from "react";
import { 
  MessageSquare,
  Mail,
  Phone,
  Video,
  Send,
  Paperclip,
  Smile,
  Search,
  Filter,
  MoreHorizontal,
  Star,
  Archive,
  Trash2,
  Eye,
  Calendar,
  Clock,
  CheckCircle,
  AlertCircle,
  Users,
  Plus,
  Settings,
  Bell,
  Image,
  FileText,
  Download
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import StudioLayout from "@/components/studio/StudioLayout";
import { formatDate, timeAgo, cn } from "@/lib/utils";

interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  senderName: string;
  senderType: 'client' | 'studio';
  content: string;
  type: 'text' | 'image' | 'file' | 'gallery_link';
  timestamp: string;
  isRead: boolean;
  attachments?: {
    id: string;
    name: string;
    type: string;
    size: number;
    url: string;
  }[];
}

interface Conversation {
  id: string;
  clientId: string;
  clientName: string;
  clientEmail: string;
  clientPhone?: string;
  eventType: string;
  eventDate: string;
  lastMessage: string;
  lastMessageTime: string;
  unreadCount: number;
  isStarred: boolean;
  isArchived: boolean;
  status: 'active' | 'completed' | 'pending';
  priority: 'low' | 'medium' | 'high';
  tags: string[];
}

export default function CommunicationPage() {
  const [activeTab, setActiveTab] = useState("conversations");
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [newMessage, setNewMessage] = useState("");

  const [conversations] = useState<Conversation[]>([
    {
      id: '1',
      clientId: 'client1',
      clientName: 'Sarah Johnson',
      clientEmail: '<EMAIL>',
      clientPhone: '+****************',
      eventType: 'Wedding',
      eventDate: '2024-02-15',
      lastMessage: 'Thank you so much! The photos are absolutely beautiful. Can I get the high-res versions?',
      lastMessageTime: '2024-01-20T14:30:00Z',
      unreadCount: 2,
      isStarred: true,
      isArchived: false,
      status: 'active',
      priority: 'high',
      tags: ['wedding', 'vip']
    },
    {
      id: '2',
      clientId: 'client2',
      clientName: 'Mike Davis',
      clientEmail: '<EMAIL>',
      clientPhone: '+****************',
      eventType: 'Birthday Party',
      eventDate: '2024-01-25',
      lastMessage: 'When will the edited photos be ready?',
      lastMessageTime: '2024-01-19T16:45:00Z',
      unreadCount: 1,
      isStarred: false,
      isArchived: false,
      status: 'pending',
      priority: 'medium',
      tags: ['birthday', 'family']
    },
    {
      id: '3',
      clientId: 'client3',
      clientName: 'Emma Wilson',
      clientEmail: '<EMAIL>',
      eventType: 'Engagement',
      eventDate: '2024-01-10',
      lastMessage: 'Perfect! Thank you for the quick turnaround.',
      lastMessageTime: '2024-01-18T11:20:00Z',
      unreadCount: 0,
      isStarred: false,
      isArchived: false,
      status: 'completed',
      priority: 'low',
      tags: ['engagement', 'outdoor']
    }
  ]);

  const [messages] = useState<Message[]>([
    {
      id: '1',
      conversationId: '1',
      senderId: 'client1',
      senderName: 'Sarah Johnson',
      senderType: 'client',
      content: 'Hi! I just saw the preview photos and they look amazing! When will all the photos be ready?',
      type: 'text',
      timestamp: '2024-01-20T10:00:00Z',
      isRead: true
    },
    {
      id: '2',
      conversationId: '1',
      senderId: 'studio',
      senderName: 'PhotoStudio Pro',
      senderType: 'studio',
      content: 'Thank you Sarah! I\'m so glad you love them. All edited photos will be ready by tomorrow evening. I\'ll send you the gallery link once they\'re uploaded.',
      type: 'text',
      timestamp: '2024-01-20T10:15:00Z',
      isRead: true
    },
    {
      id: '3',
      conversationId: '1',
      senderId: 'studio',
      senderName: 'PhotoStudio Pro',
      senderType: 'studio',
      content: 'Here\'s your complete wedding gallery! All 150 photos are now available for viewing and download.',
      type: 'gallery_link',
      timestamp: '2024-01-20T14:00:00Z',
      isRead: true
    },
    {
      id: '4',
      conversationId: '1',
      senderId: 'client1',
      senderName: 'Sarah Johnson',
      senderType: 'client',
      content: 'Thank you so much! The photos are absolutely beautiful. Can I get the high-res versions?',
      type: 'text',
      timestamp: '2024-01-20T14:30:00Z',
      isRead: false
    }
  ]);

  const filteredConversations = conversations.filter(conv => {
    const matchesSearch = conv.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         conv.eventType.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || conv.status === statusFilter;
    return matchesSearch && matchesStatus && !conv.isArchived;
  });

  const selectedConversationData = conversations.find(c => c.id === selectedConversation);
  const conversationMessages = messages.filter(m => m.conversationId === selectedConversation);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'pending': return 'warning';
      case 'completed': return 'secondary';
      default: return 'secondary';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
      case 'low': return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-700';
    }
  };

  const handleSendMessage = () => {
    if (newMessage.trim() && selectedConversation) {
      // In a real app, this would send the message to the backend
      console.log('Sending message:', newMessage);
      setNewMessage("");
    }
  };

  const totalUnread = conversations.reduce((sum, conv) => sum + conv.unreadCount, 0);

  return (
    <StudioLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <MessageSquare className="h-8 w-8 text-blue-600" />
              Client Communication
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Manage conversations, send messages, and stay connected with your clients
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
              <Plus className="h-4 w-4 mr-2" />
              New Message
            </Button>
          </div>
        </div>

        {/* Communication Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <MessageSquare className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {conversations.filter(c => !c.isArchived).length}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Active Conversations</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-red-600 rounded-xl flex items-center justify-center">
                  <Bell className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-red-900 dark:text-red-100">
                    {totalUnread}
                  </p>
                  <p className="text-sm text-red-600 dark:text-red-400">Unread Messages</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {conversations.filter(c => c.status === 'completed').length}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Completed</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <Star className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {conversations.filter(c => c.isStarred).length}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Starred</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Communication Interface */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[600px]">
          {/* Conversations List */}
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800 flex flex-col">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-blue-600" />
                  Conversations
                </CardTitle>
                <div className="flex items-center gap-2">
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="text-xs px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="all">All</option>
                    <option value="active">Active</option>
                    <option value="pending">Pending</option>
                    <option value="completed">Completed</option>
                  </select>
                </div>
              </div>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search conversations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 h-9 bg-gray-50 dark:bg-gray-700"
                />
              </div>
            </CardHeader>
            <CardContent className="flex-1 overflow-y-auto p-0">
              <div className="space-y-1">
                {filteredConversations.map(conversation => (
                  <div
                    key={conversation.id}
                    onClick={() => setSelectedConversation(conversation.id)}
                    className={cn(
                      "p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border-l-4",
                      selectedConversation === conversation.id 
                        ? "bg-blue-50 dark:bg-blue-900/20 border-l-blue-600" 
                        : "border-l-transparent"
                    )}
                  >
                    <div className="flex items-start gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={`/api/placeholder/40/40`} />
                        <AvatarFallback className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
                          {conversation.clientName.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <p className="font-semibold text-gray-900 dark:text-white text-sm">
                              {conversation.clientName}
                            </p>
                            {conversation.isStarred && (
                              <Star className="h-3 w-3 text-yellow-500 fill-current" />
                            )}
                          </div>
                          <div className="flex items-center gap-1">
                            {conversation.unreadCount > 0 && (
                              <Badge variant="destructive" className="text-xs h-5 w-5 rounded-full p-0 flex items-center justify-center">
                                {conversation.unreadCount}
                              </Badge>
                            )}
                            <span className="text-xs text-gray-500">
                              {timeAgo(conversation.lastMessageTime)}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant={getStatusColor(conversation.status) as any} className="text-xs">
                            {conversation.status}
                          </Badge>
                          <Badge className={cn("text-xs", getPriorityColor(conversation.priority))}>
                            {conversation.priority}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 truncate">
                          {conversation.lastMessage}
                        </p>
                        <div className="flex items-center gap-2 mt-2">
                          <span className="text-xs text-gray-500">{conversation.eventType}</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-500">{formatDate(conversation.eventDate)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Message Thread */}
          <Card className="lg:col-span-2 border-0 shadow-md bg-white dark:bg-gray-800 flex flex-col">
            {selectedConversationData ? (
              <>
                {/* Conversation Header */}
                <CardHeader className="pb-4 border-b border-gray-200 dark:border-gray-600">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={`/api/placeholder/40/40`} />
                        <AvatarFallback className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
                          {selectedConversationData.clientName.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          {selectedConversationData.clientName}
                        </h3>
                        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                          <span>{selectedConversationData.eventType}</span>
                          <span>•</span>
                          <span>{formatDate(selectedConversationData.eventDate)}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Phone className="h-4 w-4 mr-2" />
                        Call
                      </Button>
                      <Button variant="outline" size="sm">
                        <Video className="h-4 w-4 mr-2" />
                        Video
                      </Button>
                      <Button variant="outline" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>

                {/* Messages */}
                <CardContent className="flex-1 overflow-y-auto p-4">
                  <div className="space-y-4">
                    {conversationMessages.map(message => (
                      <div
                        key={message.id}
                        className={cn(
                          "flex gap-3",
                          message.senderType === 'studio' ? "justify-end" : "justify-start"
                        )}
                      >
                        {message.senderType === 'client' && (
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={`/api/placeholder/32/32`} />
                            <AvatarFallback className="bg-gray-300 text-gray-700 text-xs">
                              {message.senderName.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                        )}
                        <div
                          className={cn(
                            "max-w-[70%] rounded-lg p-3",
                            message.senderType === 'studio'
                              ? "bg-blue-600 text-white"
                              : "bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white"
                          )}
                        >
                          {message.type === 'gallery_link' ? (
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <Image className="h-4 w-4" />
                                <span className="font-medium">Photo Gallery Shared</span>
                              </div>
                              <p className="text-sm opacity-90">{message.content}</p>
                              <Button
                                variant={message.senderType === 'studio' ? 'secondary' : 'default'}
                                size="sm"
                                className="w-full"
                              >
                                <Eye className="h-4 w-4 mr-2" />
                                View Gallery
                              </Button>
                            </div>
                          ) : (
                            <p className="text-sm">{message.content}</p>
                          )}
                          <div className="flex items-center justify-between mt-2">
                            <span className={cn(
                              "text-xs",
                              message.senderType === 'studio' ? "text-blue-100" : "text-gray-500 dark:text-gray-400"
                            )}>
                              {formatDate(message.timestamp)}
                            </span>
                            {message.senderType === 'studio' && (
                              <CheckCircle className="h-3 w-3 text-blue-200" />
                            )}
                          </div>
                        </div>
                        {message.senderType === 'studio' && (
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={`/api/placeholder/32/32`} />
                            <AvatarFallback className="bg-blue-600 text-white text-xs">
                              PS
                            </AvatarFallback>
                          </Avatar>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>

                {/* Message Input */}
                <div className="p-4 border-t border-gray-200 dark:border-gray-600">
                  <div className="flex items-end gap-3">
                    <div className="flex-1">
                      <Textarea
                        placeholder="Type your message..."
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        className="min-h-[60px] resize-none"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            handleSendMessage();
                          }
                        }}
                      />
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Paperclip className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Image className="h-4 w-4" />
                      </Button>
                      <Button 
                        onClick={handleSendMessage}
                        disabled={!newMessage.trim()}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center">
                  <MessageSquare className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Select a conversation
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400">
                    Choose a conversation from the list to start messaging
                  </p>
                </div>
              </div>
            )}
          </Card>
        </div>
      </div>
    </StudioLayout>
  );
}
