"use client";

import { useState } from "react";
import { 
  FileText,
  Plus,
  Edit,
  Eye,
  Download,
  Send,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Calendar,
  DollarSign,
  User,
  Search,
  Filter,
  MoreHorizontal,
  Copy,
  Trash2,
  Settings,
  Signature,
  Mail,
  Phone,
  MapPin,
  Package,
  Camera,
  Users,
  Timer,
  Star,
  Archive,
  RefreshCw
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import StudioLayout from "@/components/studio/StudioLayout";
import { formatDate, formatCurrency, timeAgo, cn } from "@/lib/utils";

interface Contract {
  id: string;
  contractNumber: string;
  title: string;
  clientId: string;
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  eventType: string;
  eventDate: string;
  eventLocation: string;
  packageType: string;
  totalAmount: number;
  depositAmount: number;
  balanceAmount: number;
  status: 'draft' | 'sent' | 'viewed' | 'signed' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high';
  createdAt: string;
  updatedAt: string;
  sentAt?: string;
  signedAt?: string;
  expiryDate: string;
  terms: string[];
  deliverables: string[];
  timeline: {
    milestone: string;
    date: string;
    completed: boolean;
  }[];
  isStarred: boolean;
  notes: string;
}

interface ContractTemplate {
  id: string;
  name: string;
  category: 'wedding' | 'portrait' | 'event' | 'commercial' | 'general';
  description: string;
  content: string;
  variables: string[];
  isDefault: boolean;
  usage: number;
  createdAt: string;
  updatedAt: string;
}

interface Invoice {
  id: string;
  invoiceNumber: string;
  contractId?: string;
  clientId: string;
  clientName: string;
  clientEmail: string;
  issueDate: string;
  dueDate: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  items: InvoiceItem[];
  subtotal: number;
  taxRate: number;
  taxAmount: number;
  totalAmount: number;
  paidAmount: number;
  balanceAmount: number;
  paymentTerms: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  sentAt?: string;
  paidAt?: string;
}

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

interface PaymentRecord {
  id: string;
  invoiceId: string;
  amount: number;
  method: 'cash' | 'check' | 'card' | 'bank_transfer' | 'paypal' | 'stripe';
  reference?: string;
  date: string;
  notes?: string;
  createdAt: string;
}

interface ContractAnalytics {
  totalContracts: number;
  signedContracts: number;
  pendingContracts: number;
  totalRevenue: number;
  averageContractValue: number;
  conversionRate: number;
  monthlyTrend: {
    month: string;
    contracts: number;
    revenue: number;
  }[];
}

interface BookingRequest {
  id: string;
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  eventType: string;
  eventDate: string;
  eventLocation: string;
  budget: number;
  message: string;
  status: 'pending' | 'reviewing' | 'approved' | 'declined';
  priority: 'low' | 'medium' | 'high';
  createdAt: string;
  respondedAt?: string;
}

export default function ContractsPage() {
  const [activeTab, setActiveTab] = useState("contracts");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [priorityFilter, setPriorityFilter] = useState("all");

  const [contracts] = useState<Contract[]>([
    {
      id: '1',
      contractNumber: 'CNT-2024-001',
      title: 'Sarah & John Wedding Photography',
      clientId: 'client1',
      clientName: 'Sarah Johnson',
      clientEmail: '<EMAIL>',
      clientPhone: '+****************',
      eventType: 'Wedding',
      eventDate: '2024-02-15',
      eventLocation: 'Grand Ballroom, Downtown Hotel',
      packageType: 'Premium Wedding Package',
      totalAmount: 2500,
      depositAmount: 1000,
      balanceAmount: 1500,
      status: 'signed',
      priority: 'high',
      createdAt: '2024-01-10T10:00:00Z',
      updatedAt: '2024-01-15T14:30:00Z',
      sentAt: '2024-01-10T15:00:00Z',
      signedAt: '2024-01-12T09:30:00Z',
      expiryDate: '2024-01-25T00:00:00Z',
      terms: [
        '50% deposit required to secure booking',
        'Final payment due 7 days before event',
        'Cancellation policy: 30 days notice required',
        'Weather contingency plan included'
      ],
      deliverables: [
        '8 hours of wedding photography',
        '150+ edited high-resolution photos',
        'Online gallery for 6 months',
        'USB drive with all photos',
        'Print release included'
      ],
      timeline: [
        { milestone: 'Contract signed', date: '2024-01-12', completed: true },
        { milestone: 'Engagement session', date: '2024-01-30', completed: false },
        { milestone: 'Final payment due', date: '2024-02-08', completed: false },
        { milestone: 'Wedding day', date: '2024-02-15', completed: false },
        { milestone: 'Photos delivered', date: '2024-03-01', completed: false }
      ],
      isStarred: true,
      notes: 'VIP client - extra attention to detail required'
    },
    {
      id: '2',
      contractNumber: 'CNT-2024-002',
      title: 'Mike Davis Birthday Party',
      clientId: 'client2',
      clientName: 'Mike Davis',
      clientEmail: '<EMAIL>',
      clientPhone: '+****************',
      eventType: 'Birthday',
      eventDate: '2024-01-25',
      eventLocation: 'Private Residence',
      packageType: 'Party Photography',
      totalAmount: 800,
      depositAmount: 300,
      balanceAmount: 500,
      status: 'completed',
      priority: 'medium',
      createdAt: '2024-01-05T15:20:00Z',
      updatedAt: '2024-01-26T09:15:00Z',
      sentAt: '2024-01-05T16:00:00Z',
      signedAt: '2024-01-06T11:45:00Z',
      expiryDate: '2024-01-20T00:00:00Z',
      terms: [
        '50% deposit required',
        'Balance due on event day',
        '2-hour minimum booking'
      ],
      deliverables: [
        '3 hours of party photography',
        '100+ edited photos',
        'Online gallery for 3 months'
      ],
      timeline: [
        { milestone: 'Contract signed', date: '2024-01-06', completed: true },
        { milestone: 'Final payment', date: '2024-01-25', completed: true },
        { milestone: 'Event day', date: '2024-01-25', completed: true },
        { milestone: 'Photos delivered', date: '2024-02-05', completed: true }
      ],
      isStarred: false,
      notes: 'Surprise party - coordinate with organizer'
    },
    {
      id: '3',
      contractNumber: 'CNT-2024-003',
      title: 'Emma Wilson Engagement Session',
      clientId: 'client3',
      clientName: 'Emma Wilson',
      clientEmail: '<EMAIL>',
      clientPhone: '+****************',
      eventType: 'Engagement',
      eventDate: '2024-01-20',
      eventLocation: 'Central Park',
      packageType: 'Engagement Photography',
      totalAmount: 500,
      depositAmount: 200,
      balanceAmount: 300,
      status: 'sent',
      priority: 'low',
      createdAt: '2024-01-08T11:45:00Z',
      updatedAt: '2024-01-08T11:45:00Z',
      sentAt: '2024-01-08T12:00:00Z',
      expiryDate: '2024-01-22T00:00:00Z',
      terms: [
        'Weather-dependent outdoor session',
        'Rescheduling allowed with 24h notice',
        'Payment due before session'
      ],
      deliverables: [
        '1-hour engagement session',
        '50+ edited photos',
        'Online gallery for 2 months'
      ],
      timeline: [
        { milestone: 'Contract sent', date: '2024-01-08', completed: true },
        { milestone: 'Contract signing', date: '2024-01-15', completed: false },
        { milestone: 'Session date', date: '2024-01-20', completed: false },
        { milestone: 'Photos delivered', date: '2024-01-30', completed: false }
      ],
      isStarred: false,
      notes: 'Follow up if not signed by Jan 15'
    }
  ]);

  const [templates] = useState<ContractTemplate[]>([
    {
      id: '1',
      name: 'Wedding Photography Contract',
      category: 'wedding',
      description: 'Comprehensive wedding photography contract with all standard terms',
      content: 'Wedding photography contract template content...',
      variables: ['client_name', 'event_date', 'venue', 'package_price', 'deposit_amount'],
      isDefault: true,
      usage: 45,
      createdAt: '2023-06-15T10:00:00Z',
      updatedAt: '2024-01-10T14:30:00Z'
    },
    {
      id: '2',
      name: 'Portrait Session Contract',
      category: 'portrait',
      description: 'Standard portrait photography session agreement',
      content: 'Portrait session contract template content...',
      variables: ['client_name', 'session_date', 'location', 'session_fee'],
      isDefault: true,
      usage: 32,
      createdAt: '2023-07-20T14:00:00Z',
      updatedAt: '2023-12-05T09:20:00Z'
    },
    {
      id: '3',
      name: 'Event Photography Contract',
      category: 'event',
      description: 'General event photography contract for parties and celebrations',
      content: 'Event photography contract template content...',
      variables: ['client_name', 'event_type', 'event_date', 'duration', 'total_cost'],
      isDefault: false,
      usage: 28,
      createdAt: '2023-08-10T16:30:00Z',
      updatedAt: '2023-11-15T11:45:00Z'
    }
  ]);

  const [bookingRequests] = useState<BookingRequest[]>([
    {
      id: '1',
      clientName: 'Jennifer Smith',
      clientEmail: '<EMAIL>',
      clientPhone: '+****************',
      eventType: 'Corporate Event',
      eventDate: '2024-02-20',
      eventLocation: 'TechCorp Headquarters',
      budget: 1200,
      message: 'Looking for professional photography for our annual company meeting. Need headshots and event coverage.',
      status: 'pending',
      priority: 'medium',
      createdAt: '2024-01-18T13:30:00Z'
    },
    {
      id: '2',
      clientName: 'Robert Brown',
      clientEmail: '<EMAIL>',
      clientPhone: '+****************',
      eventType: 'Family Portrait',
      eventDate: '2024-02-10',
      eventLocation: 'Studio or Outdoor Location',
      budget: 400,
      message: 'Family of 5 looking for professional portraits. Prefer outdoor setting if weather permits.',
      status: 'reviewing',
      priority: 'low',
      createdAt: '2024-01-16T09:20:00Z'
    }
  ]);

  const [invoices] = useState<Invoice[]>([
    {
      id: '1',
      invoiceNumber: 'INV-2024-001',
      contractId: '1',
      clientId: 'client1',
      clientName: 'Sarah Johnson',
      clientEmail: '<EMAIL>',
      issueDate: '2024-01-15',
      dueDate: '2024-02-08',
      status: 'sent',
      items: [
        {
          id: '1',
          description: 'Wedding Photography Package - Deposit',
          quantity: 1,
          unitPrice: 1000,
          total: 1000
        }
      ],
      subtotal: 1000,
      taxRate: 8.5,
      taxAmount: 85,
      totalAmount: 1085,
      paidAmount: 0,
      balanceAmount: 1085,
      paymentTerms: 'Net 30',
      notes: 'Deposit payment for wedding photography services',
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-15T10:00:00Z',
      sentAt: '2024-01-15T11:00:00Z'
    },
    {
      id: '2',
      invoiceNumber: 'INV-2024-002',
      contractId: '1',
      clientId: 'client1',
      clientName: 'Sarah Johnson',
      clientEmail: '<EMAIL>',
      issueDate: '2024-02-01',
      dueDate: '2024-02-08',
      status: 'draft',
      items: [
        {
          id: '1',
          description: 'Wedding Photography Package - Final Payment',
          quantity: 1,
          unitPrice: 1500,
          total: 1500
        }
      ],
      subtotal: 1500,
      taxRate: 8.5,
      taxAmount: 127.5,
      totalAmount: 1627.5,
      paidAmount: 0,
      balanceAmount: 1627.5,
      paymentTerms: 'Due before event date',
      notes: 'Final payment for wedding photography services',
      createdAt: '2024-02-01T10:00:00Z',
      updatedAt: '2024-02-01T10:00:00Z'
    }
  ]);

  const [paymentRecords] = useState<PaymentRecord[]>([
    {
      id: '1',
      invoiceId: '1',
      amount: 1085,
      method: 'card',
      reference: 'ch_1234567890',
      date: '2024-01-16',
      notes: 'Stripe payment - Visa ending in 4242',
      createdAt: '2024-01-16T14:30:00Z'
    }
  ]);

  const [contractAnalytics] = useState<ContractAnalytics>({
    totalContracts: contracts.length,
    signedContracts: contracts.filter(c => c.status === 'signed').length,
    pendingContracts: contracts.filter(c => ['draft', 'sent', 'viewed'].includes(c.status)).length,
    totalRevenue: contracts.reduce((sum, c) => sum + c.totalAmount, 0),
    averageContractValue: contracts.reduce((sum, c) => sum + c.totalAmount, 0) / contracts.length,
    conversionRate: (contracts.filter(c => c.status === 'signed').length / contracts.length) * 100,
    monthlyTrend: [
      { month: 'Jan', contracts: 8, revenue: 12500 },
      { month: 'Feb', contracts: 12, revenue: 18750 },
      { month: 'Mar', contracts: 15, revenue: 22300 },
      { month: 'Apr', contracts: 10, revenue: 16800 },
      { month: 'May', contracts: 18, revenue: 28900 },
      { month: 'Jun', contracts: 22, revenue: 35600 }
    ]
  });

  const filteredContracts = contracts.filter(contract => {
    const matchesSearch = contract.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         contract.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         contract.contractNumber.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || contract.status === statusFilter;
    const matchesPriority = priorityFilter === "all" || contract.priority === priorityFilter;
    return matchesSearch && matchesStatus && matchesPriority;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'secondary';
      case 'sent': return 'default';
      case 'viewed': return 'warning';
      case 'signed': return 'success';
      case 'completed': return 'success';
      case 'cancelled': return 'destructive';
      case 'pending': return 'warning';
      case 'reviewing': return 'default';
      case 'approved': return 'success';
      case 'declined': return 'destructive';
      default: return 'secondary';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
      case 'low': return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-700';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'wedding': return 'bg-pink-100 dark:bg-pink-900/20 text-pink-700 dark:text-pink-300';
      case 'portrait': return 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300';
      case 'event': return 'bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300';
      case 'commercial': return 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  const contractStats = {
    total: contracts.length,
    signed: contracts.filter(c => c.status === 'signed').length,
    pending: contracts.filter(c => c.status === 'sent' || c.status === 'viewed').length,
    totalValue: contracts.reduce((sum, c) => sum + c.totalAmount, 0),
    pendingPayments: contracts.reduce((sum, c) => sum + c.balanceAmount, 0)
  };

  return (
    <StudioLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <FileText className="h-8 w-8 text-blue-600" />
              Contract & Booking Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Manage contracts, track bookings, and streamline your client agreements
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Contract Settings
            </Button>
            <Button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
              <Plus className="h-4 w-4 mr-2" />
              New Contract
            </Button>
          </div>
        </div>

        {/* Contract Stats */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <FileText className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {contractStats.total}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Total Contracts</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {contractStats.signed}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Signed</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-orange-600 rounded-xl flex items-center justify-center">
                  <Clock className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {contractStats.pending}
                  </p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">Pending</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {formatCurrency(contractStats.totalValue)}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Total Value</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-red-600 rounded-xl flex items-center justify-center">
                  <AlertTriangle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-red-900 dark:text-red-100">
                    {formatCurrency(contractStats.pendingPayments)}
                  </p>
                  <p className="text-sm text-red-600 dark:text-red-400">Pending Payments</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="contracts">Contracts</TabsTrigger>
            <TabsTrigger value="invoices">Invoices</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="bookings">Booking Requests</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          {/* Contracts Tab */}
          <TabsContent value="contracts" className="space-y-6">
            {/* Filters */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                      <Input
                        placeholder="Search contracts by title, client, or contract number..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 h-11 bg-gray-50 dark:bg-gray-700"
                      />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="all">All Status</option>
                      <option value="draft">Draft</option>
                      <option value="sent">Sent</option>
                      <option value="viewed">Viewed</option>
                      <option value="signed">Signed</option>
                      <option value="completed">Completed</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                    <select
                      value={priorityFilter}
                      onChange={(e) => setPriorityFilter(e.target.value)}
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="all">All Priority</option>
                      <option value="high">High</option>
                      <option value="medium">Medium</option>
                      <option value="low">Low</option>
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Contracts List */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="space-y-4">
                  {filteredContracts.map(contract => {
                    const completedMilestones = contract.timeline.filter(m => m.completed).length;
                    const progressPercentage = (completedMilestones / contract.timeline.length) * 100;
                    
                    return (
                      <div key={contract.id} className="p-6 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-4">
                            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-semibold">
                              {contract.contractNumber.split('-')[2]}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                  {contract.title}
                                </h3>
                                {contract.isStarred && (
                                  <Star className="h-4 w-4 text-yellow-500 fill-current" />
                                )}
                                <Badge variant={getStatusColor(contract.status) as any}>
                                  {contract.status}
                                </Badge>
                                <Badge className={cn("text-xs", getPriorityColor(contract.priority))}>
                                  {contract.priority} priority
                                </Badge>
                              </div>
                              
                              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600 dark:text-gray-400 mb-4">
                                <div className="flex items-center gap-2">
                                  <User className="h-4 w-4" />
                                  <span>{contract.clientName}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Calendar className="h-4 w-4" />
                                  <span>{formatDate(contract.eventDate)}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Package className="h-4 w-4" />
                                  <span>{contract.packageType}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                  <DollarSign className="h-4 w-4" />
                                  <span>{formatCurrency(contract.totalAmount)}</span>
                                </div>
                              </div>

                              <div className="flex items-center gap-2 mb-3">
                                <MapPin className="h-4 w-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">
                                  {contract.eventLocation}
                                </span>
                              </div>

                              {/* Progress */}
                              <div className="mb-3">
                                <div className="flex items-center justify-between text-sm mb-1">
                                  <span className="text-gray-600 dark:text-gray-400">Progress</span>
                                  <span className="font-medium">
                                    {completedMilestones}/{contract.timeline.length} milestones
                                  </span>
                                </div>
                                <Progress value={progressPercentage} className="h-2" />
                              </div>

                              {/* Payment Status */}
                              {contract.balanceAmount > 0 && (
                                <div className="mb-3">
                                  <div className="flex items-center justify-between text-sm mb-1">
                                    <span className="text-gray-600 dark:text-gray-400">Payment</span>
                                    <span className="text-red-600 font-medium">
                                      {formatCurrency(contract.balanceAmount)} remaining
                                    </span>
                                  </div>
                                  <Progress 
                                    value={((contract.totalAmount - contract.balanceAmount) / contract.totalAmount) * 100} 
                                    className="h-2"
                                  />
                                </div>
                              )}

                              {contract.notes && (
                                <p className="text-sm text-gray-600 dark:text-gray-400 italic">
                                  {contract.notes}
                                </p>
                              )}
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4 mr-2" />
                              View
                            </Button>
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </Button>
                            {contract.status === 'draft' && (
                              <Button variant="outline" size="sm">
                                <Send className="h-4 w-4 mr-2" />
                                Send
                              </Button>
                            )}
                            <Button variant="outline" size="sm">
                              <Download className="h-4 w-4 mr-2" />
                              Download
                            </Button>
                            <Button variant="outline" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Invoices Tab */}
          <TabsContent value="invoices" className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <DollarSign className="h-5 w-5 text-green-600" />
                      Invoice Management
                    </CardTitle>
                    <CardDescription>
                      Create, send, and track invoices for your photography services
                    </CardDescription>
                  </div>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Invoice
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {invoices.map((invoice) => (
                    <div key={invoice.id} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="h-10 w-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                            <DollarSign className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <div className="flex items-center gap-2">
                              <h3 className="font-semibold text-gray-900 dark:text-white">
                                {invoice.invoiceNumber}
                              </h3>
                              <Badge variant={getStatusColor(invoice.status) as any}>
                                {invoice.status}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mt-1">
                              <div className="flex items-center gap-1">
                                <User className="h-3 w-3" />
                                <span>{invoice.clientName}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                <span>Due: {formatDate(invoice.dueDate)}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <DollarSign className="h-3 w-3" />
                                <span>{formatCurrency(invoice.totalAmount)}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            View
                          </Button>
                          <Button variant="outline" size="sm">
                            <Send className="h-4 w-4 mr-2" />
                            Send
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4 mr-2" />
                            PDF
                          </Button>
                        </div>
                      </div>

                      {/* Invoice Items Preview */}
                      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
                        <div className="space-y-2">
                          {invoice.items.map((item) => (
                            <div key={item.id} className="flex items-center justify-between text-sm">
                              <span className="text-gray-600 dark:text-gray-400">
                                {item.description} (x{item.quantity})
                              </span>
                              <span className="font-medium text-gray-900 dark:text-white">
                                {formatCurrency(item.total)}
                              </span>
                            </div>
                          ))}
                          <div className="flex items-center justify-between text-sm font-medium pt-2 border-t border-gray-200 dark:border-gray-600">
                            <span className="text-gray-900 dark:text-white">Total Amount</span>
                            <span className="text-gray-900 dark:text-white">
                              {formatCurrency(invoice.totalAmount)}
                            </span>
                          </div>
                          {invoice.balanceAmount > 0 && (
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-red-600 dark:text-red-400">Balance Due</span>
                              <span className="font-medium text-red-600 dark:text-red-400">
                                {formatCurrency(invoice.balanceAmount)}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Templates Tab */}
          <TabsContent value="templates" className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5 text-blue-600" />
                      Contract Templates
                    </CardTitle>
                    <CardDescription>
                      Pre-built contract templates for different types of photography services
                    </CardDescription>
                  </div>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Template
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {templates.map(template => (
                    <div key={template.id} className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <h3 className="font-semibold text-gray-900 dark:text-white">
                            {template.name}
                          </h3>
                          <div className="flex items-center gap-1">
                            {template.isDefault && (
                              <Badge variant="secondary" className="text-xs">
                                Default
                              </Badge>
                            )}
                            <Badge className={cn("text-xs", getCategoryColor(template.category))}>
                              {template.category}
                            </Badge>
                          </div>
                        </div>
                        
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {template.description}
                        </p>

                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          <p>Used {template.usage} times</p>
                          <p>{template.variables.length} variables</p>
                          <p>Updated {formatDate(template.updatedAt)}</p>
                        </div>

                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm" className="flex-1">
                            <FileText className="h-3 w-3 mr-2" />
                            Use Template
                          </Button>
                          <Button variant="outline" size="sm">
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Booking Requests Tab */}
          <TabsContent value="bookings" className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-green-600" />
                  Booking Requests
                </CardTitle>
                <CardDescription>
                  New booking inquiries from potential clients
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {bookingRequests.map(request => (
                    <div key={request.id} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold text-gray-900 dark:text-white">
                              {request.clientName}
                            </h3>
                            <Badge variant={getStatusColor(request.status) as any}>
                              {request.status}
                            </Badge>
                            <Badge className={cn("text-xs", getPriorityColor(request.priority))}>
                              {request.priority}
                            </Badge>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600 dark:text-gray-400 mb-3">
                            <div className="flex items-center gap-2">
                              <Camera className="h-4 w-4" />
                              <span>{request.eventType}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4" />
                              <span>{formatDate(request.eventDate)}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <MapPin className="h-4 w-4" />
                              <span>{request.eventLocation}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <DollarSign className="h-4 w-4" />
                              <span>Budget: {formatCurrency(request.budget)}</span>
                            </div>
                          </div>

                          <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-3">
                            <div className="flex items-center gap-2">
                              <Mail className="h-4 w-4" />
                              <span>{request.clientEmail}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Phone className="h-4 w-4" />
                              <span>{request.clientPhone}</span>
                            </div>
                          </div>

                          <p className="text-sm text-gray-700 dark:text-gray-300 mb-3">
                            {request.message}
                          </p>

                          <p className="text-xs text-gray-500 dark:text-gray-500">
                            Received {timeAgo(request.createdAt)}
                          </p>
                        </div>

                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm">
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Approve
                          </Button>
                          <Button variant="outline" size="sm">
                            <XCircle className="h-4 w-4 mr-2" />
                            Decline
                          </Button>
                          <Button variant="outline" size="sm">
                            <Mail className="h-4 w-4 mr-2" />
                            Reply
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle>Contract Status Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {['signed', 'sent', 'viewed', 'draft', 'completed'].map(status => {
                      const count = contracts.filter(c => c.status === status).length;
                      const percentage = contracts.length > 0 ? (count / contracts.length) * 100 : 0;
                      return (
                        <div key={status} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className={cn(
                              "h-3 w-3 rounded-full",
                              status === 'signed' ? 'bg-green-500' :
                              status === 'sent' ? 'bg-blue-500' :
                              status === 'viewed' ? 'bg-yellow-500' :
                              status === 'draft' ? 'bg-gray-500' :
                              'bg-purple-500'
                            )} />
                            <span className="text-sm font-medium capitalize">{status}</span>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-bold">{count}</p>
                            <p className="text-xs text-gray-500">{percentage.toFixed(1)}%</p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle>Revenue by Event Type</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {['Wedding', 'Birthday', 'Engagement', 'Corporate'].map(eventType => {
                      const typeContracts = contracts.filter(c => c.eventType === eventType);
                      const revenue = typeContracts.reduce((sum, c) => sum + c.totalAmount, 0);
                      return (
                        <div key={eventType} className="flex items-center justify-between">
                          <span className="text-sm font-medium">{eventType}</span>
                          <div className="text-right">
                            <p className="text-sm font-bold">{formatCurrency(revenue)}</p>
                            <p className="text-xs text-gray-500">{typeContracts.length} contracts</p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </StudioLayout>
  );
}
