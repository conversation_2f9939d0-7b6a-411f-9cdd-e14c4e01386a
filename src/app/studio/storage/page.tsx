"use client";

import { useState } from "react";
import {
  HardDrive,
  Folder,
  FileImage,
  Trash2,
  Download,
  Eye,
  BarChart3,
  Users,
  Calendar,
  Filter,
  Search,
  RotateCcw,
  AlertTriangle,
  CheckCircle,
  Clock,
  Archive,
  Video,
  File,
  Star,
  Tag,
  Zap,
  Settings,
  MoreHorizontal,
  TrendingUp,
  RefreshCw,
  Grid,
  List,
  SortAsc,
  FolderOpen
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import StudioLayout from "@/components/studio/StudioLayout";
import { formatFileSize, formatDate, timeAgo, cn } from "@/lib/utils";

interface StorageItem {
  id: string;
  name: string;
  type: 'folder' | 'image' | 'video' | 'document' | 'archive';
  size: number;
  clientId?: string;
  clientName?: string;
  eventType?: string;
  eventName?: string;
  uploadDate: string;
  lastAccessed?: string;
  downloads: number;
  isInTrash: boolean;
  isStarred?: boolean;
  thumbnailUrl?: string;
  tags: string[];
  aiTags?: string[];
  faceCount?: number;
  status: 'active' | 'archived' | 'deleted';
  resolution?: string;
  duration?: number; // for videos
}

interface ClientStorage {
  clientId: string;
  clientName: string;
  eventType: string;
  eventName: string;
  totalSize: number;
  photoCount: number;
  videoCount: number;
  documentCount: number;
  lastActivity: string;
  storagePercentage: number;
  storageUsed: number;
  storageLimit: number;
}

interface FileTypeAnalysis {
  type: string;
  count: number;
  totalSize: number;
  percentage: number;
  avgSize: number;
}

export default function StoragePage() {
  const [activeTab, setActiveTab] = useState("overview");
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState("all");
  const [clientFilter, setClientFilter] = useState("all");
  const [sortBy, setSortBy] = useState("uploadDate");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showTrash, setShowTrash] = useState(false);

  const [storageItems] = useState<StorageItem[]>([
    {
      id: '1',
      name: 'wedding_ceremony_001.jpg',
      type: 'image',
      size: 8500000, // 8.5MB
      clientId: 'client1',
      clientName: 'Sarah Johnson',
      eventType: 'Wedding',
      eventName: 'Wedding Ceremony',
      uploadDate: '2024-01-20T10:30:00Z',
      lastAccessed: '2024-01-20T14:20:00Z',
      downloads: 12,
      isInTrash: false,
      isStarred: true,
      thumbnailUrl: '/api/placeholder/150/150',
      tags: ['wedding', 'ceremony', 'bride'],
      aiTags: ['people', 'wedding dress', 'flowers', 'indoor'],
      faceCount: 3,
      status: 'active',
      resolution: '4000x3000'
    },
    {
      id: '2',
      name: 'birthday_party_video.mp4',
      type: 'video',
      size: 125000000, // 125MB
      clientId: 'client2',
      clientName: 'Mike Davis',
      eventType: 'Birthday',
      eventName: 'Birthday Party',
      uploadDate: '2024-01-18T16:45:00Z',
      lastAccessed: '2024-01-19T09:15:00Z',
      downloads: 5,
      isInTrash: false,
      isStarred: false,
      thumbnailUrl: '/api/placeholder/150/150',
      tags: ['birthday', 'party', 'celebration'],
      aiTags: ['party', 'cake', 'children', 'indoor'],
      faceCount: 8,
      status: 'active',
      duration: 180 // 3 minutes
    },
    {
      id: '3',
      name: 'engagement_photos.zip',
      type: 'archive',
      size: 450000000, // 450MB
      clientId: 'client3',
      clientName: 'Emma Wilson',
      eventType: 'Engagement',
      eventName: 'Engagement Shoot',
      uploadDate: '2024-01-15T11:20:00Z',
      lastAccessed: '2024-01-16T08:30:00Z',
      downloads: 3,
      isInTrash: false,
      isStarred: false,
      tags: ['engagement', 'outdoor', 'sunset'],
      aiTags: ['outdoor', 'nature', 'couple'],
      status: 'active'
    },
    {
      id: '4',
      name: 'deleted_event_folder',
      type: 'folder',
      size: *********, // 800MB
      clientId: 'client4',
      clientName: 'John Smith',
      eventType: 'Corporate',
      eventName: 'Company Event',
      uploadDate: '2024-01-05T09:00:00Z',
      lastAccessed: '2024-01-12T15:30:00Z',
      downloads: 8,
      isInTrash: true,
      isStarred: false,
      tags: ['corporate', 'event'],
      status: 'deleted'
    }
  ]);

  const [clientStorage] = useState<ClientStorage[]>([
    {
      clientId: 'client1',
      clientName: 'Sarah Johnson',
      eventType: 'Wedding',
      eventName: 'Wedding Ceremony',
      totalSize: 1200000000, // 1.2GB
      photoCount: 142,
      videoCount: 8,
      documentCount: 2,
      lastActivity: '2024-01-20T14:20:00Z',
      storagePercentage: 24,
      storageUsed: 1200000000,
      storageLimit: 2000000000 // 2GB
    },
    {
      clientId: 'client2',
      clientName: 'Mike Davis',
      eventType: 'Birthday',
      eventName: 'Birthday Party',
      totalSize: *********, // 650MB
      photoCount: 78,
      videoCount: 7,
      documentCount: 1,
      lastActivity: '2024-01-19T09:15:00Z',
      storagePercentage: 13,
      storageUsed: *********,
      storageLimit: 1000000000 // 1GB
    },
    {
      clientId: 'client3',
      clientName: 'Emma Wilson',
      eventType: 'Engagement',
      eventName: 'Engagement Shoot',
      totalSize: 950000000, // 950MB
      photoCount: 115,
      videoCount: 5,
      documentCount: 0,
      lastActivity: '2024-01-16T08:30:00Z',
      storagePercentage: 19,
      storageUsed: 950000000,
      storageLimit: 1500000000 // 1.5GB
    }
  ]);

  const totalStorage = 50000000000; // 50GB
  const usedStorage = storageItems.reduce((sum, item) => sum + item.size, 0);
  const storagePercentage = (usedStorage / totalStorage) * 100;
  const trashItems = storageItems.filter(item => item.isInTrash);
  const activeItems = storageItems.filter(item => !item.isInTrash);

  // File type analysis
  const fileTypeAnalysis: FileTypeAnalysis[] = ['image', 'video', 'archive', 'document', 'folder'].map(type => {
    const items = activeItems.filter(item => item.type === type);
    const totalSize = items.reduce((sum, item) => sum + item.size, 0);
    return {
      type,
      count: items.length,
      totalSize,
      percentage: (totalSize / usedStorage) * 100,
      avgSize: items.length > 0 ? totalSize / items.length : 0
    };
  });

  const filteredItems = (showTrash ? trashItems : activeItems).filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.clientName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         item.aiTags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesType = typeFilter === "all" || item.type === typeFilter;
    const matchesClient = clientFilter === "all" || item.clientId === clientFilter;

    return matchesSearch && matchesType && matchesClient;
  });

  const getFileTypeIcon = (type: string) => {
    switch (type) {
      case 'folder': return Folder;
      case 'image': return FileImage;
      case 'video': return Video;
      case 'archive': return Archive;
      case 'document': return File;
      default: return File;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'image': return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20';
      case 'video': return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'archive': return 'text-purple-600 bg-purple-100 dark:bg-purple-900/20';
      case 'document': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20';
      case 'folder': return 'text-gray-600 bg-gray-100 dark:bg-gray-700';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-700';
    }
  };

  const getStorageColor = () => {
    if (storagePercentage > 90) return "destructive";
    if (storagePercentage > 75) return "warning";
    return "default";
  };

  const clients = [...new Set(storageItems.map(item => ({ id: item.clientId, name: item.clientName })))].filter(c => c.id);

  return (
    <StudioLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <HardDrive className="h-8 w-8 text-blue-600" />
              Storage & Media Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Manage your files, analyze storage usage, and organize media assets with AI-powered tagging
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Storage Settings
            </Button>
            <Button variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Sync Storage
            </Button>
            <Badge variant={storagePercentage > 80 ? 'destructive' : 'secondary'} className="text-sm">
              {storagePercentage.toFixed(1)}% Used
            </Badge>
          </div>
        </div>

        {/* Storage Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <HardDrive className="h-6 w-6 text-white" />
                </div>
                <div className="flex-1">
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {formatFileSize(usedStorage)}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Used Storage</p>
                  <Progress value={storagePercentage} className="h-2 mt-2" variant={getStorageColor()} />
                  <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                    {storagePercentage.toFixed(1)}% of {formatFileSize(totalStorage)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <FileImage className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {activeItems.filter(item => item.type === 'image').length}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Images</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <Video className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {activeItems.filter(item => item.type === 'video').length}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Videos</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-orange-600 rounded-xl flex items-center justify-center">
                  <Users className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {clients.length}
                  </p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">Clients</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="files">Files</TabsTrigger>
            <TabsTrigger value="clients">Client Storage</TabsTrigger>
            <TabsTrigger value="trash">Trash</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* File Type Analysis */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-blue-600" />
                    Storage by File Type
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {fileTypeAnalysis.filter(type => type.count > 0).map(type => (
                      <div key={type.type} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={cn(
                            "h-8 w-8 rounded-lg flex items-center justify-center",
                            getTypeColor(type.type)
                          )}>
                            {(() => {
                              const Icon = getFileTypeIcon(type.type);
                              return <Icon className="h-4 w-4" />;
                            })()}
                          </div>
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white capitalize">
                              {type.type}s
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {type.count} files • Avg: {formatFileSize(type.avgSize)}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-gray-900 dark:text-white">
                            {formatFileSize(type.totalSize)}
                          </p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {type.percentage.toFixed(1)}%
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-green-600" />
                    Recent Activity
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {activeItems
                      .sort((a, b) => new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime())
                      .slice(0, 5)
                      .map(item => (
                        <div key={item.id} className="flex items-center gap-3">
                          <div className={cn(
                            "h-8 w-8 rounded-lg flex items-center justify-center",
                            getTypeColor(item.type)
                          )}>
                            {(() => {
                              const Icon = getFileTypeIcon(item.type);
                              return <Icon className="h-4 w-4" />;
                            })()}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="font-medium text-gray-900 dark:text-white truncate">
                              {item.name}
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {item.clientName} • {timeAgo(item.uploadDate)}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                              {formatFileSize(item.size)}
                            </p>
                            {item.isStarred && (
                              <Star className="h-3 w-3 text-yellow-500 fill-current ml-auto" />
                            )}
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-orange-600" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                    <Archive className="h-6 w-6 text-blue-600" />
                    <span className="text-sm">Bulk Archive</span>
                  </Button>
                  <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                    <Download className="h-6 w-6 text-green-600" />
                    <span className="text-sm">Export Files</span>
                  </Button>
                  <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                    <Tag className="h-6 w-6 text-purple-600" />
                    <span className="text-sm">AI Auto Tag</span>
                  </Button>
                  <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                    <RefreshCw className="h-6 w-6 text-orange-600" />
                    <span className="text-sm">Optimize Storage</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Files Tab */}
          <TabsContent value="files" className="space-y-6">
            {/* Filters and Search */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                      <Input
                        placeholder="Search files by name, client, tags, or AI tags..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 h-11 bg-gray-50 dark:bg-gray-700"
                      />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <select
                      value={typeFilter}
                      onChange={(e) => setTypeFilter(e.target.value)}
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="all">All Types</option>
                      <option value="image">Images</option>
                      <option value="video">Videos</option>
                      <option value="archive">Archives</option>
                      <option value="document">Documents</option>
                      <option value="folder">Folders</option>
                    </select>
                    <select
                      value={clientFilter}
                      onChange={(e) => setClientFilter(e.target.value)}
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="all">All Clients</option>
                      {clients.map(client => (
                        <option key={client.id} value={client.id}>{client.name}</option>
                      ))}
                    </select>
                    <Button
                      variant={viewMode === 'grid' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setViewMode('grid')}
                    >
                      <Grid className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewMode === 'list' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setViewMode('list')}
                    >
                      <List className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Files Display */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                {viewMode === 'grid' ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                    {filteredItems.map(item => (
                      <div key={item.id} className="group relative bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:shadow-lg transition-all duration-200">
                        <div className="aspect-square bg-gray-200 dark:bg-gray-600 rounded-lg mb-3 flex items-center justify-center overflow-hidden">
                          {item.thumbnailUrl ? (
                            <img src={item.thumbnailUrl} alt={item.name} className="w-full h-full object-cover" />
                          ) : (
                            (() => {
                              const Icon = getFileTypeIcon(item.type);
                              return <Icon className="h-12 w-12 text-gray-400" />;
                            })()
                          )}
                        </div>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <h3 className="font-medium text-gray-900 dark:text-white text-sm truncate">
                              {item.name}
                            </h3>
                            {item.isStarred && (
                              <Star className="h-4 w-4 text-yellow-500 fill-current" />
                            )}
                          </div>
                          <p className="text-xs text-gray-600 dark:text-gray-400">
                            {item.clientName}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-500">
                            {formatFileSize(item.size)}
                          </p>
                          <div className="flex flex-wrap gap-1">
                            {item.tags.slice(0, 2).map(tag => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                          {item.aiTags && item.aiTags.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {item.aiTags.slice(0, 2).map(tag => (
                                <Badge key={tag} variant="outline" className="text-xs">
                                  <Zap className="h-2 w-2 mr-1" />
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>
                        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                          <Button variant="outline" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="space-y-2">
                    {filteredItems.map(item => (
                      <div key={item.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:shadow-md transition-all duration-200">
                        <div className="flex items-center gap-4">
                          <div className={cn(
                            "h-10 w-10 rounded-lg flex items-center justify-center",
                            getTypeColor(item.type)
                          )}>
                            {(() => {
                              const Icon = getFileTypeIcon(item.type);
                              return <Icon className="h-5 w-5" />;
                            })()}
                          </div>
                          <div>
                            <div className="flex items-center gap-2">
                              <h3 className="font-medium text-gray-900 dark:text-white">
                                {item.name}
                              </h3>
                              {item.isStarred && (
                                <Star className="h-4 w-4 text-yellow-500 fill-current" />
                              )}
                            </div>
                            <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                              <span>{item.clientName}</span>
                              <span>•</span>
                              <span>{formatFileSize(item.size)}</span>
                              <span>•</span>
                              <span>{formatDate(item.uploadDate)}</span>
                              {item.faceCount && (
                                <>
                                  <span>•</span>
                                  <span>{item.faceCount} faces</span>
                                </>
                              )}
                              {item.duration && (
                                <>
                                  <span>•</span>
                                  <span>{Math.floor(item.duration / 60)}:{(item.duration % 60).toString().padStart(2, '0')}</span>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="flex flex-wrap gap-1">
                            {item.tags.slice(0, 3).map(tag => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                            {item.aiTags && item.aiTags.slice(0, 2).map(tag => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                <Zap className="h-2 w-2 mr-1" />
                                {tag}
                              </Badge>
                            ))}
                          </div>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            View
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4 mr-2" />
                            Download
                          </Button>
                          <Button variant="outline" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Empty State */}
                {filteredItems.length === 0 && (
                  <div className="text-center py-12">
                    <Archive className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      No files found
                    </h3>
                    <p className="text-gray-500 dark:text-gray-400">
                      Try adjusting your search or filter criteria.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Client Storage Tab */}
          <TabsContent value="clients" className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-green-600" />
                  Client Storage Breakdown
                </CardTitle>
                <CardDescription>
                  Monitor storage usage by client and manage storage limits
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {clientStorage.map(client => {
                    const usagePercentage = (client.storageUsed / client.storageLimit) * 100;
                    return (
                      <div key={client.clientId} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-3">
                            <div className="h-10 w-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-semibold">
                              {client.clientName.charAt(0)}
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900 dark:text-white">
                                {client.clientName}
                              </h3>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                {client.eventName}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-gray-900 dark:text-white">
                              {formatFileSize(client.storageUsed)} / {formatFileSize(client.storageLimit)}
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {client.photoCount + client.videoCount + client.documentCount} files
                            </p>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-600 dark:text-gray-400">Storage Usage</span>
                            <span className="text-gray-600 dark:text-gray-400">
                              {usagePercentage.toFixed(1)}%
                            </span>
                          </div>
                          <Progress
                            value={usagePercentage}
                            className="h-2"
                            variant={usagePercentage > 90 ? 'destructive' : usagePercentage > 75 ? 'warning' : 'default'}
                          />
                        </div>

                        <div className="grid grid-cols-4 gap-4 mt-4 text-sm">
                          <div className="text-center">
                            <p className="font-semibold text-gray-900 dark:text-white">
                              {client.photoCount}
                            </p>
                            <p className="text-gray-600 dark:text-gray-400">Images</p>
                          </div>
                          <div className="text-center">
                            <p className="font-semibold text-gray-900 dark:text-white">
                              {client.videoCount}
                            </p>
                            <p className="text-gray-600 dark:text-gray-400">Videos</p>
                          </div>
                          <div className="text-center">
                            <p className="font-semibold text-gray-900 dark:text-white">
                              {client.documentCount}
                            </p>
                            <p className="text-gray-600 dark:text-gray-400">Documents</p>
                          </div>
                          <div className="text-center">
                            <p className="font-semibold text-gray-900 dark:text-white">
                              {timeAgo(client.lastActivity)}
                            </p>
                            <p className="text-gray-600 dark:text-gray-400">Last Activity</p>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Trash Tab */}
          <TabsContent value="trash" className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Trash2 className="h-5 w-5 text-red-600" />
                  Trash Bin
                </CardTitle>
                <CardDescription>
                  Recover or permanently delete files (files are auto-deleted after 30 days)
                </CardDescription>
              </CardHeader>
              <CardContent>
                {trashItems.length > 0 ? (
                  <div className="space-y-3">
                    {trashItems.map((item) => {
                      const FileIcon = getFileTypeIcon(item.type);
                      return (
                        <div key={item.id} className="flex items-center justify-between p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                          <div className="flex items-center gap-4">
                            <div className={cn(
                              "h-10 w-10 rounded-lg flex items-center justify-center",
                              getTypeColor(item.type)
                            )}>
                              <FileIcon className="h-5 w-5" />
                            </div>
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white">
                                {item.name}
                              </p>
                              <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                                <span>{formatFileSize(item.size)}</span>
                                {item.clientName && (
                                  <>
                                    <span>•</span>
                                    <span>{item.clientName}</span>
                                  </>
                                )}
                                <span>•</span>
                                <span>Deleted: {formatDate(item.uploadDate)}</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm">
                              <RotateCcw className="h-4 w-4 mr-2" />
                              Restore
                            </Button>
                            <Button variant="destructive" size="sm">
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete Forever
                            </Button>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Trash2 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      Trash is empty
                    </h3>
                    <p className="text-gray-500 dark:text-gray-400">
                      Deleted files will appear here and can be recovered within 30 days.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </StudioLayout>
  );
}
