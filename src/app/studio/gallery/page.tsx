"use client";

import { useState } from "react";
import { 
  Camera,
  Grid3X3,
  List,
  Search,
  Filter,
  Download,
  Share2,
  Eye,
  Heart,
  Star,
  Users,
  Calendar,
  Tag,
  MoreHorizontal,
  ZoomIn,
  Edit,
  Trash2
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import StudioLayout from "@/components/studio/StudioLayout";
import { formatDate, cn } from "@/lib/utils";

interface Photo {
  id: string;
  filename: string;
  thumbnailUrl: string;
  fullUrl: string;
  clientName?: string;
  eventType: string;
  eventDate: string;
  uploadDate: string;
  views: number;
  downloads: number;
  likes: number;
  isMatched: boolean;
  matchConfidence?: number;
  tags: string[];
  faceCount: number;
  isStarred: boolean;
}

interface Gallery {
  id: string;
  name: string;
  eventType: string;
  eventDate: string;
  clientName?: string;
  photoCount: number;
  coverPhoto: string;
  isPublic: boolean;
  qrCode: string;
  views: number;
  downloads: number;
}

export default function GalleryPage() {
  const [photos] = useState<Photo[]>([
    {
      id: '1',
      filename: 'IMG_001.jpg',
      thumbnailUrl: '/api/placeholder/300/300',
      fullUrl: '/api/placeholder/1200/800',
      clientName: 'Sarah Johnson',
      eventType: 'Wedding',
      eventDate: '2024-01-20',
      uploadDate: '2024-01-15',
      views: 45,
      downloads: 12,
      likes: 8,
      isMatched: true,
      matchConfidence: 95,
      tags: ['bride', 'ceremony', 'outdoor'],
      faceCount: 2,
      isStarred: true
    },
    {
      id: '2',
      filename: 'IMG_002.jpg',
      thumbnailUrl: '/api/placeholder/300/300',
      fullUrl: '/api/placeholder/1200/800',
      clientName: 'Mike Davis',
      eventType: 'Birthday',
      eventDate: '2024-01-18',
      uploadDate: '2024-01-16',
      views: 23,
      downloads: 5,
      likes: 3,
      isMatched: true,
      matchConfidence: 87,
      tags: ['party', 'indoor', 'group'],
      faceCount: 5,
      isStarred: false
    },
    {
      id: '3',
      filename: 'IMG_003.jpg',
      thumbnailUrl: '/api/placeholder/300/300',
      fullUrl: '/api/placeholder/1200/800',
      eventType: 'Engagement',
      eventDate: '2024-01-12',
      uploadDate: '2024-01-10',
      views: 18,
      downloads: 3,
      likes: 2,
      isMatched: false,
      tags: ['couple', 'sunset', 'outdoor'],
      faceCount: 2,
      isStarred: false
    }
  ]);

  const [galleries] = useState<Gallery[]>([
    {
      id: '1',
      name: 'Sarah & Mike Wedding',
      eventType: 'Wedding',
      eventDate: '2024-01-20',
      clientName: 'Sarah Johnson',
      photoCount: 150,
      coverPhoto: '/api/placeholder/400/300',
      isPublic: true,
      qrCode: 'QR123456',
      views: 245,
      downloads: 67
    },
    {
      id: '2',
      name: 'Mike Birthday Party',
      eventType: 'Birthday',
      eventDate: '2024-01-18',
      clientName: 'Mike Davis',
      photoCount: 85,
      coverPhoto: '/api/placeholder/400/300',
      isPublic: true,
      qrCode: 'QR789012',
      views: 123,
      downloads: 34
    }
  ]);

  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [showMatched, setShowMatched] = useState(false);
  const [selectedPhotos, setSelectedPhotos] = useState<string[]>([]);

  const filteredPhotos = photos.filter(photo => {
    const matchesSearch = photo.filename.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         photo.clientName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         photo.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesType = filterType === "all" || photo.eventType.toLowerCase() === filterType.toLowerCase();
    const matchesFilter = !showMatched || photo.isMatched;
    
    return matchesSearch && matchesType && matchesFilter;
  });

  const togglePhotoSelection = (photoId: string) => {
    setSelectedPhotos(prev => 
      prev.includes(photoId) 
        ? prev.filter(id => id !== photoId)
        : [...prev, photoId]
    );
  };

  const getEventTypeIcon = (eventType: string) => {
    switch (eventType.toLowerCase()) {
      case 'wedding': return '💒';
      case 'birthday': return '🎂';
      case 'engagement': return '💍';
      case 'corporate': return '🏢';
      default: return '📸';
    }
  };

  return (
    <StudioLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <Camera className="h-8 w-8 text-blue-600" />
              Photo Gallery
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Browse, organize, and manage your photo collections
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Badge variant="secondary" className="text-sm">
              {photos.length} Photos
            </Badge>
            <Badge variant="secondary" className="text-sm">
              {galleries.length} Galleries
            </Badge>
          </div>
        </div>

        {/* Gallery Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <Camera className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {photos.length}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Total Photos</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <Users className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {photos.filter(p => p.isMatched).length}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Matched Photos</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <Eye className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {photos.reduce((sum, p) => sum + p.views, 0)}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Total Views</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-orange-600 rounded-xl flex items-center justify-center">
                  <Download className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {photos.reduce((sum, p) => sum + p.downloads, 0)}
                  </p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">Downloads</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Galleries Section */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Grid3X3 className="h-5 w-5 text-blue-600" />
              Photo Galleries
            </CardTitle>
            <CardDescription>
              Organized collections of photos by event
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {galleries.map((gallery) => (
                <Card key={gallery.id} className="border border-gray-200 dark:border-gray-600 hover:shadow-lg transition-all duration-200 cursor-pointer">
                  <div className="relative">
                    <img
                      src={gallery.coverPhoto}
                      alt={gallery.name}
                      className="w-full h-48 object-cover rounded-t-lg"
                    />
                    <div className="absolute top-3 right-3">
                      <Badge variant={gallery.isPublic ? 'success' : 'secondary'} className="text-xs">
                        {gallery.isPublic ? 'Public' : 'Private'}
                      </Badge>
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          {gallery.name}
                        </h3>
                        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                          <span className="text-lg">{getEventTypeIcon(gallery.eventType)}</span>
                          <span>{gallery.eventType}</span>
                          <span>•</span>
                          <span>{formatDate(gallery.eventDate)}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-3">
                      <span>{gallery.photoCount} photos</span>
                      <div className="flex items-center gap-3">
                        <span className="flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          {gallery.views}
                        </span>
                        <span className="flex items-center gap-1">
                          <Download className="h-3 w-3" />
                          {gallery.downloads}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1">
                        <Share2 className="h-4 w-4 mr-2" />
                        Share
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Photo Browser */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Camera className="h-5 w-5 text-green-600" />
                  Photo Browser
                </CardTitle>
                <CardDescription>
                  Browse and manage individual photos
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Filters */}
            <div className="flex flex-col lg:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    placeholder="Search photos by filename, client, or tags..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 h-11 bg-gray-50 dark:bg-gray-700"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">All Events</option>
                  <option value="wedding">Wedding</option>
                  <option value="birthday">Birthday</option>
                  <option value="engagement">Engagement</option>
                  <option value="corporate">Corporate</option>
                </select>
                <Button
                  variant={showMatched ? "default" : "outline"}
                  size="sm"
                  onClick={() => setShowMatched(!showMatched)}
                >
                  <Users className="h-4 w-4 mr-2" />
                  Matched Only
                </Button>
              </div>
            </div>

            {/* Bulk Actions */}
            {selectedPhotos.length > 0 && (
              <div className="flex items-center gap-2 mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                  {selectedPhotos.length} photos selected
                </span>
                <div className="flex items-center gap-2 ml-auto">
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                  <Button variant="outline" size="sm">
                    <Share2 className="h-4 w-4 mr-2" />
                    Share
                  </Button>
                  <Button variant="outline" size="sm">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                </div>
              </div>
            )}

            {/* Photo Grid/List */}
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                {filteredPhotos.map((photo) => (
                  <div
                    key={photo.id}
                    className={cn(
                      "relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all duration-200",
                      selectedPhotos.includes(photo.id)
                        ? "border-blue-500 shadow-lg"
                        : "border-transparent hover:border-gray-300 dark:hover:border-gray-600"
                    )}
                    onClick={() => togglePhotoSelection(photo.id)}
                  >
                    <img
                      src={photo.thumbnailUrl}
                      alt={photo.filename}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center gap-2">
                        <Button variant="secondary" size="sm">
                          <ZoomIn className="h-4 w-4" />
                        </Button>
                        <Button variant="secondary" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="absolute top-2 left-2 flex items-center gap-1">
                      {photo.isStarred && (
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      )}
                      {photo.isMatched && (
                        <Badge variant="success" className="text-xs">
                          {photo.matchConfidence}%
                        </Badge>
                      )}
                    </div>
                    <div className="absolute bottom-2 left-2 right-2">
                      <div className="bg-black bg-opacity-75 rounded px-2 py-1">
                        <p className="text-white text-xs font-medium truncate">
                          {photo.filename}
                        </p>
                        <div className="flex items-center justify-between text-xs text-gray-300">
                          <span>{photo.faceCount} faces</span>
                          <div className="flex items-center gap-2">
                            <span className="flex items-center gap-1">
                              <Eye className="h-3 w-3" />
                              {photo.views}
                            </span>
                            <span className="flex items-center gap-1">
                              <Heart className="h-3 w-3" />
                              {photo.likes}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-3">
                {filteredPhotos.map((photo) => (
                  <div
                    key={photo.id}
                    className={cn(
                      "flex items-center gap-4 p-4 rounded-lg border transition-all duration-200 cursor-pointer",
                      selectedPhotos.includes(photo.id)
                        ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                        : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                    )}
                    onClick={() => togglePhotoSelection(photo.id)}
                  >
                    <img
                      src={photo.thumbnailUrl}
                      alt={photo.filename}
                      className="w-16 h-16 object-cover rounded-lg"
                    />
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <p className="font-medium text-gray-900 dark:text-white">
                          {photo.filename}
                        </p>
                        <div className="flex items-center gap-2">
                          {photo.isStarred && (
                            <Star className="h-4 w-4 text-yellow-400 fill-current" />
                          )}
                          {photo.isMatched && (
                            <Badge variant="success" className="text-xs">
                              {photo.matchConfidence}% match
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                        <span>{photo.clientName || 'Unmatched'}</span>
                        <span>•</span>
                        <span>{photo.eventType}</span>
                        <span>•</span>
                        <span>{photo.faceCount} faces</span>
                        <span>•</span>
                        <span>Uploaded: {formatDate(photo.uploadDate)}</span>
                      </div>
                      <div className="flex items-center gap-2 mt-2">
                        {photo.tags.map((tag, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            <Tag className="h-3 w-3 mr-1" />
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-right text-sm text-gray-600 dark:text-gray-400">
                        <div className="flex items-center gap-3">
                          <span className="flex items-center gap-1">
                            <Eye className="h-3 w-3" />
                            {photo.views}
                          </span>
                          <span className="flex items-center gap-1">
                            <Download className="h-3 w-3" />
                            {photo.downloads}
                          </span>
                          <span className="flex items-center gap-1">
                            <Heart className="h-3 w-3" />
                            {photo.likes}
                          </span>
                        </div>
                      </div>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Empty State */}
            {filteredPhotos.length === 0 && (
              <div className="text-center py-12">
                <Camera className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  No photos found
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                  Try adjusting your search or filter criteria
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </StudioLayout>
  );
}
