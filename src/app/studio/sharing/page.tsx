"use client";

import { useState } from "react";
import { 
  Share2,
  Download,
  Eye,
  Lock,
  Unlock,
  Link,
  QrCode,
  Mail,
  MessageSquare,
  Facebook,
  Twitter,
  Instagram,
  Copy,
  Settings,
  Image,
  Users,
  Calendar,
  Clock,
  Star,
  Heart,
  Shield,
  Key,
  Globe,
  Smartphone,
  Monitor,
  Tablet,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Plus,
  Edit,
  Trash2,
  Search,
  Filter,
  MoreHorizontal,
  Palette,
  Type,
  Layers,
  Zap,
  Activity,
  TrendingUp,
  BarChart3,
  PieChart
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Textarea } from "@/components/ui/textarea";
import StudioLayout from "@/components/studio/StudioLayout";
import { formatDate, timeAgo, cn } from "@/lib/utils";

interface SharedGallery {
  id: string;
  name: string;
  clientName: string;
  eventType: string;
  eventDate: string;
  photoCount: number;
  isPasswordProtected: boolean;
  password?: string;
  expiryDate?: string;
  downloadEnabled: boolean;
  socialSharingEnabled: boolean;
  watermarkEnabled: boolean;
  viewCount: number;
  downloadCount: number;
  shareCount: number;
  lastAccessed: string;
  createdAt: string;
  status: 'active' | 'expired' | 'disabled';
  shareUrl: string;
  qrCodeUrl: string;
}

interface WatermarkSettings {
  id: string;
  name: string;
  type: 'text' | 'logo' | 'both';
  text?: string;
  logoUrl?: string;
  position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
  opacity: number;
  size: 'small' | 'medium' | 'large';
  color: string;
  font: string;
  isDefault: boolean;
}

interface DownloadActivity {
  id: string;
  galleryId: string;
  galleryName: string;
  clientName: string;
  photoId?: string;
  photoName?: string;
  downloadType: 'single' | 'bulk' | 'gallery';
  fileSize: number;
  ipAddress: string;
  userAgent: string;
  location?: string;
  timestamp: string;
}

interface SharingAnalytics {
  totalShares: number;
  totalViews: number;
  totalDownloads: number;
  averageViewTime: number;
  popularPlatforms: {
    platform: string;
    shares: number;
    percentage: number;
  }[];
  deviceBreakdown: {
    device: string;
    views: number;
    percentage: number;
  }[];
  geographicData: {
    country: string;
    views: number;
    downloads: number;
  }[];
}

export default function SharingPage() {
  const [activeTab, setActiveTab] = useState("galleries");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [showCreateGallery, setShowCreateGallery] = useState(false);
  const [showWatermarkEditor, setShowWatermarkEditor] = useState(false);

  const [sharedGalleries] = useState<SharedGallery[]>([
    {
      id: '1',
      name: 'Sarah & John Wedding',
      clientName: 'Sarah Johnson',
      eventType: 'Wedding',
      eventDate: '2024-01-15',
      photoCount: 450,
      isPasswordProtected: true,
      password: 'wedding2024',
      expiryDate: '2024-03-15',
      downloadEnabled: true,
      socialSharingEnabled: true,
      watermarkEnabled: false,
      viewCount: 1250,
      downloadCount: 320,
      shareCount: 45,
      lastAccessed: '2024-01-20T14:30:00Z',
      createdAt: '2024-01-16T10:00:00Z',
      status: 'active',
      shareUrl: 'https://studio.com/gallery/sarah-john-wedding',
      qrCodeUrl: '/api/qr/gallery/1'
    },
    {
      id: '2',
      name: 'Mike Birthday Celebration',
      clientName: 'Mike Davis',
      eventType: 'Birthday',
      eventDate: '2024-01-18',
      photoCount: 180,
      isPasswordProtected: false,
      downloadEnabled: true,
      socialSharingEnabled: true,
      watermarkEnabled: true,
      viewCount: 680,
      downloadCount: 125,
      shareCount: 28,
      lastAccessed: '2024-01-21T16:45:00Z',
      createdAt: '2024-01-19T09:30:00Z',
      status: 'active',
      shareUrl: 'https://studio.com/gallery/mike-birthday',
      qrCodeUrl: '/api/qr/gallery/2'
    }
  ]);

  const [watermarkSettings] = useState<WatermarkSettings[]>([
    {
      id: '1',
      name: 'Studio Logo',
      type: 'logo',
      logoUrl: '/api/placeholder/200/60',
      position: 'bottom-right',
      opacity: 70,
      size: 'medium',
      color: '#ffffff',
      font: 'Arial',
      isDefault: true
    },
    {
      id: '2',
      name: 'Copyright Text',
      type: 'text',
      text: '© 2024 Studio Name',
      position: 'bottom-left',
      opacity: 80,
      size: 'small',
      color: '#000000',
      font: 'Helvetica',
      isDefault: false
    }
  ]);

  const [downloadActivity] = useState<DownloadActivity[]>([
    {
      id: '1',
      galleryId: '1',
      galleryName: 'Sarah & John Wedding',
      clientName: 'Sarah Johnson',
      photoId: 'photo_001',
      photoName: 'wedding_ceremony_01.jpg',
      downloadType: 'single',
      fileSize: 2500000,
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X)',
      location: 'New York, NY',
      timestamp: '2024-01-20T14:30:00Z'
    },
    {
      id: '2',
      galleryId: '1',
      galleryName: 'Sarah & John Wedding',
      clientName: 'Sarah Johnson',
      downloadType: 'bulk',
      fileSize: 125000000,
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
      location: 'New York, NY',
      timestamp: '2024-01-20T15:45:00Z'
    }
  ]);

  const [sharingAnalytics] = useState<SharingAnalytics>({
    totalShares: 1250,
    totalViews: 15420,
    totalDownloads: 3680,
    averageViewTime: 4.2,
    popularPlatforms: [
      { platform: 'Direct Link', shares: 650, percentage: 52 },
      { platform: 'WhatsApp', shares: 280, percentage: 22.4 },
      { platform: 'Facebook', shares: 180, percentage: 14.4 },
      { platform: 'Instagram', shares: 90, percentage: 7.2 },
      { platform: 'Email', shares: 50, percentage: 4 }
    ],
    deviceBreakdown: [
      { device: 'Mobile', views: 8950, percentage: 58 },
      { device: 'Desktop', views: 4320, percentage: 28 },
      { device: 'Tablet', views: 2150, percentage: 14 }
    ],
    geographicData: [
      { country: 'United States', views: 8500, downloads: 2100 },
      { country: 'Canada', views: 2800, downloads: 680 },
      { country: 'United Kingdom', views: 1900, downloads: 420 },
      { country: 'Australia', views: 1200, downloads: 280 },
      { country: 'Germany', views: 1020, downloads: 200 }
    ]
  });

  const filteredGalleries = sharedGalleries.filter(gallery => {
    const matchesSearch = gallery.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         gallery.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         gallery.eventType.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || gallery.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'default';
      case 'expired': return 'secondary';
      case 'disabled': return 'destructive';
      default: return 'secondary';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // In a real app, show a toast notification
    console.log('Copied to clipboard:', text);
  };

  const generateShareUrl = (galleryId: string) => {
    return `https://studio.com/gallery/${galleryId}`;
  };

  return (
    <StudioLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <Share2 className="h-8 w-8 text-blue-600" />
              Downloads & Sharing
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Manage gallery sharing, downloads, watermarks, and track sharing analytics
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button 
              variant="outline"
              onClick={() => setShowWatermarkEditor(true)}
            >
              <Shield className="h-4 w-4 mr-2" />
              Watermark Settings
            </Button>
            <Button 
              onClick={() => setShowCreateGallery(true)}
              className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Shared Gallery
            </Button>
          </div>
        </div>

        {/* Sharing Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <Share2 className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {sharingAnalytics.totalShares.toLocaleString()}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Total Shares</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <Eye className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {sharingAnalytics.totalViews.toLocaleString()}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Total Views</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <Download className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {sharingAnalytics.totalDownloads.toLocaleString()}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Total Downloads</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-orange-600 rounded-xl flex items-center justify-center">
                  <Clock className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {sharingAnalytics.averageViewTime}m
                  </p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">Avg View Time</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="galleries">Shared Galleries</TabsTrigger>
            <TabsTrigger value="watermarks">Watermarks</TabsTrigger>
            <TabsTrigger value="activity">Download Activity</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          {/* Shared Galleries Tab */}
          <TabsContent value="galleries" className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Share2 className="h-5 w-5 text-blue-600" />
                  Shared Galleries ({filteredGalleries.length})
                </CardTitle>
                <CardDescription>
                  Manage your shared photo galleries and their settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredGalleries.map((gallery) => (
                    <div key={gallery.id} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-4">
                          <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                            <Image className="h-6 w-6 text-white" />
                          </div>
                          <div>
                            <div className="flex items-center gap-2">
                              <h3 className="font-semibold text-gray-900 dark:text-white">
                                {gallery.name}
                              </h3>
                              <Badge variant={getStatusColor(gallery.status)}>
                                {gallery.status}
                              </Badge>
                              {gallery.isPasswordProtected && (
                                <Lock className="h-4 w-4 text-yellow-600" />
                              )}
                            </div>
                            <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mt-1">
                              <div className="flex items-center gap-1">
                                <Users className="h-3 w-3" />
                                <span>{gallery.clientName}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                <span>{formatDate(gallery.eventDate)}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Image className="h-3 w-3" />
                                <span>{gallery.photoCount} photos</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            View
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => copyToClipboard(gallery.shareUrl)}
                          >
                            <Copy className="h-4 w-4 mr-2" />
                            Copy Link
                          </Button>
                        </div>
                      </div>

                      {/* Gallery Stats */}
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-3 bg-white dark:bg-gray-800 rounded-lg">
                        <div className="text-center">
                          <div className="text-lg font-bold text-blue-600">
                            {gallery.viewCount.toLocaleString()}
                          </div>
                          <div className="text-xs text-gray-500">Views</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-green-600">
                            {gallery.downloadCount.toLocaleString()}
                          </div>
                          <div className="text-xs text-gray-500">Downloads</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-purple-600">
                            {gallery.shareCount.toLocaleString()}
                          </div>
                          <div className="text-xs text-gray-500">Shares</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-orange-600">
                            {timeAgo(gallery.lastAccessed)}
                          </div>
                          <div className="text-xs text-gray-500">Last Access</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </StudioLayout>
  );
}
