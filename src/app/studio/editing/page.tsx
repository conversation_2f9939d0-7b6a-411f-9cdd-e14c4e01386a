"use client";

import { useState } from "react";
import { 
  Image,
  Edit,
  Wand2,
  <PERSON><PERSON>,
  <PERSON>lide<PERSON>,
  RotateCcw,
  RotateCw,
  Crop,
  Zap,
  Download,
  Share2,
  Eye,
  EyeOff,
  Plus,
  Minus,
  Sun,
  Moon,
  Contrast,
  Droplets,
  Sparkles,
  Filter,
  Search,
  Grid,
  List,
  Clock,
  CheckCircle,
  AlertTriangle,
  Play,
  Pause,
  Settings,
  Upload,
  Folder,
  Star,
  Trash2,
  Copy,
  Layers,
  Move,
  Square,
  Circle
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Slider } from "@/components/ui/slider";
import { Progress } from "@/components/ui/progress";
import StudioLayout from "@/components/studio/StudioLayout";
import { formatDate, timeAgo, cn } from "@/lib/utils";

interface EditingProject {
  id: string;
  name: string;
  clientName: string;
  eventType: string;
  totalPhotos: number;
  editedPhotos: number;
  status: 'pending' | 'in_progress' | 'review' | 'completed';
  priority: 'low' | 'medium' | 'high';
  dueDate: string;
  createdAt: string;
  updatedAt: string;
  presets: string[];
  tags: string[];
  isStarred: boolean;
}

interface Photo {
  id: string;
  projectId: string;
  filename: string;
  originalUrl: string;
  editedUrl?: string;
  thumbnailUrl: string;
  status: 'raw' | 'editing' | 'edited' | 'approved' | 'rejected';
  editingTime: number; // in minutes
  presets: string[];
  adjustments: {
    exposure: number;
    contrast: number;
    highlights: number;
    shadows: number;
    whites: number;
    blacks: number;
    saturation: number;
    vibrance: number;
    temperature: number;
    tint: number;
  };
  createdAt: string;
  updatedAt: string;
}

interface EditingPreset {
  id: string;
  name: string;
  category: 'portrait' | 'landscape' | 'wedding' | 'event' | 'vintage' | 'modern' | 'custom';
  adjustments: {
    exposure: number;
    contrast: number;
    highlights: number;
    shadows: number;
    whites: number;
    blacks: number;
    saturation: number;
    vibrance: number;
    temperature: number;
    tint: number;
  };
  isDefault: boolean;
  usage: number;
  createdAt: string;
}

interface BatchEditOperation {
  id: string;
  name: string;
  type: 'preset' | 'adjustment' | 'watermark' | 'resize' | 'crop';
  params: any;
  photoIds: string[];
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  createdAt: string;
}

interface EditingHistory {
  id: string;
  photoId: string;
  action: string;
  params: any;
  timestamp: string;
  canUndo: boolean;
}

interface AIEnhancement {
  id: string;
  name: string;
  description: string;
  type: 'noise_reduction' | 'sharpening' | 'color_enhancement' | 'sky_replacement' | 'object_removal';
  isAvailable: boolean;
  processingTime: number; // in seconds
  cost?: number; // in credits
}

export default function EditingPage() {
  const [activeTab, setActiveTab] = useState("projects");
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const [selectedPhoto, setSelectedPhoto] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showEditor, setShowEditor] = useState(false);
  const [showBatchEditor, setShowBatchEditor] = useState(false);
  const [selectedPhotos, setSelectedPhotos] = useState<string[]>([]);
  const [showAIEnhancements, setShowAIEnhancements] = useState(false);
  const [editingHistory, setEditingHistory] = useState<EditingHistory[]>([]);
  const [currentAdjustments, setCurrentAdjustments] = useState({
    exposure: 0,
    contrast: 0,
    highlights: 0,
    shadows: 0,
    whites: 0,
    blacks: 0,
    saturation: 0,
    vibrance: 0,
    temperature: 0,
    tint: 0
  });

  const [projects] = useState<EditingProject[]>([
    {
      id: '1',
      name: 'Sarah & John Wedding',
      clientName: 'Sarah Johnson',
      eventType: 'Wedding',
      totalPhotos: 250,
      editedPhotos: 180,
      status: 'in_progress',
      priority: 'high',
      dueDate: '2024-02-01T00:00:00Z',
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-20T14:30:00Z',
      presets: ['Wedding Classic', 'Warm Tone', 'Soft Portrait'],
      tags: ['wedding', 'outdoor', 'golden hour'],
      isStarred: true
    },
    {
      id: '2',
      name: 'Mike\'s Birthday Party',
      clientName: 'Mike Davis',
      eventType: 'Birthday',
      totalPhotos: 120,
      editedPhotos: 120,
      status: 'completed',
      priority: 'medium',
      dueDate: '2024-01-25T00:00:00Z',
      createdAt: '2024-01-18T16:00:00Z',
      updatedAt: '2024-01-24T11:20:00Z',
      presets: ['Party Bright', 'Vibrant Colors'],
      tags: ['birthday', 'indoor', 'party'],
      isStarred: false
    },
    {
      id: '3',
      name: 'Emma & David Engagement',
      clientName: 'Emma Wilson',
      eventType: 'Engagement',
      totalPhotos: 80,
      editedPhotos: 25,
      status: 'in_progress',
      priority: 'low',
      dueDate: '2024-01-30T00:00:00Z',
      createdAt: '2024-01-20T09:15:00Z',
      updatedAt: '2024-01-20T16:45:00Z',
      presets: ['Natural Portrait', 'Soft Light'],
      tags: ['engagement', 'outdoor', 'sunset'],
      isStarred: false
    }
  ]);

  const [photos] = useState<Photo[]>([
    {
      id: '1',
      projectId: '1',
      filename: 'IMG_001.jpg',
      originalUrl: '/api/placeholder/800/600',
      editedUrl: '/api/placeholder/800/600',
      thumbnailUrl: '/api/placeholder/200/150',
      status: 'edited',
      editingTime: 15,
      presets: ['Wedding Classic'],
      adjustments: {
        exposure: 0.5,
        contrast: 0.2,
        highlights: -0.3,
        shadows: 0.4,
        whites: 0.1,
        blacks: -0.2,
        saturation: 0.1,
        vibrance: 0.3,
        temperature: 200,
        tint: 10
      },
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-20T14:30:00Z'
    },
    {
      id: '2',
      projectId: '1',
      filename: 'IMG_002.jpg',
      originalUrl: '/api/placeholder/800/600',
      thumbnailUrl: '/api/placeholder/200/150',
      status: 'editing',
      editingTime: 8,
      presets: [],
      adjustments: {
        exposure: 0,
        contrast: 0,
        highlights: 0,
        shadows: 0,
        whites: 0,
        blacks: 0,
        saturation: 0,
        vibrance: 0,
        temperature: 0,
        tint: 0
      },
      createdAt: '2024-01-15T10:05:00Z',
      updatedAt: '2024-01-20T15:00:00Z'
    }
  ]);

  const [presets] = useState<EditingPreset[]>([
    {
      id: '1',
      name: 'Wedding Classic',
      category: 'wedding',
      adjustments: {
        exposure: 0.3,
        contrast: 0.2,
        highlights: -0.2,
        shadows: 0.3,
        whites: 0.1,
        blacks: -0.1,
        saturation: 0.1,
        vibrance: 0.2,
        temperature: 100,
        tint: 5
      },
      isDefault: true,
      usage: 450,
      createdAt: '2023-06-15T10:00:00Z'
    },
    {
      id: '2',
      name: 'Natural Portrait',
      category: 'portrait',
      adjustments: {
        exposure: 0.2,
        contrast: 0.1,
        highlights: -0.1,
        shadows: 0.2,
        whites: 0.05,
        blacks: -0.05,
        saturation: -0.1,
        vibrance: 0.1,
        temperature: 50,
        tint: 0
      },
      isDefault: true,
      usage: 320,
      createdAt: '2023-07-20T14:00:00Z'
    },
    {
      id: '3',
      name: 'Vibrant Colors',
      category: 'event',
      adjustments: {
        exposure: 0.1,
        contrast: 0.3,
        highlights: -0.1,
        shadows: 0.1,
        whites: 0.1,
        blacks: -0.2,
        saturation: 0.4,
        vibrance: 0.5,
        temperature: 0,
        tint: 0
      },
      isDefault: false,
      usage: 180,
      createdAt: '2023-08-10T16:30:00Z'
    }
  ]);

  const [currentAdjustments, setCurrentAdjustments] = useState({
    exposure: 0,
    contrast: 0,
    highlights: 0,
    shadows: 0,
    whites: 0,
    blacks: 0,
    saturation: 0,
    vibrance: 0,
    temperature: 0,
    tint: 0
  });

  const [aiEnhancements] = useState<AIEnhancement[]>([
    {
      id: '1',
      name: 'AI Noise Reduction',
      description: 'Remove noise while preserving detail',
      type: 'noise_reduction',
      isAvailable: true,
      processingTime: 30,
      cost: 1
    },
    {
      id: '2',
      name: 'Smart Sharpening',
      description: 'Intelligently sharpen images',
      type: 'sharpening',
      isAvailable: true,
      processingTime: 15,
      cost: 1
    },
    {
      id: '3',
      name: 'Color Enhancement',
      description: 'AI-powered color correction',
      type: 'color_enhancement',
      isAvailable: true,
      processingTime: 45,
      cost: 2
    },
    {
      id: '4',
      name: 'Sky Replacement',
      description: 'Replace skies with AI precision',
      type: 'sky_replacement',
      isAvailable: true,
      processingTime: 120,
      cost: 5
    },
    {
      id: '5',
      name: 'Object Removal',
      description: 'Remove unwanted objects seamlessly',
      type: 'object_removal',
      isAvailable: false,
      processingTime: 180,
      cost: 8
    }
  ]);

  const [batchOperations] = useState<BatchEditOperation[]>([
    {
      id: '1',
      name: 'Wedding Preset Application',
      type: 'preset',
      params: { presetId: 'wedding-classic' },
      photoIds: ['1', '2', '3', '4', '5'],
      status: 'completed',
      progress: 100,
      createdAt: '2024-01-20T10:00:00Z'
    },
    {
      id: '2',
      name: 'Batch Resize for Web',
      type: 'resize',
      params: { width: 1920, height: 1080, quality: 85 },
      photoIds: ['6', '7', '8', '9', '10'],
      status: 'processing',
      progress: 65,
      createdAt: '2024-01-20T14:30:00Z'
    }
  ]);

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.clientName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || project.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const selectedProjectData = projects.find(p => p.id === selectedProject);
  const projectPhotos = photos.filter(p => p.projectId === selectedProject);
  const selectedPhotoData = photos.find(p => p.id === selectedPhoto);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'secondary';
      case 'in_progress': return 'warning';
      case 'review': return 'default';
      case 'completed': return 'success';
      case 'raw': return 'secondary';
      case 'editing': return 'warning';
      case 'edited': return 'success';
      case 'approved': return 'success';
      case 'rejected': return 'destructive';
      default: return 'secondary';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
      case 'low': return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-700';
    }
  };

  const getPresetCategoryColor = (category: string) => {
    switch (category) {
      case 'wedding': return 'bg-pink-100 dark:bg-pink-900/20 text-pink-700 dark:text-pink-300';
      case 'portrait': return 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300';
      case 'landscape': return 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300';
      case 'event': return 'bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300';
      case 'vintage': return 'bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300';
      case 'modern': return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  const applyPreset = (preset: EditingPreset) => {
    setCurrentAdjustments(preset.adjustments);
  };

  const resetAdjustments = () => {
    setCurrentAdjustments({
      exposure: 0,
      contrast: 0,
      highlights: 0,
      shadows: 0,
      whites: 0,
      blacks: 0,
      saturation: 0,
      vibrance: 0,
      temperature: 0,
      tint: 0
    });
  };

  const totalEditingTime = projects.reduce((sum, project) => {
    const projectPhotos = photos.filter(p => p.projectId === project.id);
    return sum + projectPhotos.reduce((photoSum, photo) => photoSum + photo.editingTime, 0);
  }, 0);

  return (
    <StudioLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <Edit className="h-8 w-8 text-purple-600" />
              Photo Editing Workflow
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Streamline your photo editing process with AI-powered tools and presets
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={() => setShowAIEnhancements(true)}
            >
              <Sparkles className="h-4 w-4 mr-2" />
              AI Tools
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowBatchEditor(true)}
              disabled={selectedPhotos.length === 0}
            >
              <Layers className="h-4 w-4 mr-2" />
              Batch Edit ({selectedPhotos.length})
            </Button>
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Import Photos
            </Button>
            <Button className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800">
              <Plus className="h-4 w-4 mr-2" />
              New Project
            </Button>
          </div>
        </div>

        {/* Editing Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <Folder className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {projects.length}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Active Projects</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <Image className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {photos.length}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Photos in Queue</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {photos.filter(p => p.status === 'edited' || p.status === 'approved').length}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Completed</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-orange-600 rounded-xl flex items-center justify-center">
                  <Clock className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {Math.round(totalEditingTime / 60)}h
                  </p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">Editing Time</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="projects">Projects</TabsTrigger>
            <TabsTrigger value="editor">Photo Editor</TabsTrigger>
            <TabsTrigger value="presets">Presets</TabsTrigger>
            <TabsTrigger value="batch">Batch Processing</TabsTrigger>
          </TabsList>

          {/* Projects Tab */}
          <TabsContent value="projects" className="space-y-6">
            {/* Filters */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                      <Input
                        placeholder="Search projects by name or client..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 h-11 bg-gray-50 dark:bg-gray-700"
                      />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="all">All Status</option>
                      <option value="pending">Pending</option>
                      <option value="in_progress">In Progress</option>
                      <option value="review">Review</option>
                      <option value="completed">Completed</option>
                    </select>
                    <Button
                      variant={viewMode === 'grid' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setViewMode('grid')}
                    >
                      <Grid className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewMode === 'list' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setViewMode('list')}
                    >
                      <List className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Projects Display */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                {viewMode === 'grid' ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {filteredProjects.map(project => {
                      const progress = (project.editedPhotos / project.totalPhotos) * 100;
                      return (
                        <div
                          key={project.id}
                          onClick={() => setSelectedProject(project.id)}
                          className={cn(
                            "p-6 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-lg",
                            selectedProject === project.id 
                              ? "border-purple-500 bg-purple-50 dark:bg-purple-900/20" 
                              : "border-gray-200 dark:border-gray-600 hover:border-purple-300"
                          )}
                        >
                          <div className="space-y-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <h3 className="font-semibold text-gray-900 dark:text-white">
                                  {project.name}
                                </h3>
                                {project.isStarred && (
                                  <Star className="h-4 w-4 text-yellow-500 fill-current" />
                                )}
                              </div>
                              <Badge variant={getStatusColor(project.status) as any}>
                                {project.status.replace('_', ' ')}
                              </Badge>
                            </div>
                            
                            <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                              <p>Client: {project.clientName}</p>
                              <p>Type: {project.eventType}</p>
                              <p>Due: {formatDate(project.dueDate)}</p>
                            </div>

                            <div className="space-y-2">
                              <div className="flex items-center justify-between text-sm">
                                <span className="text-gray-600 dark:text-gray-400">Progress</span>
                                <span className="font-medium">
                                  {project.editedPhotos}/{project.totalPhotos}
                                </span>
                              </div>
                              <Progress value={progress} className="h-2" />
                              <p className="text-xs text-gray-500">
                                {progress.toFixed(1)}% complete
                              </p>
                            </div>

                            <div className="flex items-center justify-between">
                              <Badge className={cn("text-xs", getPriorityColor(project.priority))}>
                                {project.priority} priority
                              </Badge>
                              <div className="flex items-center gap-1">
                                <Button variant="outline" size="sm">
                                  <Eye className="h-3 w-3" />
                                </Button>
                                <Button variant="outline" size="sm">
                                  <Edit className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {filteredProjects.map(project => {
                      const progress = (project.editedPhotos / project.totalPhotos) * 100;
                      return (
                        <div
                          key={project.id}
                          onClick={() => setSelectedProject(project.id)}
                          className={cn(
                            "p-4 rounded-lg border cursor-pointer transition-all duration-200",
                            selectedProject === project.id 
                              ? "border-purple-500 bg-purple-50 dark:bg-purple-900/20" 
                              : "border-gray-200 dark:border-gray-600 hover:border-purple-300"
                          )}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                              <div className="h-12 w-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-semibold">
                                {project.name.charAt(0)}
                              </div>
                              <div>
                                <div className="flex items-center gap-2">
                                  <h3 className="font-semibold text-gray-900 dark:text-white">
                                    {project.name}
                                  </h3>
                                  {project.isStarred && (
                                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                                  )}
                                  <Badge variant={getStatusColor(project.status) as any}>
                                    {project.status.replace('_', ' ')}
                                  </Badge>
                                  <Badge className={cn("text-xs", getPriorityColor(project.priority))}>
                                    {project.priority}
                                  </Badge>
                                </div>
                                <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mt-1">
                                  <span>{project.clientName}</span>
                                  <span>•</span>
                                  <span>{project.eventType}</span>
                                  <span>•</span>
                                  <span>Due {formatDate(project.dueDate)}</span>
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center gap-4">
                              <div className="text-right">
                                <p className="text-sm font-medium text-gray-900 dark:text-white">
                                  {project.editedPhotos}/{project.totalPhotos} photos
                                </p>
                                <Progress value={progress} className="h-2 w-24" />
                              </div>
                              <div className="flex items-center gap-1">
                                <Button variant="outline" size="sm">
                                  <Eye className="h-4 w-4 mr-2" />
                                  View
                                </Button>
                                <Button variant="outline" size="sm">
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Project Photos */}
            {selectedProjectData && (
              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Image className="h-5 w-5 text-purple-600" />
                    {selectedProjectData.name} - Photos
                  </CardTitle>
                  <CardDescription>
                    {projectPhotos.length} photos • {projectPhotos.filter(p => p.status === 'edited').length} edited
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    {projectPhotos.map(photo => (
                      <div
                        key={photo.id}
                        onClick={() => {
                          setSelectedPhoto(photo.id);
                          setShowEditor(true);
                        }}
                        className="relative group cursor-pointer"
                      >
                        <div className="aspect-square bg-gray-200 dark:bg-gray-600 rounded-lg overflow-hidden">
                          <img
                            src={photo.thumbnailUrl}
                            alt={photo.filename}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                          />
                        </div>
                        <div className="absolute top-2 right-2">
                          <Badge variant={getStatusColor(photo.status) as any} className="text-xs">
                            {photo.status}
                          </Badge>
                        </div>
                        <div className="absolute bottom-2 left-2 right-2">
                          <p className="text-xs text-white bg-black bg-opacity-50 px-2 py-1 rounded truncate">
                            {photo.filename}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Photo Editor Tab */}
          <TabsContent value="editor" className="space-y-6">
            {selectedPhotoData ? (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Photo Preview */}
                <Card className="lg:col-span-2 border-0 shadow-md bg-white dark:bg-gray-800">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <Edit className="h-5 w-5 text-purple-600" />
                        {selectedPhotoData.filename}
                      </CardTitle>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          <RotateCcw className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <RotateCw className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Crop className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" onClick={resetAdjustments}>
                          Reset
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="aspect-video bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
                      <img
                        src={selectedPhotoData.editedUrl || selectedPhotoData.originalUrl}
                        alt={selectedPhotoData.filename}
                        className="w-full h-full object-contain"
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Editing Controls */}
                <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Sliders className="h-5 w-5 text-purple-600" />
                      Adjustments
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Basic Adjustments */}
                    <div className="space-y-4">
                      <h4 className="font-medium text-gray-900 dark:text-white">Basic</h4>
                      
                      <div className="space-y-3">
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <label className="text-sm text-gray-600 dark:text-gray-400">Exposure</label>
                            <span className="text-sm font-mono">{currentAdjustments.exposure.toFixed(2)}</span>
                          </div>
                          <Slider
                            value={[currentAdjustments.exposure]}
                            onValueChange={(value) => setCurrentAdjustments(prev => ({ ...prev, exposure: value[0] }))}
                            min={-2}
                            max={2}
                            step={0.01}
                            className="w-full"
                          />
                        </div>

                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <label className="text-sm text-gray-600 dark:text-gray-400">Contrast</label>
                            <span className="text-sm font-mono">{currentAdjustments.contrast.toFixed(2)}</span>
                          </div>
                          <Slider
                            value={[currentAdjustments.contrast]}
                            onValueChange={(value) => setCurrentAdjustments(prev => ({ ...prev, contrast: value[0] }))}
                            min={-1}
                            max={1}
                            step={0.01}
                            className="w-full"
                          />
                        </div>

                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <label className="text-sm text-gray-600 dark:text-gray-400">Highlights</label>
                            <span className="text-sm font-mono">{currentAdjustments.highlights.toFixed(2)}</span>
                          </div>
                          <Slider
                            value={[currentAdjustments.highlights]}
                            onValueChange={(value) => setCurrentAdjustments(prev => ({ ...prev, highlights: value[0] }))}
                            min={-1}
                            max={1}
                            step={0.01}
                            className="w-full"
                          />
                        </div>

                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <label className="text-sm text-gray-600 dark:text-gray-400">Shadows</label>
                            <span className="text-sm font-mono">{currentAdjustments.shadows.toFixed(2)}</span>
                          </div>
                          <Slider
                            value={[currentAdjustments.shadows]}
                            onValueChange={(value) => setCurrentAdjustments(prev => ({ ...prev, shadows: value[0] }))}
                            min={-1}
                            max={1}
                            step={0.01}
                            className="w-full"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Color Adjustments */}
                    <div className="space-y-4">
                      <h4 className="font-medium text-gray-900 dark:text-white">Color</h4>
                      
                      <div className="space-y-3">
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <label className="text-sm text-gray-600 dark:text-gray-400">Saturation</label>
                            <span className="text-sm font-mono">{currentAdjustments.saturation.toFixed(2)}</span>
                          </div>
                          <Slider
                            value={[currentAdjustments.saturation]}
                            onValueChange={(value) => setCurrentAdjustments(prev => ({ ...prev, saturation: value[0] }))}
                            min={-1}
                            max={1}
                            step={0.01}
                            className="w-full"
                          />
                        </div>

                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <label className="text-sm text-gray-600 dark:text-gray-400">Vibrance</label>
                            <span className="text-sm font-mono">{currentAdjustments.vibrance.toFixed(2)}</span>
                          </div>
                          <Slider
                            value={[currentAdjustments.vibrance]}
                            onValueChange={(value) => setCurrentAdjustments(prev => ({ ...prev, vibrance: value[0] }))}
                            min={-1}
                            max={1}
                            step={0.01}
                            className="w-full"
                          />
                        </div>

                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <label className="text-sm text-gray-600 dark:text-gray-400">Temperature</label>
                            <span className="text-sm font-mono">{currentAdjustments.temperature}</span>
                          </div>
                          <Slider
                            value={[currentAdjustments.temperature]}
                            onValueChange={(value) => setCurrentAdjustments(prev => ({ ...prev, temperature: value[0] }))}
                            min={-1000}
                            max={1000}
                            step={10}
                            className="w-full"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="space-y-2">
                      <Button className="w-full bg-purple-600 hover:bg-purple-700">
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Apply Changes
                      </Button>
                      <div className="grid grid-cols-2 gap-2">
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Export
                        </Button>
                        <Button variant="outline" size="sm">
                          <Share2 className="h-4 w-4 mr-2" />
                          Share
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardContent className="p-12">
                  <div className="text-center">
                    <Edit className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      No Photo Selected
                    </h3>
                    <p className="text-gray-500 dark:text-gray-400">
                      Select a photo from a project to start editing
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Presets Tab */}
          <TabsContent value="presets" className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Wand2 className="h-5 w-5 text-purple-600" />
                      Editing Presets
                    </CardTitle>
                    <CardDescription>
                      Pre-configured adjustment sets for different photo styles
                    </CardDescription>
                  </div>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Preset
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {presets.map(preset => (
                    <div key={preset.id} className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <h3 className="font-semibold text-gray-900 dark:text-white">
                            {preset.name}
                          </h3>
                          <div className="flex items-center gap-1">
                            {preset.isDefault && (
                              <Badge variant="secondary" className="text-xs">
                                Default
                              </Badge>
                            )}
                            <Badge className={cn("text-xs", getPresetCategoryColor(preset.category))}>
                              {preset.category}
                            </Badge>
                          </div>
                        </div>
                        
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          <p>Used {preset.usage} times</p>
                          <p>Created {formatDate(preset.createdAt)}</p>
                        </div>

                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1"
                            onClick={() => applyPreset(preset)}
                          >
                            <Wand2 className="h-3 w-3 mr-2" />
                            Apply
                          </Button>
                          <Button variant="outline" size="sm">
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Batch Processing Tab */}
          <TabsContent value="batch" className="space-y-6">
            {/* Active Batch Operations */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-blue-600" />
                  Active Batch Operations
                </CardTitle>
                <CardDescription>
                  Monitor ongoing batch processing tasks
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {batchOperations.map((operation) => (
                    <div key={operation.id} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {operation.name}
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {operation.photoIds.length} photos • {formatDate(operation.createdAt)}
                          </p>
                        </div>
                        <Badge variant={operation.status === 'completed' ? 'default' : operation.status === 'processing' ? 'secondary' : 'destructive'}>
                          {operation.status}
                        </Badge>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600 dark:text-gray-400">Progress</span>
                          <span className="font-medium">{operation.progress}%</span>
                        </div>
                        <Progress value={operation.progress} className="h-2" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* AI Enhancements */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-purple-600" />
                  AI-Powered Enhancements
                </CardTitle>
                <CardDescription>
                  Apply advanced AI processing to your photos
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {aiEnhancements.map((enhancement) => (
                    <div key={enhancement.id} className={cn(
                      "p-4 rounded-lg border-2 transition-all duration-200",
                      enhancement.isAvailable
                        ? "border-gray-200 dark:border-gray-600 hover:border-purple-300 dark:hover:border-purple-600 cursor-pointer"
                        : "border-gray-100 dark:border-gray-700 opacity-50 cursor-not-allowed"
                    )}>
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                            {enhancement.name}
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                            {enhancement.description}
                          </p>
                          <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              <span>{enhancement.processingTime}s</span>
                            </div>
                            {enhancement.cost && (
                              <div className="flex items-center gap-1">
                                <Star className="h-3 w-3" />
                                <span>{enhancement.cost} credits</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <Button
                        size="sm"
                        className="w-full"
                        disabled={!enhancement.isAvailable}
                      >
                        {enhancement.isAvailable ? 'Apply Enhancement' : 'Coming Soon'}
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Batch Tools */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-green-600" />
                  Batch Processing Tools
                </CardTitle>
                <CardDescription>
                  Apply edits to multiple photos simultaneously
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Button variant="outline" className="h-24 flex flex-col items-center justify-center gap-2">
                    <Wand2 className="h-6 w-6 text-purple-600" />
                    <span className="text-sm font-medium">Apply Preset</span>
                    <span className="text-xs text-gray-500">Bulk preset application</span>
                  </Button>
                  <Button variant="outline" className="h-24 flex flex-col items-center justify-center gap-2">
                    <Sliders className="h-6 w-6 text-blue-600" />
                    <span className="text-sm font-medium">Sync Settings</span>
                    <span className="text-xs text-gray-500">Copy adjustments</span>
                  </Button>
                  <Button variant="outline" className="h-24 flex flex-col items-center justify-center gap-2">
                    <Crop className="h-6 w-6 text-orange-600" />
                    <span className="text-sm font-medium">Batch Crop</span>
                    <span className="text-xs text-gray-500">Uniform cropping</span>
                  </Button>
                  <Button variant="outline" className="h-24 flex flex-col items-center justify-center gap-2">
                    <Download className="h-6 w-6 text-green-600" />
                    <span className="text-sm font-medium">Batch Export</span>
                    <span className="text-xs text-gray-500">Export all photos</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </StudioLayout>
  );
}
