"use client";

import { useState } from "react";
import {
  Users,
  Plus,
  Search,
  Filter,
  Star,
  StarOff,
  Tag,
  MessageSquare,
  Calendar,
  Clock,
  Eye,
  Edit,
  Trash2,
  Download,
  Mail,
  Phone,
  MapPin,
  Camera,
  Heart,
  Share2,
  QrCode,
  Activity,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  User,
  Crown,
  Gift,
  Zap
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import { Textarea } from "@/components/ui/textarea";
import StudioLayout from "@/components/studio/StudioLayout";
import { formatDate, timeAgo, cn } from "@/lib/utils";

interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  isVIP: boolean;
  tags: string[];
  notes: string;
  totalPhotos: number;
  totalGalleries: number;
  totalDownloads: number;
  lastActivity: string;
  joinedDate: string;
  status: 'active' | 'inactive' | 'blocked';
  qrCodeExpiry?: string;
  favoritePhotos: number;
  sharedGalleries: number;
  storageUsed: number; // in bytes
  location?: string;
  eventTypes: string[];
  totalSpent: number;
  averageRating: number;
  referralSource?: string;
}

interface ClientActivity {
  id: string;
  clientId: string;
  type: 'gallery_view' | 'photo_download' | 'photo_favorite' | 'gallery_share' | 'qr_scan' | 'comment' | 'rating';
  description: string;
  timestamp: string;
  metadata?: any;
}

interface ClientNote {
  id: string;
  clientId: string;
  content: string;
  createdAt: string;
  createdBy: string;
  isImportant: boolean;
}

export default function ClientsPage() {
  const [activeTab, setActiveTab] = useState("overview");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [tagFilter, setTagFilter] = useState("all");
  const [selectedClient, setSelectedClient] = useState<string | null>(null);
  const [showAddNote, setShowAddNote] = useState(false);
  const [newNote, setNewNote] = useState("");

  const [clients] = useState<Client[]>([
    {
      id: '1',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      avatar: '/api/placeholder/40/40',
      isVIP: true,
      tags: ['wedding', 'premium', 'repeat-client'],
      notes: 'Preferred photographer for all events. Very detail-oriented.',
      totalPhotos: 450,
      totalGalleries: 8,
      totalDownloads: 125,
      lastActivity: '2024-01-20T14:30:00Z',
      joinedDate: '2023-06-15T10:00:00Z',
      status: 'active',
      qrCodeExpiry: '2024-02-20T00:00:00Z',
      favoritePhotos: 45,
      sharedGalleries: 3,
      storageUsed: 2500000000, // 2.5GB
      location: 'New York, NY',
      eventTypes: ['Wedding', 'Engagement', 'Anniversary'],
      totalSpent: 2500,
      averageRating: 4.9,
      referralSource: 'Google Search'
    },
    {
      id: '2',
      name: 'Mike Davis',
      email: '<EMAIL>',
      phone: '+****************',
      isVIP: false,
      tags: ['birthday', 'family'],
      notes: 'Loves candid shots. Prefers outdoor settings.',
      totalPhotos: 180,
      totalGalleries: 3,
      totalDownloads: 45,
      lastActivity: '2024-01-18T09:15:00Z',
      joinedDate: '2023-12-10T14:20:00Z',
      status: 'active',
      qrCodeExpiry: '2024-01-25T00:00:00Z',
      favoritePhotos: 18,
      sharedGalleries: 1,
      storageUsed: 850000000, // 850MB
      location: 'Los Angeles, CA',
      eventTypes: ['Birthday', 'Family Portrait'],
      totalSpent: 800,
      averageRating: 4.7,
      referralSource: 'Referral'
    },
    {
      id: '3',
      name: 'Emma Wilson',
      email: '<EMAIL>',
      phone: '+****************',
      isVIP: false,
      tags: ['engagement', 'new-client'],
      notes: 'First-time client. Very excited about the session.',
      totalPhotos: 75,
      totalGalleries: 1,
      totalDownloads: 12,
      lastActivity: '2024-01-15T16:45:00Z',
      joinedDate: '2024-01-10T11:30:00Z',
      status: 'active',
      favoritePhotos: 8,
      sharedGalleries: 0,
      storageUsed: 320000000, // 320MB
      location: 'Chicago, IL',
      eventTypes: ['Engagement'],
      totalSpent: 500,
      averageRating: 5.0,
      referralSource: 'Instagram'
    }
  ]);

  const [clientActivities] = useState<ClientActivity[]>([
    {
      id: '1',
      clientId: '1',
      type: 'gallery_view',
      description: 'Viewed wedding gallery',
      timestamp: '2024-01-20T14:30:00Z'
    },
    {
      id: '2',
      clientId: '1',
      type: 'photo_download',
      description: 'Downloaded 5 photos from wedding gallery',
      timestamp: '2024-01-20T14:25:00Z'
    },
    {
      id: '3',
      clientId: '2',
      type: 'photo_favorite',
      description: 'Added 3 photos to favorites',
      timestamp: '2024-01-18T09:15:00Z'
    }
  ]);

  const [clientNotes] = useState<ClientNote[]>([
    {
      id: '1',
      clientId: '1',
      content: 'Client requested additional editing for outdoor shots. Prefers warmer tones.',
      createdAt: '2024-01-15T10:00:00Z',
      createdBy: 'Studio Admin',
      isImportant: true
    },
    {
      id: '2',
      clientId: '1',
      content: 'Scheduled follow-up session for anniversary photos in March.',
      createdAt: '2024-01-10T14:30:00Z',
      createdBy: 'Studio Admin',
      isImportant: false
    }
  ]);

  const availableTags = ['wedding', 'birthday', 'engagement', 'family', 'corporate', 'premium', 'repeat-client', 'new-client', 'vip'];

  const filteredClients = clients.filter(client => {
    const matchesSearch = client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.phone.includes(searchTerm);
    const matchesStatus = statusFilter === "all" || client.status === statusFilter;
    const matchesTag = tagFilter === "all" || client.tags.includes(tagFilter);
    return matchesSearch && matchesStatus && matchesTag;
  });

  const selectedClientData = clients.find(c => c.id === selectedClient);
  const clientActivitiesForSelected = clientActivities.filter(a => a.clientId === selectedClient);
  const clientNotesForSelected = clientNotes.filter(n => n.clientId === selectedClient);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'default';
      case 'inactive': return 'secondary';
      case 'blocked': return 'destructive';
      default: return 'secondary';
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'gallery_view': return Eye;
      case 'photo_download': return Download;
      case 'photo_favorite': return Heart;
      case 'gallery_share': return Share2;
      case 'qr_scan': return QrCode;
      case 'comment': return MessageSquare;
      case 'rating': return Star;
      default: return Activity;
    }
  };

  const toggleVIP = (clientId: string) => {
    // In a real app, this would update the client's VIP status
    console.log('Toggle VIP for client:', clientId);
  };

  const addClientNote = () => {
    if (!newNote.trim() || !selectedClient) return;
    // In a real app, this would add the note to the database
    console.log('Adding note for client:', selectedClient, newNote);
    setNewNote("");
    setShowAddNote(false);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const clientStats = {
    total: clients.length,
    active: clients.filter(c => c.status === 'active').length,
    vip: clients.filter(c => c.isVIP).length,
    totalPhotos: clients.reduce((sum, c) => sum + c.totalPhotos, 0),
    totalRevenue: clients.reduce((sum, c) => sum + c.totalSpent, 0)
  };


  return (
    <StudioLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <Users className="h-8 w-8 text-blue-600" />
              Client Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Manage your clients with advanced features, notes, and activity tracking
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Clients
            </Button>
            <Button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
              <Plus className="h-4 w-4 mr-2" />
              Add Client
            </Button>
          </div>
        </div>

        {/* Client Stats */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <Users className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {clientStats.total}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Total Clients</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {clientStats.active}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Active Clients</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <Crown className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {clientStats.vip}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">VIP Clients</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-orange-600 rounded-xl flex items-center justify-center">
                  <Camera className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {clientStats.totalPhotos.toLocaleString()}
                  </p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">Total Photos</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-red-600 rounded-xl flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-red-900 dark:text-red-100">
                    ${clientStats.totalRevenue.toLocaleString()}
                  </p>
                  <p className="text-sm text-red-600 dark:text-red-400">Total Revenue</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    placeholder="Search clients by name, email, or phone..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 h-11 bg-gray-50 dark:bg-gray-700"
                  />
                </div>
              </div>
              <div className="flex flex-wrap gap-3">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="blocked">Blocked</option>
                </select>
                <select
                  value={tagFilter}
                  onChange={(e) => setTagFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">All Tags</option>
                  {availableTags.map(tag => (
                    <option key={tag} value={tag}>{tag}</option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Client List */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-600" />
              Clients ({filteredClients.length})
            </CardTitle>
            <CardDescription>
              Manage your clients with enhanced features and tracking
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredClients.map((client) => (
                <Card key={client.id} className="border border-gray-200 dark:border-gray-600 hover:shadow-lg transition-all duration-200">
                  <CardContent className="p-6">
                    <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
                      <div className="flex-1 space-y-4">
                        {/* Client Header */}
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-4">
                            <Avatar className="h-12 w-12">
                              <AvatarImage src={client.avatar} alt={client.name} />
                              <AvatarFallback className="bg-blue-100 dark:bg-blue-900/20 text-blue-600">
                                {client.name.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="flex items-center gap-2">
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                  {client.name}
                                </h3>
                                {client.isVIP && (
                                  <Crown className="h-5 w-5 text-yellow-500" />
                                )}
                                <Badge variant={getStatusColor(client.status)}>
                                  {client.status}
                                </Badge>
                              </div>
                              <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mt-1">
                                <div className="flex items-center gap-1">
                                  <Mail className="h-3 w-3" />
                                  <span>{client.email}</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Phone className="h-3 w-3" />
                                  <span>{client.phone}</span>
                                </div>
                                {client.location && (
                                  <div className="flex items-center gap-1">
                                    <MapPin className="h-3 w-3" />
                                    <span>{client.location}</span>
                                  </div>
                                )}
                              </div>
                              <div className="flex items-center gap-2 mt-2">
                                {client.tags.slice(0, 3).map(tag => (
                                  <Badge key={tag} variant="outline" className="text-xs">
                                    <Tag className="h-3 w-3 mr-1" />
                                    {tag}
                                  </Badge>
                                ))}
                                {client.tags.length > 3 && (
                                  <Badge variant="outline" className="text-xs">
                                    +{client.tags.length - 3} more
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Client Stats */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div className="flex items-center gap-2">
                            <Camera className="h-4 w-4 text-blue-600" />
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white">{client.totalPhotos}</p>
                              <p className="text-xs text-gray-500">Photos</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Download className="h-4 w-4 text-green-600" />
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white">{client.totalDownloads}</p>
                              <p className="text-xs text-gray-500">Downloads</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Heart className="h-4 w-4 text-red-600" />
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white">{client.favoritePhotos}</p>
                              <p className="text-xs text-gray-500">Favorites</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-purple-600" />
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white">{timeAgo(client.lastActivity)}</p>
                              <p className="text-xs text-gray-500">Last Active</p>
                            </div>
                          </div>
                        </div>

                        {/* Client Notes Preview */}
                        {client.notes && (
                          <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div className="flex items-center gap-2 mb-1">
                              <MessageSquare className="h-4 w-4 text-gray-600" />
                              <span className="text-sm font-medium text-gray-900 dark:text-white">Notes</span>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {client.notes.length > 100 ? `${client.notes.substring(0, 100)}...` : client.notes}
                            </p>
                          </div>
                        )}
                      </div>

                      {/* Action Buttons */}
                      <div className="flex flex-col gap-2 lg:flex-row lg:items-center">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedClient(client.id)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => toggleVIP(client.id)}
                        >
                          {client.isVIP ? (
                            <>
                              <StarOff className="h-4 w-4 mr-2" />
                              Remove VIP
                            </>
                          ) : (
                            <>
                              <Star className="h-4 w-4 mr-2" />
                              Make VIP
                            </>
                          )}
                        </Button>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Empty State */}
            {filteredClients.length === 0 && (
              <div className="text-center py-12">
                <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  No clients found
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                  Try adjusting your search or filter criteria.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </StudioLayout>
  );
}