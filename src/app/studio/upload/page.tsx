"use client";

import { useState, useCallback } from "react";
import {
  Upload,
  Camera,
  FileImage,
  X,
  Check,
  AlertTriangle,
  Users,
  Zap,
  Eye,
  Trash2,
  RotateCcw,
  Settings,
  Maximize,
  Minimize,
  RotateCw,
  FlipHorizontal,
  FlipVertical,
  Filter,
  Download,
  Share2,
  Tag,
  Clock,
  CheckCircle,
  XCircle,
  Loader,
  Image as ImageIcon,
  Grid,
  List,
  Search,
  SortAsc,
  MoreHorizontal,
  Star,
  Heart,
  MessageSquare,
  Sliders,
  Layers
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import StudioLayout from "@/components/studio/StudioLayout";
import { formatFileSize, cn } from "@/lib/utils";

interface UploadFile {
  id: string;
  file: File;
  preview: string;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  faceMatches?: number;
  clientMatches?: string[];
  error?: string;
  thumbnail?: string;
  originalSize: number;
  compressedSize?: number;
  dimensions?: { width: number; height: number };
  metadata?: {
    camera?: string;
    lens?: string;
    settings?: string;
    location?: string;
    timestamp?: string;
  };
  tags?: string[];
  isSelected?: boolean;
  editHistory?: EditAction[];
}

interface EditAction {
  id: string;
  type: 'resize' | 'crop' | 'rotate' | 'filter' | 'brightness' | 'contrast';
  params: any;
  timestamp: string;
}

interface BatchOperation {
  id: string;
  type: 'resize' | 'watermark' | 'rename' | 'tag' | 'compress';
  params: any;
  fileIds: string[];
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
}

export default function UploadPage() {
  const [files, setFiles] = useState<UploadFile[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [eventName, setEventName] = useState("");
  const [eventDate, setEventDate] = useState("");
  const [eventType, setEventType] = useState("wedding");
  const [autoResize, setAutoResize] = useState(true);
  const [enableFaceDetection, setEnableFaceDetection] = useState(true);
  const [isUploading, setIsUploading] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [showBatchOperations, setShowBatchOperations] = useState(false);
  const [compressionQuality, setCompressionQuality] = useState(80);
  const [maxWidth, setMaxWidth] = useState(1920);
  const [maxHeight, setMaxHeight] = useState(1080);
  const [watermarkEnabled, setWatermarkEnabled] = useState(false);
  const [watermarkText, setWatermarkText] = useState("© Studio Name");
  const [autoTagging, setAutoTagging] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");

  // Helper functions
  const toggleFileSelection = (fileId: string) => {
    setSelectedFiles(prev =>
      prev.includes(fileId)
        ? prev.filter(id => id !== fileId)
        : [...prev, fileId]
    );
  };

  const selectAllFiles = () => {
    setSelectedFiles(files.map(f => f.id));
  };

  const clearSelection = () => {
    setSelectedFiles([]);
  };

  const deleteSelectedFiles = () => {
    setFiles(prev => prev.filter(f => !selectedFiles.includes(f.id)));
    setSelectedFiles([]);
  };

  const filteredFiles = files.filter(file => {
    const matchesSearch = file.file.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === "all" || file.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  }, []);

  const handleFiles = (fileList: FileList) => {
    const newFiles: UploadFile[] = [];

    Array.from(fileList).forEach((file) => {
      if (file.type.startsWith('image/')) {
        const id = Math.random().toString(36).substr(2, 9);
        const preview = URL.createObjectURL(file);

        newFiles.push({
          id,
          file,
          preview,
          status: 'pending',
          progress: 0,
          originalSize: file.size,
          dimensions: { width: 0, height: 0 }, // Will be populated after image load
          tags: [],
          isSelected: false
        });
      }
    });

    setFiles(prev => [...prev, ...newFiles]);
  };

  const removeFile = (id: string) => {
    setFiles(prev => {
      const file = prev.find(f => f.id === id);
      if (file) {
        URL.revokeObjectURL(file.preview);
      }
      return prev.filter(f => f.id !== id);
    });
  };

  const startUpload = async () => {
    if (files.length === 0) return;

    setIsUploading(true);

    // Simulate upload process
    for (const file of files) {
      setFiles(prev => prev.map(f =>
        f.id === file.id ? { ...f, status: 'uploading' } : f
      ));

      // Simulate upload progress
      for (let progress = 0; progress <= 100; progress += 10) {
        await new Promise(resolve => setTimeout(resolve, 100));
        setFiles(prev => prev.map(f =>
          f.id === file.id ? { ...f, progress } : f
        ));
      }

      // Simulate face detection
      if (enableFaceDetection) {
        setFiles(prev => prev.map(f =>
          f.id === file.id ? { ...f, status: 'processing' } : f
        ));

        await new Promise(resolve => setTimeout(resolve, 1000));

        const faceMatches = Math.floor(Math.random() * 5) + 1;
        const clientMatches = ['Sarah Johnson', 'Mike Davis'].slice(0, Math.floor(Math.random() * 2) + 1);

        setFiles(prev => prev.map(f =>
          f.id === file.id ? {
            ...f,
            status: 'completed',
            faceMatches,
            clientMatches
          } : f
        ));
      } else {
        setFiles(prev => prev.map(f =>
          f.id === file.id ? { ...f, status: 'completed' } : f
        ));
      }
    }

    setIsUploading(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return FileImage;
      case 'uploading': return Upload;
      case 'processing': return Zap;
      case 'completed': return Check;
      case 'error': return AlertTriangle;
      default: return FileImage;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-gray-500';
      case 'uploading': return 'text-blue-500';
      case 'processing': return 'text-purple-500';
      case 'completed': return 'text-green-500';
      case 'error': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const totalFiles = files.length;
  const completedFiles = files.filter(f => f.status === 'completed').length;
  const totalSize = files.reduce((sum, f) => sum + f.file.size, 0);

  return (
    <StudioLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <Upload className="h-8 w-8 text-blue-600" />
              Upload Photos
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Upload and process photos with automatic face detection and client matching
            </p>
          </div>
          <div className="flex items-center gap-3">
            {totalFiles > 0 && (
              <Badge variant="secondary" className="text-sm">
                {completedFiles}/{totalFiles} Processed
              </Badge>
            )}
          </div>
        </div>

        {/* Upload Settings */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5 text-blue-600" />
              Upload Configuration
            </CardTitle>
            <CardDescription>
              Configure event details, processing settings, and advanced options
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Event Name
                </label>
                <Input
                  placeholder="e.g., Sarah & Mike Wedding"
                  value={eventName}
                  onChange={(e) => setEventName(e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Event Date
                </label>
                <Input
                  type="date"
                  value={eventDate}
                  onChange={(e) => setEventDate(e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Event Type
                </label>
                <select
                  value={eventType}
                  onChange={(e) => setEventType(e.target.value)}
                  className="w-full h-11 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="wedding">Wedding</option>
                  <option value="birthday">Birthday</option>
                  <option value="engagement">Engagement</option>
                  <option value="corporate">Corporate</option>
                  <option value="family">Family</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>

            {/* Advanced Settings */}
            <div className="border-t pt-4">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">Processing Options</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={enableFaceDetection}
                      onChange={(e) => setEnableFaceDetection(e.target.checked)}
                      className="w-4 h-4 text-blue-600 rounded"
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Enable Face Detection & Matching
                    </span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={autoResize}
                      onChange={(e) => setAutoResize(e.target.checked)}
                      className="w-4 h-4 text-blue-600 rounded"
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Auto-resize Images
                    </span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={autoTagging}
                      onChange={(e) => setAutoTagging(e.target.checked)}
                      className="w-4 h-4 text-blue-600 rounded"
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Auto-tag with AI
                    </span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={watermarkEnabled}
                      onChange={(e) => setWatermarkEnabled(e.target.checked)}
                      className="w-4 h-4 text-blue-600 rounded"
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Add Watermark
                    </span>
                  </label>
                </div>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Compression Quality ({compressionQuality}%)
                    </label>
                    <input
                      type="range"
                      min="10"
                      max="100"
                      value={compressionQuality}
                      onChange={(e) => setCompressionQuality(Number(e.target.value))}
                      className="w-full"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Max Width
                      </label>
                      <Input
                        type="number"
                        value={maxWidth}
                        onChange={(e) => setMaxWidth(Number(e.target.value))}
                        className="h-9"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Max Height
                      </label>
                      <Input
                        type="number"
                        value={maxHeight}
                        onChange={(e) => setMaxHeight(Number(e.target.value))}
                        className="h-9"
                      />
                    </div>
                  </div>
                  {watermarkEnabled && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Watermark Text
                      </label>
                      <Input
                        value={watermarkText}
                        onChange={(e) => setWatermarkText(e.target.value)}
                        className="h-9"
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Upload Area */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardContent className="p-6">
            <div
              className={cn(
                "border-2 border-dashed rounded-lg p-8 text-center transition-colors",
                dragActive
                  ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                  : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
              )}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Drop photos here or click to browse
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Support for JPG, PNG, and RAW files up to 50MB each
              </p>
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={(e) => e.target.files && handleFiles(e.target.files)}
                className="hidden"
                id="file-upload"
              />
              <label htmlFor="file-upload">
                <Button className="cursor-pointer">
                  <FileImage className="h-4 w-4 mr-2" />
                  Choose Files
                </Button>
              </label>
            </div>
          </CardContent>
        </Card>

        {/* Upload Progress */}
        {files.length > 0 && (
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <FileImage className="h-5 w-5 text-green-600" />
                    Upload Queue ({filteredFiles.length}/{files.length} files)
                  </CardTitle>
                  <CardDescription>
                    Total size: {formatFileSize(totalSize)} • {selectedFiles.length} selected
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                  >
                    {viewMode === 'grid' ? (
                      <List className="h-4 w-4" />
                    ) : (
                      <Grid className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setFiles([])}
                    disabled={isUploading}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Clear All
                  </Button>
                  <Button
                    onClick={startUpload}
                    disabled={isUploading || files.length === 0}
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
                  >
                    {isUploading ? (
                      <>
                        <RotateCcw className="h-4 w-4 mr-2 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4 mr-2" />
                        Start Upload
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* Search and Filter Controls */}
              <div className="flex flex-col lg:flex-row gap-4 mb-6">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search files..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-9 h-9"
                    />
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                    className="px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                  >
                    <option value="all">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="uploading">Uploading</option>
                    <option value="processing">Processing</option>
                    <option value="completed">Completed</option>
                    <option value="error">Error</option>
                  </select>
                  {selectedFiles.length > 0 && (
                    <>
                      <Button variant="outline" size="sm" onClick={selectAllFiles}>
                        Select All
                      </Button>
                      <Button variant="outline" size="sm" onClick={clearSelection}>
                        Clear Selection
                      </Button>
                      <Button variant="destructive" size="sm" onClick={deleteSelectedFiles}>
                        Delete Selected
                      </Button>
                    </>
                  )}
                </div>
              </div>

              <div className="space-y-4">
                {filteredFiles.map((file) => {
                  const StatusIcon = getStatusIcon(file.status);
                  return (
                    <div key={file.id} className={cn(
                      "flex items-center gap-4 p-4 rounded-lg border-2 transition-all duration-200",
                      selectedFiles.includes(file.id)
                        ? "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800"
                        : "bg-gray-50 dark:bg-gray-700 border-transparent hover:border-gray-200 dark:hover:border-gray-600"
                    )}>
                      <input
                        type="checkbox"
                        checked={selectedFiles.includes(file.id)}
                        onChange={() => toggleFileSelection(file.id)}
                        className="w-4 h-4 text-blue-600 rounded"
                      />
                      <div className="relative">
                        <img
                          src={file.preview}
                          alt={file.file.name}
                          className="w-16 h-16 object-cover rounded-lg"
                        />
                        {file.faceMatches && file.faceMatches > 0 && (
                          <div className="absolute -top-2 -right-2 bg-green-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                            {file.faceMatches}
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-2">
                          <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {file.file.name}
                          </p>
                          <div className="flex items-center gap-2">
                            <StatusIcon className={cn("h-4 w-4", getStatusColor(file.status))} />
                            <span className={cn("text-xs font-medium", getStatusColor(file.status))}>
                              {file.status}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-2">
                          <span>{formatFileSize(file.file.size)}</span>
                          {file.status === 'uploading' && (
                            <span>{file.progress}%</span>
                          )}
                        </div>
                        {(file.status === 'uploading' || file.status === 'processing') && (
                          <Progress value={file.progress} className="h-2" />
                        )}
                        {file.status === 'completed' && file.faceMatches && (
                          <div className="flex items-center gap-4 mt-2">
                            <Badge variant="success" className="text-xs">
                              <Eye className="h-3 w-3 mr-1" />
                              {file.faceMatches} faces detected
                            </Badge>
                            {file.clientMatches && file.clientMatches.length > 0 && (
                              <Badge variant="secondary" className="text-xs">
                                <Users className="h-3 w-3 mr-1" />
                                Matched: {file.clientMatches.join(', ')}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(file.id)}
                        disabled={isUploading}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </StudioLayout>
  );
}