"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>ard,
  Crown,
  Check,
  X,
  Calendar,
  Download,
  Receipt,
  <PERSON>ertTriangle,
  Zap,
  Shield,
  Users,
  HardDrive,
  Camera,
  Star,
  TrendingUp,
  Gift,
  Bell,
  Settings,
  Plus,
  Edit,
  Trash2,
  RefreshCw,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  ArrowUpRight,
  ArrowDownRight,
  Target,
  Lightbulb,
  MessageSquare,
  Send,
  FileText,
  Calculator
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import StudioLayout from "@/components/studio/StudioLayout";
import { formatDate, formatCurrency, cn } from "@/lib/utils";

interface Plan {
  id: string;
  name: string;
  price: number;
  interval: 'month' | 'year';
  features: string[];
  limits: {
    storage: string;
    clients: string;
    photos: string;
    bandwidth: string;
  };
  popular?: boolean;
  current?: boolean;
}

interface Invoice {
  id: string;
  date: string;
  amount: number;
  status: 'paid' | 'pending' | 'failed';
  description: string;
  downloadUrl: string;
}

interface Usage {
  storage: { used: number; limit: number };
  clients: { used: number; limit: number };
  photos: { used: number; limit: number };
  bandwidth: { used: number; limit: number };
}

interface PaymentMethod {
  id: string;
  type: 'card' | 'bank' | 'paypal';
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
  email?: string; // for PayPal
}

interface UsageAlert {
  id: string;
  type: 'storage' | 'bandwidth' | 'clients' | 'photos';
  threshold: number;
  currentUsage: number;
  isActive: boolean;
  notificationSent: boolean;
}

interface PlanSuggestion {
  id: string;
  reason: string;
  currentPlan: string;
  suggestedPlan: string;
  potentialSavings?: number;
  benefits: string[];
  urgency: 'low' | 'medium' | 'high';
}

interface CustomPlanRequest {
  id: string;
  requirements: {
    storage: string;
    clients: string;
    photos: string;
    bandwidth: string;
    additionalFeatures: string[];
  };
  businessInfo: {
    studioName: string;
    monthlyRevenue: string;
    teamSize: number;
    specialNeeds: string;
  };
  status: 'pending' | 'reviewing' | 'approved' | 'rejected';
  submittedAt: string;
  estimatedPrice?: number;
}

export default function BillingPage() {
  const [billingInterval, setBillingInterval] = useState<'month' | 'year'>('month');
  const [activeTab, setActiveTab] = useState('overview');
  const [showCustomPlanForm, setShowCustomPlanForm] = useState(false);
  const [showPaymentMethodForm, setShowPaymentMethodForm] = useState(false);
  
  const [plans] = useState<Plan[]>([
    {
      id: 'starter',
      name: 'Starter',
      price: billingInterval === 'month' ? 29 : 290,
      interval: billingInterval,
      features: [
        'Up to 10 clients',
        '50GB storage',
        '1,000 photos/month',
        'Basic face recognition',
        'Email support',
        'Mobile app access'
      ],
      limits: {
        storage: '50GB',
        clients: '10',
        photos: '1,000/month',
        bandwidth: '100GB/month'
      }
    },
    {
      id: 'professional',
      name: 'Professional',
      price: billingInterval === 'month' ? 79 : 790,
      interval: billingInterval,
      features: [
        'Up to 50 clients',
        '200GB storage',
        '5,000 photos/month',
        'Advanced face recognition',
        'Priority support',
        'Custom branding',
        'Analytics dashboard',
        'Bulk operations'
      ],
      limits: {
        storage: '200GB',
        clients: '50',
        photos: '5,000/month',
        bandwidth: '500GB/month'
      },
      popular: true,
      current: true
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      price: billingInterval === 'month' ? 199 : 1990,
      interval: billingInterval,
      features: [
        'Unlimited clients',
        '1TB storage',
        'Unlimited photos',
        'AI-powered recognition',
        '24/7 phone support',
        'White-label solution',
        'Advanced analytics',
        'API access',
        'Custom integrations',
        'Dedicated account manager'
      ],
      limits: {
        storage: '1TB',
        clients: 'Unlimited',
        photos: 'Unlimited',
        bandwidth: '2TB/month'
      }
    }
  ]);

  const [invoices] = useState<Invoice[]>([
    {
      id: 'INV-2024-001',
      date: '2024-01-15',
      amount: 79,
      status: 'paid',
      description: 'Professional Plan - January 2024',
      downloadUrl: '/invoices/INV-2024-001.pdf'
    },
    {
      id: 'INV-2023-012',
      date: '2023-12-15',
      amount: 79,
      status: 'paid',
      description: 'Professional Plan - December 2023',
      downloadUrl: '/invoices/INV-2023-012.pdf'
    },
    {
      id: 'INV-2023-011',
      date: '2023-11-15',
      amount: 79,
      status: 'paid',
      description: 'Professional Plan - November 2023',
      downloadUrl: '/invoices/INV-2023-011.pdf'
    }
  ]);

  const [usage] = useState<Usage>({
    storage: { used: 145, limit: 200 }, // GB
    clients: { used: 32, limit: 50 },
    photos: { used: 3240, limit: 5000 },
    bandwidth: { used: 280, limit: 500 } // GB
  });

  const [paymentMethods] = useState<PaymentMethod[]>([
    {
      id: '1',
      type: 'card',
      last4: '4242',
      brand: 'Visa',
      expiryMonth: 12,
      expiryYear: 2025,
      isDefault: true
    },
    {
      id: '2',
      type: 'card',
      last4: '5555',
      brand: 'Mastercard',
      expiryMonth: 8,
      expiryYear: 2026,
      isDefault: false
    }
  ]);

  const [usageAlerts] = useState<UsageAlert[]>([
    {
      id: '1',
      type: 'storage',
      threshold: 80,
      currentUsage: 72.5,
      isActive: true,
      notificationSent: false
    },
    {
      id: '2',
      type: 'clients',
      threshold: 90,
      currentUsage: 64,
      isActive: true,
      notificationSent: false
    }
  ]);

  const [planSuggestions] = useState<PlanSuggestion[]>([
    {
      id: '1',
      reason: 'You\'re approaching your storage limit',
      currentPlan: 'Professional',
      suggestedPlan: 'Enterprise',
      potentialSavings: 0,
      benefits: ['Unlimited storage', 'Priority support', 'Advanced analytics'],
      urgency: 'medium'
    },
    {
      id: '2',
      reason: 'Annual billing could save you money',
      currentPlan: 'Professional (Monthly)',
      suggestedPlan: 'Professional (Annual)',
      potentialSavings: 158,
      benefits: ['2 months free', 'Same features', 'Better cash flow'],
      urgency: 'low'
    }
  ]);

  const [customPlanRequests] = useState<CustomPlanRequest[]>([
    {
      id: '1',
      requirements: {
        storage: '1TB',
        clients: '200',
        photos: '50000',
        bandwidth: '5TB',
        additionalFeatures: ['White-label', 'API access', 'Custom domain']
      },
      businessInfo: {
        studioName: 'Elite Photography Studio',
        monthlyRevenue: '$10,000-$25,000',
        teamSize: 8,
        specialNeeds: 'Need integration with existing CRM system'
      },
      status: 'reviewing',
      submittedAt: '2024-01-10T10:00:00Z',
      estimatedPrice: 299
    }
  ]);

  const currentPlan = plans.find(plan => plan.current);
  const nextBillingDate = '2024-02-15';
  const savings = billingInterval === 'year' ? 20 : 0;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'success';
      case 'pending': return 'warning';
      case 'failed': return 'destructive';
      default: return 'secondary';
    }
  };

  const getUsagePercentage = (used: number, limit: number) => {
    return Math.min((used / limit) * 100, 100);
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'destructive';
    if (percentage >= 75) return 'warning';
    return 'default';
  };

  return (
    <StudioLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <CreditCard className="h-8 w-8 text-blue-600" />
              Billing & Subscription
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Manage your subscription, payments, and get personalized plan recommendations
            </p>
          </div>
          <div className="flex items-center gap-3">
            {currentPlan && (
              <Badge variant="secondary" className="text-sm">
                {currentPlan.name} Plan
              </Badge>
            )}
            <Button
              variant="outline"
              onClick={() => setShowCustomPlanForm(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Request Custom Plan
            </Button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'overview', name: 'Overview', icon: TrendingUp },
              { id: 'plans', name: 'Plans & Suggestions', icon: Lightbulb },
              { id: 'payment', name: 'Payment Methods', icon: CreditCard },
              { id: 'invoices', name: 'Invoices', icon: Receipt },
              { id: 'alerts', name: 'Usage Alerts', icon: Bell }
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={cn(
                    "flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors",
                    activeTab === tab.id
                      ? "border-blue-500 text-blue-600 dark:text-blue-400"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                  )}
                >
                  <Icon className="h-4 w-4" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Current Plan & Usage */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Current Plan */}
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 lg:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Crown className="h-5 w-5 text-blue-600" />
                    Current Plan: {currentPlan?.name}
                  </CardTitle>
                  <CardDescription>
                    Next billing date: {formatDate(nextBillingDate)}
                  </CardDescription>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {formatCurrency(currentPlan?.price || 0)}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">
                    per {currentPlan?.interval}
                  </p>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg">
                  <HardDrive className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {currentPlan?.limits.storage}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">Storage</p>
                </div>
                <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg">
                  <Users className="h-6 w-6 text-green-600 mx-auto mb-2" />
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {currentPlan?.limits.clients}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">Clients</p>
                </div>
                <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg">
                  <Camera className="h-6 w-6 text-purple-600 mx-auto mb-2" />
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {currentPlan?.limits.photos}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">Photos</p>
                </div>
                <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-orange-600 mx-auto mb-2" />
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {currentPlan?.limits.bandwidth}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">Bandwidth</p>
                </div>
              </div>
              <div className="flex items-center gap-2 mt-4">
                <Button variant="outline" size="sm">
                  <Calendar className="h-4 w-4 mr-2" />
                  Change Plan
                </Button>
                <Button variant="outline" size="sm">
                  <X className="h-4 w-4 mr-2" />
                  Cancel Subscription
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Usage Overview */}
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-green-600" />
                Usage Overview
              </CardTitle>
              <CardDescription>
                Current month usage
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Storage</span>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {usage.storage.used}GB / {usage.storage.limit}GB
                  </span>
                </div>
                <Progress 
                  value={getUsagePercentage(usage.storage.used, usage.storage.limit)} 
                  className="h-2"
                  variant={getUsageColor(getUsagePercentage(usage.storage.used, usage.storage.limit))}
                />
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Clients</span>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {usage.clients.used} / {usage.clients.limit}
                  </span>
                </div>
                <Progress 
                  value={getUsagePercentage(usage.clients.used, usage.clients.limit)} 
                  className="h-2"
                  variant={getUsageColor(getUsagePercentage(usage.clients.used, usage.clients.limit))}
                />
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Photos</span>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {usage.photos.used.toLocaleString()} / {usage.photos.limit.toLocaleString()}
                  </span>
                </div>
                <Progress 
                  value={getUsagePercentage(usage.photos.used, usage.photos.limit)} 
                  className="h-2"
                  variant={getUsageColor(getUsagePercentage(usage.photos.used, usage.photos.limit))}
                />
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Bandwidth</span>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {usage.bandwidth.used}GB / {usage.bandwidth.limit}GB
                  </span>
                </div>
                <Progress 
                  value={getUsagePercentage(usage.bandwidth.used, usage.bandwidth.limit)} 
                  className="h-2"
                  variant={getUsageColor(getUsagePercentage(usage.bandwidth.used, usage.bandwidth.limit))}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Pricing Plans */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Star className="h-5 w-5 text-yellow-600" />
                  Choose Your Plan
                </CardTitle>
                <CardDescription>
                  Upgrade or downgrade your subscription anytime
                </CardDescription>
              </div>
              <div className="flex items-center gap-2 p-1 bg-gray-100 dark:bg-gray-700 rounded-lg">
                <Button
                  variant={billingInterval === 'month' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setBillingInterval('month')}
                >
                  Monthly
                </Button>
                <Button
                  variant={billingInterval === 'year' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setBillingInterval('year')}
                >
                  Yearly
                  {savings > 0 && (
                    <Badge variant="success" className="ml-2 text-xs">
                      Save {savings}%
                    </Badge>
                  )}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {plans.map((plan) => (
                <Card 
                  key={plan.id} 
                  className={cn(
                    "relative border-2 transition-all duration-200",
                    plan.popular 
                      ? "border-blue-500 shadow-lg scale-105" 
                      : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500",
                    plan.current && "ring-2 ring-green-500 ring-offset-2"
                  )}
                >
                  {plan.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-blue-600 text-white">
                        Most Popular
                      </Badge>
                    </div>
                  )}
                  {plan.current && (
                    <div className="absolute -top-3 right-3">
                      <Badge variant="success">
                        Current Plan
                      </Badge>
                    </div>
                  )}
                  <CardHeader className="text-center">
                    <CardTitle className="text-xl">{plan.name}</CardTitle>
                    <div className="mt-4">
                      <span className="text-4xl font-bold text-gray-900 dark:text-white">
                        {formatCurrency(plan.price)}
                      </span>
                      <span className="text-gray-600 dark:text-gray-400">
                        /{plan.interval}
                      </span>
                    </div>
                    {billingInterval === 'year' && (
                      <p className="text-sm text-green-600 dark:text-green-400">
                        Save {formatCurrency((plan.price * 12) - (plan.price * 10))} per year
                      </p>
                    )}
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3 mb-6">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-center gap-2">
                          <Check className="h-4 w-4 text-green-600 flex-shrink-0" />
                          <span className="text-sm text-gray-700 dark:text-gray-300">
                            {feature}
                          </span>
                        </li>
                      ))}
                    </ul>
                    <Button 
                      className={cn(
                        "w-full",
                        plan.current 
                          ? "bg-gray-600 hover:bg-gray-700" 
                          : plan.popular 
                            ? "bg-blue-600 hover:bg-blue-700" 
                            : ""
                      )}
                      disabled={plan.current}
                    >
                      {plan.current ? 'Current Plan' : 'Choose Plan'}
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Billing History */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Receipt className="h-5 w-5 text-purple-600" />
              Billing History
            </CardTitle>
            <CardDescription>
              Download invoices and view payment history
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {invoices.map((invoice) => (
                <div key={invoice.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className={cn(
                      "h-10 w-10 rounded-lg flex items-center justify-center",
                      invoice.status === 'paid' ? 'bg-green-100 dark:bg-green-900/20' :
                      invoice.status === 'pending' ? 'bg-yellow-100 dark:bg-yellow-900/20' :
                      'bg-red-100 dark:bg-red-900/20'
                    )}>
                      <Receipt className={cn(
                        "h-5 w-5",
                        invoice.status === 'paid' ? 'text-green-600' :
                        invoice.status === 'pending' ? 'text-yellow-600' :
                        'text-red-600'
                      )} />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {invoice.id}
                      </p>
                      <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                        <span>{formatDate(invoice.date)}</span>
                        <span>•</span>
                        <span>{invoice.description}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <p className="font-bold text-gray-900 dark:text-white">
                        {formatCurrency(invoice.amount)}
                      </p>
                      <Badge variant={getStatusColor(invoice.status) as any} className="text-xs">
                        {invoice.status}
                      </Badge>
                    </div>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Payment Method */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-green-600" />
              Payment Method
            </CardTitle>
            <CardDescription>
              Manage your payment information
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex items-center gap-4">
                <div className="h-10 w-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <CreditCard className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">
                    •••• •••• •••• 4242
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Expires 12/2025 • Visa
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  Update
                </Button>
                <Button variant="outline" size="sm">
                  Remove
                </Button>
              </div>
            </div>
            <div className="mt-4">
              <Button variant="outline">
                <CreditCard className="h-4 w-4 mr-2" />
                Add Payment Method
              </Button>
            </div>
          </CardContent>
        </Card>
          </div>
        )}

        {/* Plans & Suggestions Tab */}
        {activeTab === 'plans' && (
          <div className="space-y-6">
            {/* Plan Suggestions */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="h-5 w-5 text-yellow-600" />
                  Personalized Plan Suggestions
                </CardTitle>
                <CardDescription>
                  Based on your usage patterns, we recommend these optimizations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {planSuggestions.map((suggestion) => (
                    <div key={suggestion.id} className={cn(
                      "p-4 rounded-lg border-l-4",
                      suggestion.urgency === 'high' ? 'bg-red-50 dark:bg-red-900/20 border-red-500' :
                      suggestion.urgency === 'medium' ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-500' :
                      'bg-blue-50 dark:bg-blue-900/20 border-blue-500'
                    )}>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                            {suggestion.reason}
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                            Switch from <strong>{suggestion.currentPlan}</strong> to <strong>{suggestion.suggestedPlan}</strong>
                          </p>
                          <div className="flex flex-wrap gap-2 mb-3">
                            {suggestion.benefits.map((benefit, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                <Check className="h-3 w-3 mr-1" />
                                {benefit}
                              </Badge>
                            ))}
                          </div>
                          {suggestion.potentialSavings && suggestion.potentialSavings > 0 && (
                            <p className="text-sm font-medium text-green-600 dark:text-green-400">
                              💰 Save ${suggestion.potentialSavings}/year
                            </p>
                          )}
                        </div>
                        <div className="flex items-center gap-2 ml-4">
                          <Button variant="outline" size="sm">
                            Learn More
                          </Button>
                          <Button size="sm" className="bg-gradient-to-r from-blue-600 to-blue-700">
                            Upgrade Now
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Available Plans */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Crown className="h-5 w-5 text-purple-600" />
                      Available Plans
                    </CardTitle>
                    <CardDescription>
                      Choose the perfect plan for your studio
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Billing:</span>
                    <div className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                      <button
                        onClick={() => setBillingInterval('month')}
                        className={cn(
                          "px-3 py-1 text-sm font-medium rounded-md transition-colors",
                          billingInterval === 'month'
                            ? "bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm"
                            : "text-gray-600 dark:text-gray-400"
                        )}
                      >
                        Monthly
                      </button>
                      <button
                        onClick={() => setBillingInterval('year')}
                        className={cn(
                          "px-3 py-1 text-sm font-medium rounded-md transition-colors",
                          billingInterval === 'year'
                            ? "bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm"
                            : "text-gray-600 dark:text-gray-400"
                        )}
                      >
                        Annual
                        <Badge variant="secondary" className="ml-2 text-xs">
                          Save {savings}%
                        </Badge>
                      </button>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {plans.map((plan) => (
                    <div key={plan.id} className={cn(
                      "relative p-6 rounded-xl border-2 transition-all duration-200",
                      plan.current
                        ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                        : plan.popular
                        ? "border-purple-500 bg-purple-50 dark:bg-purple-900/20"
                        : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                    )}>
                      {plan.popular && (
                        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                          <Badge className="bg-purple-600 text-white">
                            <Star className="h-3 w-3 mr-1" />
                            Most Popular
                          </Badge>
                        </div>
                      )}
                      {plan.current && (
                        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                          <Badge className="bg-blue-600 text-white">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Current Plan
                          </Badge>
                        </div>
                      )}
                      <div className="text-center mb-6">
                        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                          {plan.name}
                        </h3>
                        <div className="flex items-baseline justify-center gap-1">
                          <span className="text-3xl font-bold text-gray-900 dark:text-white">
                            ${plan.price}
                          </span>
                          <span className="text-gray-600 dark:text-gray-400">
                            /{plan.interval}
                          </span>
                        </div>
                      </div>
                      <ul className="space-y-3 mb-6">
                        {plan.features.map((feature, index) => (
                          <li key={index} className="flex items-center gap-2 text-sm">
                            <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
                            <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                          </li>
                        ))}
                      </ul>
                      <Button
                        className={cn(
                          "w-full",
                          plan.current
                            ? "bg-gray-600 hover:bg-gray-700"
                            : "bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
                        )}
                        disabled={plan.current}
                      >
                        {plan.current ? 'Current Plan' : 'Upgrade Now'}
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Payment Methods Tab */}
        {activeTab === 'payment' && (
          <div className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <CreditCard className="h-5 w-5 text-green-600" />
                      Payment Methods
                    </CardTitle>
                    <CardDescription>
                      Manage your payment methods and billing information
                    </CardDescription>
                  </div>
                  <Button onClick={() => setShowPaymentMethodForm(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Payment Method
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {paymentMethods.map((method) => (
                    <div key={method.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="h-10 w-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                          <CreditCard className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <p className="font-medium text-gray-900 dark:text-white">
                              •••• •••• •••• {method.last4}
                            </p>
                            {method.isDefault && (
                              <Badge variant="secondary" className="text-xs">Default</Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            Expires {method.expiryMonth}/{method.expiryYear} • {method.brand}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {!method.isDefault && (
                          <Button variant="outline" size="sm">
                            Set Default
                          </Button>
                        )}
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </Button>
                        <Button variant="outline" size="sm">
                          <Trash2 className="h-4 w-4 mr-2" />
                          Remove
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Invoices Tab */}
        {activeTab === 'invoices' && (
          <div className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Receipt className="h-5 w-5 text-blue-600" />
                  Billing History
                </CardTitle>
                <CardDescription>
                  Download invoices and view payment history
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {invoices.map((invoice) => (
                    <div key={invoice.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="h-10 w-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                          <Receipt className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {invoice.description}
                          </p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {formatDate(invoice.date)} • Invoice #{invoice.id}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <p className="font-bold text-gray-900 dark:text-white">
                            {formatCurrency(invoice.amount)}
                          </p>
                          <Badge variant={getStatusColor(invoice.status) as any} className="text-xs">
                            {invoice.status}
                          </Badge>
                        </div>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Usage Alerts Tab */}
        {activeTab === 'alerts' && (
          <div className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5 text-orange-600" />
                  Usage Alerts & Notifications
                </CardTitle>
                <CardDescription>
                  Set up alerts to monitor your usage and avoid overages
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Current Alerts */}
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-4">Active Alerts</h4>
                    <div className="space-y-3">
                      {usageAlerts.map((alert) => (
                        <div key={alert.id} className="flex items-center justify-between p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
                          <div className="flex items-center gap-3">
                            <AlertTriangle className="h-5 w-5 text-orange-600" />
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white">
                                {alert.type.charAt(0).toUpperCase() + alert.type.slice(1)} Alert
                              </p>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                Alert when usage exceeds {alert.threshold}% (currently at {alert.currentUsage.toFixed(1)}%)
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm">
                              <Settings className="h-4 w-4 mr-2" />
                              Configure
                            </Button>
                            <Button variant="outline" size="sm">
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Add New Alert */}
                  <div className="border-t pt-6">
                    <h4 className="font-medium text-gray-900 dark:text-white mb-4">Create New Alert</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Resource Type
                        </label>
                        <select className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                          <option value="storage">Storage</option>
                          <option value="bandwidth">Bandwidth</option>
                          <option value="clients">Clients</option>
                          <option value="photos">Photos</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Threshold (%)
                        </label>
                        <input
                          type="number"
                          min="50"
                          max="95"
                          defaultValue="80"
                          className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        />
                      </div>
                      <div className="flex items-end">
                        <Button className="w-full">
                          <Plus className="h-4 w-4 mr-2" />
                          Create Alert
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </StudioLayout>
  );
}
