"use client";

import { useState } from "react";
import { 
  Palette,
  Upload,
  Eye,
  Save,
  RefreshCw,
  Settings,
  Globe,
  Link,
  Image,
  Type,
  Layout,
  Code,
  Monitor,
  Smartphone,
  Tablet,
  Download,
  Share2,
  Copy,
  Edit,
  Trash2,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info,
  Star,
  Heart,
  Camera,
  Users,
  Calendar,
  Clock,
  Mail,
  Phone,
  MapPin,
  Zap,
  Activity,
  TrendingUp,
  BarChart3,
  Target,
  Lightbulb,
  Layers,
  Brush,
  Paintbrush,
  ColorWheel
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Textarea } from "@/components/ui/textarea";
import StudioLayout from "@/components/studio/StudioLayout";
import { formatDate, cn } from "@/lib/utils";

interface BrandingSettings {
  id: string;
  studioName: string;
  logo: {
    primary: string;
    secondary?: string;
    favicon: string;
  };
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
  typography: {
    headingFont: string;
    bodyFont: string;
    fontSize: 'small' | 'medium' | 'large';
  };
  domain: {
    customDomain?: string;
    subdomain: string;
    isCustomDomainActive: boolean;
  };
  socialMedia: {
    website?: string;
    facebook?: string;
    instagram?: string;
    twitter?: string;
    linkedin?: string;
  };
  contactInfo: {
    email: string;
    phone?: string;
    address?: string;
  };
  customCSS?: string;
  emailBranding: {
    headerImage?: string;
    footerText: string;
    socialLinks: boolean;
  };
  galleryBranding: {
    watermarkEnabled: boolean;
    watermarkText?: string;
    watermarkImage?: string;
    showStudioInfo: boolean;
  };
  isActive: boolean;
  lastUpdated: string;
}

interface BrandingTemplate {
  id: string;
  name: string;
  category: 'modern' | 'classic' | 'minimal' | 'creative' | 'professional';
  preview: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
  };
  typography: {
    headingFont: string;
    bodyFont: string;
  };
  isPopular: boolean;
  usageCount: number;
  description: string;
}

interface CustomizationOption {
  id: string;
  name: string;
  type: 'color' | 'font' | 'layout' | 'component';
  category: string;
  description: string;
  isEnabled: boolean;
  isPremium: boolean;
}

export default function BrandingPage() {
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [previewDevice, setPreviewDevice] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');

  const [brandingSettings] = useState<BrandingSettings>({
    id: '1',
    studioName: 'Elite Photography Studio',
    logo: {
      primary: '/api/placeholder/200/60',
      secondary: '/api/placeholder/100/30',
      favicon: '/api/placeholder/32/32'
    },
    colors: {
      primary: '#3B82F6',
      secondary: '#8B5CF6',
      accent: '#10B981',
      background: '#FFFFFF',
      text: '#1F2937'
    },
    typography: {
      headingFont: 'Inter',
      bodyFont: 'Inter',
      fontSize: 'medium'
    },
    domain: {
      customDomain: 'elitephotography.com',
      subdomain: 'elite-photography',
      isCustomDomainActive: true
    },
    socialMedia: {
      website: 'https://elitephotography.com',
      facebook: 'https://facebook.com/elitephotography',
      instagram: 'https://instagram.com/elitephotography',
      twitter: 'https://twitter.com/elitephotography'
    },
    contactInfo: {
      email: '<EMAIL>',
      phone: '+****************',
      address: '123 Photography Lane, Studio City, CA 90210'
    },
    customCSS: `/* Custom Studio Styles */
.gallery-header {
  background: linear-gradient(135deg, #3B82F6, #8B5CF6);
}

.photo-card:hover {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}`,
    emailBranding: {
      headerImage: '/api/placeholder/600/150',
      footerText: 'Thank you for choosing Elite Photography Studio',
      socialLinks: true
    },
    galleryBranding: {
      watermarkEnabled: true,
      watermarkText: '© Elite Photography',
      showStudioInfo: true
    },
    isActive: true,
    lastUpdated: '2024-01-20T15:30:00Z'
  });

  const [brandingTemplates] = useState<BrandingTemplate[]>([
    {
      id: '1',
      name: 'Modern Minimalist',
      category: 'modern',
      preview: '/api/placeholder/300/200',
      colors: {
        primary: '#000000',
        secondary: '#FFFFFF',
        accent: '#FF6B6B'
      },
      typography: {
        headingFont: 'Helvetica Neue',
        bodyFont: 'Helvetica Neue'
      },
      isPopular: true,
      usageCount: 1250,
      description: 'Clean, modern design with bold typography and minimal color palette'
    },
    {
      id: '2',
      name: 'Classic Elegance',
      category: 'classic',
      preview: '/api/placeholder/300/200',
      colors: {
        primary: '#8B4513',
        secondary: '#F5F5DC',
        accent: '#DAA520'
      },
      typography: {
        headingFont: 'Playfair Display',
        bodyFont: 'Source Sans Pro'
      },
      isPopular: false,
      usageCount: 890,
      description: 'Timeless design with serif fonts and warm, earthy colors'
    },
    {
      id: '3',
      name: 'Creative Studio',
      category: 'creative',
      preview: '/api/placeholder/300/200',
      colors: {
        primary: '#FF6B6B',
        secondary: '#4ECDC4',
        accent: '#45B7D1'
      },
      typography: {
        headingFont: 'Montserrat',
        bodyFont: 'Open Sans'
      },
      isPopular: true,
      usageCount: 2100,
      description: 'Vibrant and creative design perfect for artistic photographers'
    }
  ]);

  const [customizationOptions] = useState<CustomizationOption[]>([
    {
      id: '1',
      name: 'Custom Color Palette',
      type: 'color',
      category: 'Appearance',
      description: 'Define your own brand colors for consistent styling',
      isEnabled: true,
      isPremium: false
    },
    {
      id: '2',
      name: 'Typography Selection',
      type: 'font',
      category: 'Appearance',
      description: 'Choose from premium font collections',
      isEnabled: true,
      isPremium: true
    },
    {
      id: '3',
      name: 'Custom CSS',
      type: 'component',
      category: 'Advanced',
      description: 'Add custom CSS for complete design control',
      isEnabled: false,
      isPremium: true
    },
    {
      id: '4',
      name: 'White-label Domain',
      type: 'layout',
      category: 'Domain',
      description: 'Use your own domain name for galleries',
      isEnabled: true,
      isPremium: true
    }
  ]);

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'modern': return 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300';
      case 'classic': return 'bg-amber-100 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300';
      case 'minimal': return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
      case 'creative': return 'bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300';
      case 'professional': return 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  const getDeviceIcon = (device: string) => {
    switch (device) {
      case 'desktop': return Monitor;
      case 'tablet': return Tablet;
      case 'mobile': return Smartphone;
      default: return Monitor;
    }
  };

  return (
    <StudioLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <Palette className="h-8 w-8 text-blue-600" />
              White-labeling & Branding
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Customize your studio's brand identity and create a unique client experience
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button 
              variant="outline"
              onClick={() => setShowPreview(!showPreview)}
            >
              <Eye className="h-4 w-4 mr-2" />
              {showPreview ? 'Hide Preview' : 'Show Preview'}
            </Button>
            <Button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
          </div>
        </div>

        {/* Branding Status */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <Palette className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {brandingSettings.isActive ? 'Active' : 'Inactive'}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Branding Status</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <Globe className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {brandingSettings.domain.isCustomDomainActive ? 'Custom' : 'Subdomain'}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Domain Type</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <Brush className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {brandingTemplates.length}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Templates</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-orange-600 rounded-xl flex items-center justify-center">
                  <Settings className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {customizationOptions.filter(o => o.isEnabled).length}
                  </p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">Active Options</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="customization">Customization</TabsTrigger>
            <TabsTrigger value="domain">Domain</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="h-5 w-5 text-blue-600" />
                  Current Branding Settings
                </CardTitle>
                <CardDescription>
                  Your current brand identity and customization settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Logo & Identity */}
                  <div className="space-y-4">
                    <h3 className="font-semibold text-gray-900 dark:text-white">Logo & Identity</h3>
                    <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center gap-4 mb-4">
                        <img
                          src={brandingSettings.logo.primary}
                          alt="Studio Logo"
                          className="h-12 w-auto"
                        />
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {brandingSettings.studioName}
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            Primary Logo
                          </p>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        <Upload className="h-4 w-4 mr-2" />
                        Update Logo
                      </Button>
                    </div>
                  </div>

                  {/* Color Palette */}
                  <div className="space-y-4">
                    <h3 className="font-semibold text-gray-900 dark:text-white">Color Palette</h3>
                    <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="grid grid-cols-5 gap-3 mb-4">
                        <div className="text-center">
                          <div
                            className="w-12 h-12 rounded-lg mx-auto mb-2"
                            style={{ backgroundColor: brandingSettings.colors.primary }}
                          ></div>
                          <p className="text-xs text-gray-600 dark:text-gray-400">Primary</p>
                        </div>
                        <div className="text-center">
                          <div
                            className="w-12 h-12 rounded-lg mx-auto mb-2"
                            style={{ backgroundColor: brandingSettings.colors.secondary }}
                          ></div>
                          <p className="text-xs text-gray-600 dark:text-gray-400">Secondary</p>
                        </div>
                        <div className="text-center">
                          <div
                            className="w-12 h-12 rounded-lg mx-auto mb-2"
                            style={{ backgroundColor: brandingSettings.colors.accent }}
                          ></div>
                          <p className="text-xs text-gray-600 dark:text-gray-400">Accent</p>
                        </div>
                        <div className="text-center">
                          <div
                            className="w-12 h-12 rounded-lg mx-auto mb-2 border"
                            style={{ backgroundColor: brandingSettings.colors.background }}
                          ></div>
                          <p className="text-xs text-gray-600 dark:text-gray-400">Background</p>
                        </div>
                        <div className="text-center">
                          <div
                            className="w-12 h-12 rounded-lg mx-auto mb-2"
                            style={{ backgroundColor: brandingSettings.colors.text }}
                          ></div>
                          <p className="text-xs text-gray-600 dark:text-gray-400">Text</p>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        <Paintbrush className="h-4 w-4 mr-2" />
                        Edit Colors
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </StudioLayout>
  );
}
