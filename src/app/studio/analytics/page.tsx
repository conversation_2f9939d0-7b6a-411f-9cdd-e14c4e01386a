"use client";

import { useState } from "react";
import { 
  Bar<PERSON>hart3,
  TrendingUp,
  TrendingDown,
  Users,
  Camera,
  Download,
  Eye,
  Calendar,
  Clock,
  MapPin,
  Smartphone,
  Monitor,
  Globe,
  Activity,
  Target,
  Zap
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import StudioLayout from "@/components/studio/StudioLayout";
import { formatDate, cn } from "@/lib/utils";

interface AnalyticsData {
  totalViews: number;
  totalDownloads: number;
  totalClients: number;
  totalPhotos: number;
  avgSessionTime: number;
  bounceRate: number;
  conversionRate: number;
  topPerformingEvents: Array<{
    name: string;
    views: number;
    downloads: number;
    engagement: number;
  }>;
  deviceBreakdown: Array<{
    device: string;
    count: number;
    percentage: number;
  }>;
  timeAnalytics: Array<{
    hour: number;
    views: number;
    downloads: number;
  }>;
  weeklyTrends: Array<{
    date: string;
    views: number;
    downloads: number;
    newClients: number;
  }>;
}

export default function AnalyticsPage() {
  const [period, setPeriod] = useState("30");
  const [analytics] = useState<AnalyticsData>({
    totalViews: 12450,
    totalDownloads: 3240,
    totalClients: 45,
    totalPhotos: 1250,
    avgSessionTime: 285, // seconds
    bounceRate: 23.5,
    conversionRate: 26.8,
    topPerformingEvents: [
      { name: 'Sarah Wedding', views: 2450, downloads: 680, engagement: 87.5 },
      { name: 'Mike Birthday', views: 1890, downloads: 420, engagement: 72.3 },
      { name: 'Emma Engagement', views: 1560, downloads: 380, engagement: 68.9 },
      { name: 'Corporate Event', views: 1200, downloads: 290, engagement: 65.2 }
    ],
    deviceBreakdown: [
      { device: 'Mobile', count: 7890, percentage: 63.4 },
      { device: 'Desktop', count: 3240, percentage: 26.0 },
      { device: 'Tablet', count: 1320, percentage: 10.6 }
    ],
    timeAnalytics: [
      { hour: 0, views: 120, downloads: 25 },
      { hour: 6, views: 180, downloads: 35 },
      { hour: 9, views: 450, downloads: 95 },
      { hour: 12, views: 680, downloads: 145 },
      { hour: 15, views: 720, downloads: 160 },
      { hour: 18, views: 890, downloads: 185 },
      { hour: 21, views: 650, downloads: 140 },
      { hour: 23, views: 320, downloads: 65 }
    ],
    weeklyTrends: [
      { date: '2024-01-15', views: 1200, downloads: 280, newClients: 3 },
      { date: '2024-01-16', views: 1450, downloads: 320, newClients: 5 },
      { date: '2024-01-17', views: 1680, downloads: 390, newClients: 2 },
      { date: '2024-01-18', views: 1890, downloads: 420, newClients: 4 },
      { date: '2024-01-19', views: 2100, downloads: 480, newClients: 6 },
      { date: '2024-01-20', views: 2450, downloads: 560, newClients: 8 },
      { date: '2024-01-21', views: 1980, downloads: 450, newClients: 3 }
    ]
  });

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

  return (
    <StudioLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <BarChart3 className="h-8 w-8 text-blue-600" />
              Analytics Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Detailed insights into your studio's performance and client engagement
            </p>
          </div>
          <div className="flex items-center gap-3">
            <select
              value={period}
              onChange={(e) => setPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="90">Last 90 days</option>
              <option value="365">Last year</option>
            </select>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <Eye className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {analytics.totalViews.toLocaleString()}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Total Views</p>
                  <div className="flex items-center gap-1 mt-1">
                    <TrendingUp className="h-3 w-3 text-green-600" />
                    <span className="text-xs text-green-600 font-medium">+12.5%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <Download className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {analytics.totalDownloads.toLocaleString()}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Downloads</p>
                  <div className="flex items-center gap-1 mt-1">
                    <TrendingUp className="h-3 w-3 text-green-600" />
                    <span className="text-xs text-green-600 font-medium">+8.3%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <Users className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {analytics.totalClients}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Active Clients</p>
                  <div className="flex items-center gap-1 mt-1">
                    <TrendingUp className="h-3 w-3 text-green-600" />
                    <span className="text-xs text-green-600 font-medium">+15.2%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-orange-600 rounded-xl flex items-center justify-center">
                  <Target className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {analytics.conversionRate}%
                  </p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">Conversion Rate</p>
                  <div className="flex items-center gap-1 mt-1">
                    <TrendingUp className="h-3 w-3 text-green-600" />
                    <span className="text-xs text-green-600 font-medium">+3.1%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Performance Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-10 w-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Clock className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-xl font-bold text-gray-900 dark:text-white">
                    {formatTime(analytics.avgSessionTime)}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Avg. Session Time</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-10 w-10 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
                  <TrendingDown className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-xl font-bold text-gray-900 dark:text-white">
                    {analytics.bounceRate}%
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Bounce Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-10 w-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                  <Zap className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-xl font-bold text-gray-900 dark:text-white">
                    {((analytics.totalDownloads / analytics.totalViews) * 100).toFixed(1)}%
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Download Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Weekly Trends */}
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-blue-600" />
                Weekly Trends
              </CardTitle>
              <CardDescription>
                Views, downloads, and new clients over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={analytics.weeklyTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" tickFormatter={(date) => formatDate(date)} />
                  <YAxis />
                  <Tooltip labelFormatter={(date) => formatDate(date)} />
                  <Area 
                    type="monotone" 
                    dataKey="views" 
                    stackId="1"
                    stroke="#3B82F6" 
                    fill="#3B82F6" 
                    fillOpacity={0.6}
                    name="Views"
                  />
                  <Area 
                    type="monotone" 
                    dataKey="downloads" 
                    stackId="2"
                    stroke="#10B981" 
                    fill="#10B981" 
                    fillOpacity={0.6}
                    name="Downloads"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Device Breakdown */}
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Smartphone className="h-5 w-5 text-green-600" />
                Device Breakdown
              </CardTitle>
              <CardDescription>
                How clients access your galleries
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center">
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={analytics.deviceBreakdown}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ device, percentage }) => `${device} ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {analytics.deviceBreakdown.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="flex justify-center gap-4 mt-4">
                {analytics.deviceBreakdown.map((device, index) => (
                  <div key={device.device} className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {device.device}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Top Performing Events */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-purple-600" />
              Top Performing Events
            </CardTitle>
            <CardDescription>
              Events with highest engagement and downloads
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.topPerformingEvents.map((event, index) => (
                <div key={event.name} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className={cn(
                      "w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold text-white",
                      index === 0 ? "bg-gradient-to-br from-yellow-400 to-yellow-500" :
                      index === 1 ? "bg-gradient-to-br from-gray-400 to-gray-500" :
                      index === 2 ? "bg-gradient-to-br from-orange-400 to-orange-500" :
                      "bg-gradient-to-br from-blue-400 to-blue-500"
                    )}>
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900 dark:text-white">
                        {event.name}
                      </p>
                      <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                        <span className="flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          {event.views.toLocaleString()} views
                        </span>
                        <span className="flex items-center gap-1">
                          <Download className="h-3 w-3" />
                          {event.downloads.toLocaleString()} downloads
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-gray-900 dark:text-white">
                      {event.engagement}%
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">engagement</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Hourly Activity */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-orange-600" />
              Hourly Activity Pattern
            </CardTitle>
            <CardDescription>
              When your clients are most active
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={analytics.timeAnalytics}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="hour" tickFormatter={(hour) => `${hour}:00`} />
                <YAxis />
                <Tooltip labelFormatter={(hour) => `${hour}:00`} />
                <Bar dataKey="views" fill="#3B82F6" name="Views" />
                <Bar dataKey="downloads" fill="#10B981" name="Downloads" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </StudioLayout>
  );
}
