"use client";

import { useState } from "react";
import {
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  User,
  Globe,
  Smartphone,
  Monitor,
  Key,
  Settings,
  Search,
  Download,
  RefreshCw,
  Ban,
  UserCheck,
  UserX,
  Activity,
  MapPin,
  Zap,
  Plus,
  Edit,
  Mail
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import AdminLayout from "@/components/admin/AdminLayout";
import { formatDate, timeAgo, cn } from "@/lib/utils";

interface SecurityMetrics {
  totalLogins: number;
  failedLogins: number;
  suspiciousActivity: number;
  blockedIPs: number;
  activeAdmins: number;
  apiCalls: number;
  securityScore: number;
}

interface LoginLog {
  id: string;
  userId: string;
  userName: string;
  userType: 'admin' | 'studio' | 'client';
  ipAddress: string;
  location: string;
  device: string;
  browser: string;
  status: 'success' | 'failed' | 'blocked';
  timestamp: string;
  riskLevel: 'low' | 'medium' | 'high';
}

interface APILog {
  id: string;
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  statusCode: number;
  responseTime: number;
  ipAddress: string;
  userAgent: string;
  timestamp: string;
  userId?: string;
  errorMessage?: string;
}

interface AdminUser {
  id: string;
  name: string;
  email: string;
  role: 'super_admin' | 'admin' | 'moderator';
  status: 'active' | 'inactive' | 'suspended';
  lastLogin: string;
  permissions: string[];
  createdAt: string;
  loginCount: number;
}

export default function SecurityPage() {
  const [activeTab, setActiveTab] = useState("overview");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [timeRange, setTimeRange] = useState("24h");

  const [securityMetrics] = useState<SecurityMetrics>({
    totalLogins: 15420,
    failedLogins: 234,
    suspiciousActivity: 12,
    blockedIPs: 45,
    activeAdmins: 8,
    apiCalls: 2850000,
    securityScore: 92
  });
  const [loginLogs] = useState<LoginLog[]>([
    {
      id: '1',
      userId: 'admin1',
      userName: 'John Admin',
      userType: 'admin',
      ipAddress: '*************',
      location: 'New York, US',
      device: 'Desktop',
      browser: 'Chrome 120.0',
      status: 'success',
      timestamp: '2024-01-20T14:30:00Z',
      riskLevel: 'low'
    },
    {
      id: '2',
      userId: 'studio1',
      userName: 'PhotoStudio Pro',
      userType: 'studio',
      ipAddress: '************',
      location: 'Los Angeles, US',
      device: 'Mobile',
      browser: 'Safari 17.0',
      status: 'success',
      timestamp: '2024-01-20T14:25:00Z',
      riskLevel: 'low'
    },
    {
      id: '3',
      userId: 'unknown',
      userName: 'Unknown User',
      userType: 'admin',
      ipAddress: '************',
      location: 'Unknown',
      device: 'Desktop',
      browser: 'Chrome 119.0',
      status: 'failed',
      timestamp: '2024-01-20T14:20:00Z',
      riskLevel: 'high'
    }
  ]);

  const [apiLogs] = useState<APILog[]>([
    {
      id: '1',
      endpoint: '/api/admin/studios',
      method: 'GET',
      statusCode: 200,
      responseTime: 145,
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      timestamp: '2024-01-20T14:30:00Z',
      userId: 'admin1'
    },
    {
      id: '2',
      endpoint: '/api/photos/upload',
      method: 'POST',
      statusCode: 201,
      responseTime: 2340,
      ipAddress: '************',
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X)',
      timestamp: '2024-01-20T14:28:00Z',
      userId: 'studio1'
    },
    {
      id: '3',
      endpoint: '/api/admin/users',
      method: 'DELETE',
      statusCode: 403,
      responseTime: 89,
      ipAddress: '************',
      userAgent: 'curl/7.68.0',
      timestamp: '2024-01-20T14:25:00Z',
      errorMessage: 'Insufficient permissions'
    }
  ]);

  const [adminUsers] = useState<AdminUser[]>([
    {
      id: '1',
      name: 'John Admin',
      email: '<EMAIL>',
      role: 'super_admin',
      status: 'active',
      lastLogin: '2024-01-20T14:30:00Z',
      permissions: ['all'],
      createdAt: '2023-01-15T10:00:00Z',
      loginCount: 1250
    },
    {
      id: '2',
      name: 'Sarah Manager',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      lastLogin: '2024-01-20T09:15:00Z',
      permissions: ['studios', 'clients', 'billing'],
      createdAt: '2023-03-20T14:00:00Z',
      loginCount: 890
    },
    {
      id: '3',
      name: 'Mike Support',
      email: '<EMAIL>',
      role: 'moderator',
      status: 'inactive',
      lastLogin: '2024-01-18T16:45:00Z',
      permissions: ['clients', 'support'],
      createdAt: '2023-06-10T11:30:00Z',
      loginCount: 456
    }
  ]);

  const filteredLoginLogs = loginLogs.filter(log => {
    const matchesSearch = log.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.ipAddress.includes(searchTerm) ||
                         log.location.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || log.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case 'success': return 'default';
      case 'failed': return 'destructive';
      case 'blocked': return 'secondary';
      case 'active': return 'default';
      case 'inactive': return 'secondary';
      case 'suspended': return 'destructive';
      default: return 'secondary';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high': return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
      case 'low': return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-700';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'super_admin': return 'bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300';
      case 'admin': return 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300';
      case 'moderator': return 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  const getDeviceIcon = (device: string) => {
    switch (device.toLowerCase()) {
      case 'mobile': return Smartphone;
      case 'tablet': return Smartphone;
      default: return Monitor;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return CheckCircle;
      case 'failed': return XCircle;
      case 'blocked': return Ban;
      default: return AlertTriangle;
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <Shield className="h-8 w-8 text-red-600" />
              Security & Access Logs
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Monitor security events, access logs, and manage admin permissions
            </p>
          </div>
          <div className="flex items-center gap-3">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="1h">Last hour</option>
              <option value="24h">Last 24 hours</option>
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
            </select>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Logs
            </Button>
            <Button variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Security Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <Shield className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {securityMetrics.securityScore}%
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Security Score</p>
                  <div className="flex items-center gap-1 mt-1">
                    <CheckCircle className="h-3 w-3 text-green-600" />
                    <span className="text-xs text-green-600 dark:text-green-400">
                      Excellent
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <UserCheck className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {securityMetrics.totalLogins.toLocaleString()}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Total Logins</p>
                  <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                    Last {timeRange}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-red-600 rounded-xl flex items-center justify-center">
                  <AlertTriangle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-red-900 dark:text-red-100">
                    {securityMetrics.failedLogins}
                  </p>
                  <p className="text-sm text-red-600 dark:text-red-400">Failed Logins</p>
                  <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                    {securityMetrics.suspiciousActivity} suspicious
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <Ban className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {securityMetrics.blockedIPs}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Blocked IPs</p>
                  <p className="text-xs text-purple-600 dark:text-purple-400 mt-1">
                    Auto-blocked
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Security Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="login-logs">Login Logs</TabsTrigger>
            <TabsTrigger value="api-logs">API Logs</TabsTrigger>
            <TabsTrigger value="admin-users">Admin Users</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5 text-blue-600" />
                    Security Overview
                  </CardTitle>
                  <CardDescription>
                    Current security status and key metrics
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Security Score
                      </span>
                      <div className="flex items-center gap-2">
                        <Progress value={securityMetrics.securityScore} className="w-20 h-2" />
                        <span className="text-sm font-bold text-gray-900 dark:text-white">
                          {securityMetrics.securityScore}%
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Active Admins
                      </span>
                      <span className="text-sm font-bold text-gray-900 dark:text-white">
                        {securityMetrics.activeAdmins}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        API Calls (24h)
                      </span>
                      <span className="text-sm font-bold text-gray-900 dark:text-white">
                        {securityMetrics.apiCalls.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Failed Login Rate
                      </span>
                      <span className="text-sm font-bold text-red-600">
                        {((securityMetrics.failedLogins / securityMetrics.totalLogins) * 100).toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-red-600" />
                    Security Alerts
                  </CardTitle>
                  <CardDescription>
                    Recent security incidents requiring attention
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                        <span className="text-sm font-medium text-red-800 dark:text-red-200">
                          {securityMetrics.suspiciousActivity} suspicious activities detected
                        </span>
                      </div>
                    </div>
                    <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Ban className="h-4 w-4 text-yellow-600" />
                        <span className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                          {securityMetrics.blockedIPs} IP addresses blocked
                        </span>
                      </div>
                    </div>
                    <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Shield className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                          Security monitoring active
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Login Logs Tab */}
          <TabsContent value="login-logs" className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <UserCheck className="h-5 w-5 text-blue-600" />
                      Login Activity Logs
                    </CardTitle>
                    <CardDescription>
                      Monitor user authentication attempts and access patterns
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        placeholder="Search logs..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-9 w-64"
                      />
                    </div>
                    <select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="all">All Status</option>
                      <option value="success">Success</option>
                      <option value="failed">Failed</option>
                      <option value="blocked">Blocked</option>
                    </select>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredLoginLogs.map(log => {
                    const DeviceIcon = getDeviceIcon(log.device);
                    const StatusIcon = getStatusIcon(log.status);
                    return (
                      <div key={log.id} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <div className={cn(
                              "h-10 w-10 rounded-lg flex items-center justify-center",
                              log.status === 'success' ? 'bg-green-100 dark:bg-green-900/20' :
                              log.status === 'failed' ? 'bg-red-100 dark:bg-red-900/20' :
                              'bg-yellow-100 dark:bg-yellow-900/20'
                            )}>
                              <StatusIcon className={cn(
                                "h-5 w-5",
                                log.status === 'success' ? 'text-green-600' :
                                log.status === 'failed' ? 'text-red-600' :
                                'text-yellow-600'
                              )} />
                            </div>
                            <div>
                              <div className="flex items-center gap-2">
                                <h3 className="font-semibold text-gray-900 dark:text-white">
                                  {log.userName}
                                </h3>
                                <Badge variant={getStatusColor(log.status)}>
                                  {log.status}
                                </Badge>
                                <Badge className={cn("text-xs", getRiskColor(log.riskLevel))}>
                                  {log.riskLevel} risk
                                </Badge>
                              </div>
                              <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mt-1">
                                <div className="flex items-center gap-1">
                                  <User className="h-3 w-3" />
                                  <span>{log.userType}</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Globe className="h-3 w-3" />
                                  <span>{log.ipAddress}</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <MapPin className="h-3 w-3" />
                                  <span>{log.location}</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <DeviceIcon className="h-3 w-3" />
                                  <span>{log.device} • {log.browser}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                              {formatDate(log.timestamp)}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {timeAgo(log.timestamp)}
                            </p>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* API Logs Tab */}
          <TabsContent value="api-logs" className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-purple-600" />
                  API Access Logs
                </CardTitle>
                <CardDescription>
                  Monitor API usage, performance, and security events
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {apiLogs.map(log => (
                    <div key={log.id} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className={cn(
                            "h-10 w-10 rounded-lg flex items-center justify-center font-mono text-xs font-bold",
                            log.statusCode >= 200 && log.statusCode < 300 ? 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300' :
                            log.statusCode >= 400 && log.statusCode < 500 ? 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300' :
                            log.statusCode >= 500 ? 'bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300' :
                            'bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300'
                          )}>
                            {log.statusCode}
                          </div>
                          <div>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="font-mono text-xs">
                                {log.method}
                              </Badge>
                              <span className="font-mono text-sm text-gray-900 dark:text-white">
                                {log.endpoint}
                              </span>
                            </div>
                            <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mt-1">
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                <span>{log.responseTime}ms</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Globe className="h-3 w-3" />
                                <span>{log.ipAddress}</span>
                              </div>
                              {log.userId && (
                                <div className="flex items-center gap-1">
                                  <User className="h-3 w-3" />
                                  <span>{log.userId}</span>
                                </div>
                              )}
                            </div>
                            {log.errorMessage && (
                              <p className="text-sm text-red-600 dark:text-red-400 mt-1">
                                Error: {log.errorMessage}
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {formatDate(log.timestamp)}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {timeAgo(log.timestamp)}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Admin Users Tab */}
          <TabsContent value="admin-users" className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Key className="h-5 w-5 text-green-600" />
                      Admin User Management
                    </CardTitle>
                    <CardDescription>
                      Manage administrator accounts and permissions
                    </CardDescription>
                  </div>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Admin
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {adminUsers.map(user => (
                    <div key={user.id} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-semibold">
                            {user.name.split(' ').map(n => n[0]).join('')}
                          </div>
                          <div>
                            <div className="flex items-center gap-2">
                              <h3 className="font-semibold text-gray-900 dark:text-white">
                                {user.name}
                              </h3>
                              <Badge className={cn("text-xs", getRoleColor(user.role))}>
                                {user.role.replace('_', ' ')}
                              </Badge>
                              <Badge variant={getStatusColor(user.status)}>
                                {user.status}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mt-1">
                              <div className="flex items-center gap-1">
                                <Mail className="h-3 w-3" />
                                <span>{user.email}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                <span>Last login: {timeAgo(user.lastLogin)}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Activity className="h-3 w-3" />
                                <span>{user.loginCount} logins</span>
                              </div>
                            </div>
                            <div className="flex items-center gap-2 mt-2">
                              <span className="text-xs text-gray-500 dark:text-gray-400">Permissions:</span>
                              {user.permissions.slice(0, 3).map(permission => (
                                <Badge key={permission} variant="outline" className="text-xs">
                                  {permission}
                                </Badge>
                              ))}
                              {user.permissions.length > 3 && (
                                <Badge variant="outline" className="text-xs">
                                  +{user.permissions.length - 3} more
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </Button>
                          <Button variant="outline" size="sm">
                            <Settings className="h-4 w-4 mr-2" />
                            Permissions
                          </Button>
                          {user.status === 'active' ? (
                            <Button variant="outline" size="sm">
                              <UserX className="h-4 w-4 mr-2" />
                              Suspend
                            </Button>
                          ) : (
                            <Button variant="outline" size="sm">
                              <UserCheck className="h-4 w-4 mr-2" />
                              Activate
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
}
