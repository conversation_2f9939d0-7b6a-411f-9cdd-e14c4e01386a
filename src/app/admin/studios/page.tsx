"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Building2,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  Clock,
  Shield,
  Eye,
  Ban,
  CheckSquare,
  HardDrive,
  Users,
  Camera,
  Calendar
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import AdminLayout from "@/components/admin/AdminLayout";
import { formatFileSize, formatDate, cn } from "@/lib/utils";

interface Studio {
  id: string;
  name: string;
  email: string;
  businessName?: string;
  phone?: string;
  isActive: boolean;
  isApproved: boolean;
  storageUsed: string;
  createdAt: string;
  subscription?: {
    plan: {
      name: string;
      storageLimit: string;
    };
  };
  _count: {
    clients: number;
    photos: number;
  };
}

export default function AdminStudiosPage() {
  const [studios, setStudios] = useState<Studio[]>([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const router = useRouter();

  const fetchStudios = async () => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "10",
        ...(search && { search }),
        ...(statusFilter !== "all" && { status: statusFilter }),
      });

      const response = await fetch(`/api/admin/studios?${params}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      });

      const data = await response.json();
      if (data.success) {
        setStudios(data.data);
        setTotalPages(data.pagination.totalPages);
      }
    } catch (error) {
      console.error("Error fetching studios:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStudios();
  }, [page, search, statusFilter]);

  const handleStudioAction = async (studioId: string, action: string) => {
    try {
      const response = await fetch("/api/admin/studios", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify({ studioId, action }),
      });

      const data = await response.json();
      if (data.success) {
        fetchStudios(); // Refresh the list
      }
    } catch (error) {
      console.error("Error updating studio:", error);
    }
  };

  const getStatusBadge = (studio: Studio) => {
    if (!studio.isApproved) {
      return (
        <Badge variant="warning" className="flex items-center gap-1">
          <Clock className="w-3 h-3" />
          Pending
        </Badge>
      );
    }
    if (!studio.isActive) {
      return (
        <Badge variant="destructive" className="flex items-center gap-1">
          <Ban className="w-3 h-3" />
          Blocked
        </Badge>
      );
    }
    return (
      <Badge variant="success" className="flex items-center gap-1">
        <CheckCircle className="w-3 h-3" />
        Active
      </Badge>
    );
  };

  const formatStorageUsage = (used: string, limit?: string) => {
    const usedBytes = Number(used);
    const limitBytes = limit ? Number(limit) : 0;

    if (limit) {
      const percentage = (usedBytes / limitBytes) * 100;
      return {
        text: `${formatFileSize(usedBytes)} / ${formatFileSize(limitBytes)}`,
        percentage: Math.min(percentage, 100)
      };
    }
    return {
      text: formatFileSize(usedBytes),
      percentage: 0
    };
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <Building2 className="h-8 w-8 text-blue-600" />
              Studio Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Manage and monitor all registered studios on your platform
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Badge variant="secondary" className="text-sm">
              {studios.length} Studios
            </Badge>
          </div>
        </div>

        {/* Filters & Search */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    placeholder="Search by studio name, email, or business name..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="pl-10 h-11 bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
              <div className="flex flex-wrap gap-2">
                {[
                  { key: "all", label: "All Studios", count: studios.length },
                  { key: "pending", label: "Pending", count: studios.filter(s => !s.isApproved).length },
                  { key: "approved", label: "Active", count: studios.filter(s => s.isApproved && s.isActive).length },
                  { key: "blocked", label: "Blocked", count: studios.filter(s => s.isApproved && !s.isActive).length }
                ].map((status) => (
                  <Button
                    key={status.key}
                    variant={statusFilter === status.key ? "default" : "outline"}
                    size="sm"
                    onClick={() => setStatusFilter(status.key)}
                    className="flex items-center gap-2"
                  >
                    {status.label}
                    <Badge variant="secondary" className="text-xs">
                      {status.count}
                    </Badge>
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Studios List */}
        <div className="space-y-4">
          {studios.map((studio) => {
            const storageInfo = formatStorageUsage(
              studio.storageUsed,
              studio.subscription?.plan.storageLimit
            );

            return (
              <Card key={studio.id} className="border-0 shadow-md hover:shadow-lg transition-all duration-200 bg-white dark:bg-gray-800">
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
                    <div className="flex-1 space-y-4">
                      {/* Studio Header */}
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                            <Building2 className="h-6 w-6 text-white" />
                          </div>
                          <div>
                            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                              {studio.businessName || studio.name}
                            </h3>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {studio.email}
                            </p>
                          </div>
                        </div>
                        {getStatusBadge(studio)}
                      </div>

                      {/* Studio Stats */}
                      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                        <div className="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <Users className="h-4 w-4 text-blue-600" />
                          <div>
                            <p className="text-xs text-gray-500 dark:text-gray-400">Clients</p>
                            <p className="font-semibold text-gray-900 dark:text-white">{studio._count.clients}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <Camera className="h-4 w-4 text-green-600" />
                          <div>
                            <p className="text-xs text-gray-500 dark:text-gray-400">Photos</p>
                            <p className="font-semibold text-gray-900 dark:text-white">{studio._count.photos}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <HardDrive className="h-4 w-4 text-purple-600" />
                          <div>
                            <p className="text-xs text-gray-500 dark:text-gray-400">Storage</p>
                            <p className="font-semibold text-gray-900 dark:text-white">{storageInfo.text}</p>
                            {storageInfo.percentage > 0 && (
                              <Progress
                                value={storageInfo.percentage}
                                className="h-1 mt-1"
                                variant={storageInfo.percentage > 80 ? "destructive" : storageInfo.percentage > 60 ? "warning" : "default"}
                              />
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <Calendar className="h-4 w-4 text-orange-600" />
                          <div>
                            <p className="text-xs text-gray-500 dark:text-gray-400">Joined</p>
                            <p className="font-semibold text-gray-900 dark:text-white">{formatDate(studio.createdAt)}</p>
                          </div>
                        </div>
                      </div>

                      {/* Additional Info */}
                      <div className="flex flex-wrap gap-2 text-xs text-gray-500 dark:text-gray-400">
                        <span>Plan: {studio.subscription?.plan.name || "Free"}</span>
                        {studio.phone && <span>• Phone: {studio.phone}</span>}
                      </div>
                    </div>
                    {/* Action Buttons */}
                    <div className="flex flex-col gap-2 lg:flex-row lg:items-center">
                      {!studio.isApproved && (
                        <>
                          <Button
                            size="sm"
                            onClick={() => handleStudioAction(studio.id, "approve")}
                            className="bg-green-600 hover:bg-green-700 text-white"
                          >
                            <CheckCircle className="w-4 h-4 mr-2" />
                            Approve
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleStudioAction(studio.id, "reject")}
                          >
                            <XCircle className="w-4 h-4 mr-2" />
                            Reject
                          </Button>
                        </>
                      )}
                      {studio.isApproved && (
                        <>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => router.push(`/admin/studios/${studio.id}`)}
                            className="hover:bg-blue-50 hover:border-blue-200 hover:text-blue-600 dark:hover:bg-blue-900/20"
                          >
                            <Eye className="w-4 h-4 mr-2" />
                            View Details
                          </Button>
                          {studio.isActive ? (
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleStudioAction(studio.id, "block")}
                            >
                              <Ban className="w-4 h-4 mr-2" />
                              Block
                            </Button>
                          ) : (
                            <Button
                              size="sm"
                              onClick={() => handleStudioAction(studio.id, "unblock")}
                              className="bg-green-600 hover:bg-green-700 text-white"
                            >
                              <CheckSquare className="w-4 h-4 mr-2" />
                              Unblock
                            </Button>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardContent className="p-4">
              <div className="flex justify-center">
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    disabled={page === 1}
                    onClick={() => setPage(page - 1)}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    Previous
                  </Button>
                  <div className="flex items-center gap-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const pageNum = i + 1;
                      return (
                        <Button
                          key={pageNum}
                          variant={page === pageNum ? "default" : "outline"}
                          size="sm"
                          onClick={() => setPage(pageNum)}
                          className="w-10 h-10"
                        >
                          {pageNum}
                        </Button>
                      );
                    })}
                  </div>
                  <Button
                    variant="outline"
                    disabled={page === totalPages}
                    onClick={() => setPage(page + 1)}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    Next
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Empty State */}
        {studios.length === 0 && !loading && (
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardContent className="p-12 text-center">
              <Building2 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                No studios found
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                {search || statusFilter !== "all"
                  ? "Try adjusting your search or filter criteria."
                  : "No studios have registered yet."
                }
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </AdminLayout>
  );
}
