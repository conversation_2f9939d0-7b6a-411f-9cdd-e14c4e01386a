"use client";

import { useState } from "react";
import { 
  CreditCard,
  DollarSign,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Calendar,
  Users,
  Building2,
  Download,
  Eye,
  RefreshCw,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Mail,
  Phone,
  FileText,
  Zap
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import AdminLayout from "@/components/admin/AdminLayout";
import { formatDate, formatCurrency, timeAgo, cn } from "@/lib/utils";

interface BillingMetrics {
  totalRevenue: number;
  monthlyRevenue: number;
  revenueGrowth: number;
  totalSubscriptions: number;
  activeSubscriptions: number;
  churnRate: number;
  averageRevenuePerUser: number;
  pendingPayments: number;
  failedPayments: number;
}

interface Subscription {
  id: string;
  studioId: string;
  studioName: string;
  planName: string;
  planType: 'starter' | 'professional' | 'enterprise' | 'custom';
  amount: number;
  currency: string;
  status: 'active' | 'past_due' | 'canceled' | 'unpaid' | 'trialing';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  nextBillingDate: string;
  paymentMethod: string;
  lastPaymentDate?: string;
  lastPaymentAmount?: number;
  failedAttempts: number;
  createdAt: string;
}

interface Payment {
  id: string;
  subscriptionId: string;
  studioName: string;
  amount: number;
  currency: string;
  status: 'succeeded' | 'failed' | 'pending' | 'refunded';
  paymentMethod: string;
  description: string;
  createdAt: string;
  failureReason?: string;
  refundAmount?: number;
  refundDate?: string;
}

export default function BillingPage() {
  const [activeTab, setActiveTab] = useState("overview");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [planFilter, setPlanFilter] = useState("all");

  const [billingMetrics] = useState<BillingMetrics>({
    totalRevenue: 2456780,
    monthlyRevenue: 185420,
    revenueGrowth: 12.5,
    totalSubscriptions: 1250,
    activeSubscriptions: 1180,
    churnRate: 3.2,
    averageRevenuePerUser: 148.50,
    pendingPayments: 23,
    failedPayments: 8
  });

  const [subscriptions] = useState<Subscription[]>([
    {
      id: 'sub_001',
      studioId: 'studio1',
      studioName: 'PhotoStudio Pro',
      planName: 'Enterprise Plan',
      planType: 'enterprise',
      amount: 299,
      currency: 'USD',
      status: 'active',
      currentPeriodStart: '2024-01-01T00:00:00Z',
      currentPeriodEnd: '2024-02-01T00:00:00Z',
      nextBillingDate: '2024-02-01T00:00:00Z',
      paymentMethod: 'Visa ****4242',
      lastPaymentDate: '2024-01-01T00:00:00Z',
      lastPaymentAmount: 299,
      failedAttempts: 0,
      createdAt: '2023-06-15T10:30:00Z'
    },
    {
      id: 'sub_002',
      studioId: 'studio2',
      studioName: 'Wedding Memories',
      planName: 'Professional Plan',
      planType: 'professional',
      amount: 149,
      currency: 'USD',
      status: 'past_due',
      currentPeriodStart: '2024-01-15T00:00:00Z',
      currentPeriodEnd: '2024-02-15T00:00:00Z',
      nextBillingDate: '2024-01-20T00:00:00Z',
      paymentMethod: 'MasterCard ****5555',
      lastPaymentDate: '2023-12-15T00:00:00Z',
      lastPaymentAmount: 149,
      failedAttempts: 2,
      createdAt: '2023-08-20T14:20:00Z'
    },
    {
      id: 'sub_003',
      studioId: 'studio3',
      studioName: 'Quick Shots',
      planName: 'Starter Plan',
      planType: 'starter',
      amount: 49,
      currency: 'USD',
      status: 'active',
      currentPeriodStart: '2024-01-10T00:00:00Z',
      currentPeriodEnd: '2024-02-10T00:00:00Z',
      nextBillingDate: '2024-02-10T00:00:00Z',
      paymentMethod: 'Visa ****1234',
      lastPaymentDate: '2024-01-10T00:00:00Z',
      lastPaymentAmount: 49,
      failedAttempts: 0,
      createdAt: '2024-01-10T09:15:00Z'
    }
  ]);

  const [payments] = useState<Payment[]>([
    {
      id: 'pay_001',
      subscriptionId: 'sub_001',
      studioName: 'PhotoStudio Pro',
      amount: 299,
      currency: 'USD',
      status: 'succeeded',
      paymentMethod: 'Visa ****4242',
      description: 'Enterprise Plan - Monthly',
      createdAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 'pay_002',
      subscriptionId: 'sub_002',
      studioName: 'Wedding Memories',
      amount: 149,
      currency: 'USD',
      status: 'failed',
      paymentMethod: 'MasterCard ****5555',
      description: 'Professional Plan - Monthly',
      createdAt: '2024-01-15T00:00:00Z',
      failureReason: 'Insufficient funds'
    },
    {
      id: 'pay_003',
      subscriptionId: 'sub_003',
      studioName: 'Quick Shots',
      amount: 49,
      currency: 'USD',
      status: 'succeeded',
      paymentMethod: 'Visa ****1234',
      description: 'Starter Plan - Monthly',
      createdAt: '2024-01-10T00:00:00Z'
    }
  ]);

  const [revenueData] = useState([
    { month: 'Jul', revenue: 145000, subscriptions: 1050 },
    { month: 'Aug', revenue: 152000, subscriptions: 1080 },
    { month: 'Sep', revenue: 148000, subscriptions: 1065 },
    { month: 'Oct', revenue: 165000, subscriptions: 1120 },
    { month: 'Nov', revenue: 172000, subscriptions: 1150 },
    { month: 'Dec', revenue: 185000, subscriptions: 1180 }
  ]);

  const filteredSubscriptions = subscriptions.filter(sub => {
    const matchesSearch = sub.studioName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sub.planName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || sub.status === statusFilter;
    const matchesPlan = planFilter === "all" || sub.planType === planFilter;
    return matchesSearch && matchesStatus && matchesPlan;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'trialing': return 'secondary';
      case 'past_due': return 'warning';
      case 'unpaid': return 'destructive';
      case 'canceled': return 'secondary';
      case 'succeeded': return 'success';
      case 'failed': return 'destructive';
      case 'pending': return 'warning';
      case 'refunded': return 'secondary';
      default: return 'secondary';
    }
  };

  const getPlanColor = (planType: string) => {
    switch (planType) {
      case 'enterprise': return 'bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300';
      case 'professional': return 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300';
      case 'starter': return 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300';
      case 'custom': return 'bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <CreditCard className="h-8 w-8 text-green-600" />
              Billing & Revenue
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Monitor revenue, manage subscriptions, and track payment performance
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
            <Button variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Sync Payments
            </Button>
            <Button className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800">
              <Plus className="h-4 w-4 mr-2" />
              Manual Billing
            </Button>
          </div>
        </div>

        {/* Revenue Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {formatCurrency(billingMetrics.monthlyRevenue)}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Monthly Revenue</p>
                  <div className="flex items-center gap-1 mt-1">
                    <TrendingUp className="h-3 w-3 text-green-600" />
                    <span className="text-xs text-green-600 dark:text-green-400">
                      +{billingMetrics.revenueGrowth}%
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <Users className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {billingMetrics.activeSubscriptions}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Active Subscriptions</p>
                  <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                    of {billingMetrics.totalSubscriptions} total
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {formatCurrency(billingMetrics.averageRevenuePerUser)}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">ARPU</p>
                  <p className="text-xs text-purple-600 dark:text-purple-400 mt-1">
                    Churn: {billingMetrics.churnRate}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-red-600 rounded-xl flex items-center justify-center">
                  <AlertTriangle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-red-900 dark:text-red-100">
                    {billingMetrics.failedPayments}
                  </p>
                  <p className="text-sm text-red-600 dark:text-red-400">Failed Payments</p>
                  <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                    {billingMetrics.pendingPayments} pending
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
            <TabsTrigger value="payments">Payment History</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Revenue Chart */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-green-600" />
                    Revenue Trend
                  </CardTitle>
                  <CardDescription>
                    Monthly revenue over the last 6 months
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={revenueData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
                      <Tooltip formatter={(value: number) => [formatCurrency(value), 'Revenue']} />
                      <Line 
                        type="monotone" 
                        dataKey="revenue" 
                        stroke="#10B981" 
                        strokeWidth={3}
                        dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-blue-600" />
                    Subscription Growth
                  </CardTitle>
                  <CardDescription>
                    Active subscriptions over time
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={revenueData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="subscriptions" fill="#3B82F6" radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Failed Payments Alert */}
            {billingMetrics.failedPayments > 0 && (
              <Card className="border-0 shadow-md bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-red-700 dark:text-red-300">
                    <AlertTriangle className="h-5 w-5" />
                    Payment Issues Requiring Attention
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {subscriptions.filter(sub => sub.status === 'past_due' || sub.failedAttempts > 0).map(sub => (
                      <div key={sub.id} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="h-8 w-8 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
                            <XCircle className="h-4 w-4 text-red-600" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">
                              {sub.studioName}
                            </p>
                            <p className="text-sm text-red-600">
                              {sub.failedAttempts} failed attempts • {formatCurrency(sub.amount)} due
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm">
                            <Mail className="h-4 w-4 mr-2" />
                            Send Reminder
                          </Button>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Subscriptions Tab */}
          <TabsContent value="subscriptions" className="space-y-6">
            {/* Filters */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                      <Input
                        placeholder="Search subscriptions by studio name or plan..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 h-11 bg-gray-50 dark:bg-gray-700"
                      />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="all">All Status</option>
                      <option value="active">Active</option>
                      <option value="past_due">Past Due</option>
                      <option value="canceled">Canceled</option>
                      <option value="trialing">Trialing</option>
                    </select>
                    <select
                      value={planFilter}
                      onChange={(e) => setPlanFilter(e.target.value)}
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="all">All Plans</option>
                      <option value="starter">Starter</option>
                      <option value="professional">Professional</option>
                      <option value="enterprise">Enterprise</option>
                      <option value="custom">Custom</option>
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Subscriptions List */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="space-y-4">
                  {filteredSubscriptions.map(subscription => (
                    <div key={subscription.id} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-semibold">
                            {subscription.studioName.charAt(0)}
                          </div>
                          <div>
                            <div className="flex items-center gap-2">
                              <h3 className="font-semibold text-gray-900 dark:text-white">
                                {subscription.studioName}
                              </h3>
                              <Badge variant={getStatusColor(subscription.status) as any}>
                                {subscription.status.replace('_', ' ')}
                              </Badge>
                              <Badge className={cn("text-xs", getPlanColor(subscription.planType))}>
                                {subscription.planName}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mt-1">
                              <span>{formatCurrency(subscription.amount)}/month</span>
                              <span>•</span>
                              <span>{subscription.paymentMethod}</span>
                              <span>•</span>
                              <span>Next billing: {formatDate(subscription.nextBillingDate)}</span>
                              {subscription.failedAttempts > 0 && (
                                <>
                                  <span>•</span>
                                  <span className="text-red-600">{subscription.failedAttempts} failed attempts</span>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            View
                          </Button>
                          <Button variant="outline" size="sm">
                            <FileText className="h-4 w-4 mr-2" />
                            Invoice
                          </Button>
                          <Button variant="outline" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Payments Tab */}
          <TabsContent value="payments" className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5 text-green-600" />
                  Payment History
                </CardTitle>
                <CardDescription>
                  Recent payment transactions and their status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {payments.map(payment => (
                    <div key={payment.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className={cn(
                          "h-10 w-10 rounded-lg flex items-center justify-center",
                          payment.status === 'succeeded' ? 'bg-green-100 dark:bg-green-900/20' :
                          payment.status === 'failed' ? 'bg-red-100 dark:bg-red-900/20' :
                          payment.status === 'pending' ? 'bg-yellow-100 dark:bg-yellow-900/20' :
                          'bg-gray-100 dark:bg-gray-700'
                        )}>
                          {payment.status === 'succeeded' ? (
                            <CheckCircle className="h-5 w-5 text-green-600" />
                          ) : payment.status === 'failed' ? (
                            <XCircle className="h-5 w-5 text-red-600" />
                          ) : payment.status === 'pending' ? (
                            <Clock className="h-5 w-5 text-yellow-600" />
                          ) : (
                            <CreditCard className="h-5 w-5 text-gray-600" />
                          )}
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <p className="font-medium text-gray-900 dark:text-white">
                              {payment.studioName}
                            </p>
                            <Badge variant={getStatusColor(payment.status) as any}>
                              {payment.status}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                            <span>{formatCurrency(payment.amount)}</span>
                            <span>•</span>
                            <span>{payment.paymentMethod}</span>
                            <span>•</span>
                            <span>{formatDate(payment.createdAt)}</span>
                            {payment.failureReason && (
                              <>
                                <span>•</span>
                                <span className="text-red-600">{payment.failureReason}</span>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          Details
                        </Button>
                        {payment.status === 'succeeded' && (
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4 mr-2" />
                            Receipt
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-lg">Plan Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {['enterprise', 'professional', 'starter'].map(plan => {
                      const count = subscriptions.filter(s => s.planType === plan).length;
                      const percentage = (count / subscriptions.length) * 100;
                      return (
                        <div key={plan} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className={cn("h-3 w-3 rounded-full", getPlanColor(plan))} />
                            <span className="text-sm font-medium capitalize">{plan}</span>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-bold">{count}</p>
                            <p className="text-xs text-gray-500">{percentage.toFixed(1)}%</p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-lg">Payment Success Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600 mb-2">
                      {((payments.filter(p => p.status === 'succeeded').length / payments.length) * 100).toFixed(1)}%
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {payments.filter(p => p.status === 'succeeded').length} of {payments.length} payments successful
                    </p>
                    <Progress 
                      value={(payments.filter(p => p.status === 'succeeded').length / payments.length) * 100} 
                      className="mt-4"
                    />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-lg">Revenue Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {['enterprise', 'professional', 'starter'].map(plan => {
                      const planSubs = subscriptions.filter(s => s.planType === plan && s.status === 'active');
                      const revenue = planSubs.reduce((sum, s) => sum + s.amount, 0);
                      return (
                        <div key={plan} className="flex items-center justify-between">
                          <span className="text-sm font-medium capitalize">{plan}</span>
                          <span className="text-sm font-bold">{formatCurrency(revenue)}</span>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
}
