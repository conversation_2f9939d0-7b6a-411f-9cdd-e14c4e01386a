"use client";

import { useState } from "react";
import { 
  Activity,
  Server,
  Database,
  Cpu,
  HardDrive,
  Wifi,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Settings,
  Eye,
  BarChart3,
  Monitor
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { LineChart, Line, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import AdminLayout from "@/components/admin/AdminLayout";
import { formatDate, cn } from "@/lib/utils";

interface SystemMetrics {
  status: 'healthy' | 'warning' | 'critical';
  uptime: number;
  responseTime: number;
  throughput: number;
  errorRate: number;
  activeUsers: number;
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  networkIn: number;
  networkOut: number;
  databaseConnections: number;
  queueSize: number;
}

interface ServiceStatus {
  name: string;
  status: 'online' | 'offline' | 'degraded';
  responseTime: number;
  uptime: number;
  lastCheck: string;
  endpoint: string;
}

export default function SystemMonitoringPage() {
  const [refreshInterval, setRefreshInterval] = useState("30");
  const [isAutoRefresh, setIsAutoRefresh] = useState(true);
  
  const [systemMetrics] = useState<SystemMetrics>({
    status: 'healthy',
    uptime: 99.97,
    responseTime: 245,
    throughput: 1250,
    errorRate: 0.12,
    activeUsers: 1847,
    cpuUsage: 68.5,
    memoryUsage: 72.3,
    diskUsage: 45.8,
    networkIn: 125.6,
    networkOut: 89.3,
    databaseConnections: 45,
    queueSize: 12
  });

  const [services] = useState<ServiceStatus[]>([
    {
      name: 'API Gateway',
      status: 'online',
      responseTime: 120,
      uptime: 99.98,
      lastCheck: '2024-01-20T10:30:00Z',
      endpoint: '/api/health'
    },
    {
      name: 'Authentication Service',
      status: 'online',
      responseTime: 85,
      uptime: 99.95,
      lastCheck: '2024-01-20T10:30:00Z',
      endpoint: '/auth/health'
    },
    {
      name: 'Photo Processing',
      status: 'degraded',
      responseTime: 450,
      uptime: 98.2,
      lastCheck: '2024-01-20T10:29:45Z',
      endpoint: '/process/health'
    },
    {
      name: 'Database Primary',
      status: 'online',
      responseTime: 15,
      uptime: 99.99,
      lastCheck: '2024-01-20T10:30:00Z',
      endpoint: '/db/health'
    },
    {
      name: 'File Storage',
      status: 'online',
      responseTime: 200,
      uptime: 99.85,
      lastCheck: '2024-01-20T10:30:00Z',
      endpoint: '/storage/health'
    },
    {
      name: 'Email Service',
      status: 'offline',
      responseTime: 0,
      uptime: 95.2,
      lastCheck: '2024-01-20T10:25:30Z',
      endpoint: '/email/health'
    }
  ]);

  const [performanceData] = useState([
    { time: '10:00', cpu: 45, memory: 62, responseTime: 180 },
    { time: '10:05', cpu: 52, memory: 65, responseTime: 195 },
    { time: '10:10', cpu: 48, memory: 68, responseTime: 210 },
    { time: '10:15', cpu: 65, memory: 70, responseTime: 235 },
    { time: '10:20', cpu: 72, memory: 72, responseTime: 245 },
    { time: '10:25', cpu: 68, memory: 74, responseTime: 220 },
    { time: '10:30', cpu: 69, memory: 72, responseTime: 245 }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
      case 'healthy': return 'success';
      case 'degraded':
      case 'warning': return 'warning';
      case 'offline':
      case 'critical': return 'destructive';
      default: return 'secondary';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
      case 'healthy': return CheckCircle;
      case 'degraded':
      case 'warning': return AlertTriangle;
      case 'offline':
      case 'critical': return AlertTriangle;
      default: return Clock;
    }
  };

  const getUsageColor = (usage: number) => {
    if (usage >= 90) return 'destructive';
    if (usage >= 75) return 'warning';
    return 'default';
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <Activity className="h-8 w-8 text-green-600" />
              System Monitoring
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Real-time system health, performance metrics, and service status
            </p>
          </div>
          <div className="flex items-center gap-3">
            <select
              value={refreshInterval}
              onChange={(e) => setRefreshInterval(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="10">10 seconds</option>
              <option value="30">30 seconds</option>
              <option value="60">1 minute</option>
              <option value="300">5 minutes</option>
            </select>
            <Button
              variant={isAutoRefresh ? "default" : "outline"}
              size="sm"
              onClick={() => setIsAutoRefresh(!isAutoRefresh)}
            >
              <RefreshCw className={cn("h-4 w-4 mr-2", isAutoRefresh && "animate-spin")} />
              Auto Refresh
            </Button>
          </div>
        </div>

        {/* System Status Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className={cn(
            "border-0 shadow-md",
            systemMetrics.status === 'healthy' 
              ? "bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20"
              : systemMetrics.status === 'warning'
                ? "bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20"
                : "bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20"
          )}>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className={cn(
                  "h-12 w-12 rounded-xl flex items-center justify-center",
                  systemMetrics.status === 'healthy' ? "bg-green-600" :
                  systemMetrics.status === 'warning' ? "bg-yellow-600" : "bg-red-600"
                )}>
                  <CheckCircle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className={cn(
                    "text-2xl font-bold",
                    systemMetrics.status === 'healthy' ? "text-green-900 dark:text-green-100" :
                    systemMetrics.status === 'warning' ? "text-yellow-900 dark:text-yellow-100" :
                    "text-red-900 dark:text-red-100"
                  )}>
                    {systemMetrics.uptime}%
                  </p>
                  <p className={cn(
                    "text-sm",
                    systemMetrics.status === 'healthy' ? "text-green-600 dark:text-green-400" :
                    systemMetrics.status === 'warning' ? "text-yellow-600 dark:text-yellow-400" :
                    "text-red-600 dark:text-red-400"
                  )}>
                    System Uptime
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <Zap className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {systemMetrics.responseTime}ms
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Avg Response Time</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {systemMetrics.throughput}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Requests/min</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-orange-600 rounded-xl flex items-center justify-center">
                  <AlertTriangle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {systemMetrics.errorRate}%
                  </p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">Error Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Performance Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* System Resources */}
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Cpu className="h-5 w-5 text-blue-600" />
                System Resources
              </CardTitle>
              <CardDescription>
                CPU, Memory, and Response Time trends
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={performanceData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis />
                  <Tooltip />
                  <Line 
                    type="monotone" 
                    dataKey="cpu" 
                    stroke="#3B82F6" 
                    strokeWidth={2}
                    name="CPU %"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="memory" 
                    stroke="#10B981" 
                    strokeWidth={2}
                    name="Memory %"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Response Time */}
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-green-600" />
                Response Time Trends
              </CardTitle>
              <CardDescription>
                API response time over the last hour
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={performanceData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis />
                  <Tooltip formatter={[(value: number) => `${value}ms`, 'Response Time']} />
                  <Area 
                    type="monotone" 
                    dataKey="responseTime" 
                    stroke="#10B981" 
                    fill="#10B981" 
                    fillOpacity={0.6}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* Resource Usage */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="h-5 w-5 text-purple-600" />
              Resource Usage
            </CardTitle>
            <CardDescription>
              Current system resource utilization
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Cpu className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">CPU Usage</span>
                  </div>
                  <span className="text-sm font-bold text-gray-900 dark:text-white">
                    {systemMetrics.cpuUsage}%
                  </span>
                </div>
                <Progress 
                  value={systemMetrics.cpuUsage} 
                  className="h-3"
                  variant={getUsageColor(systemMetrics.cpuUsage)}
                />
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Memory Usage</span>
                  </div>
                  <span className="text-sm font-bold text-gray-900 dark:text-white">
                    {systemMetrics.memoryUsage}%
                  </span>
                </div>
                <Progress 
                  value={systemMetrics.memoryUsage} 
                  className="h-3"
                  variant={getUsageColor(systemMetrics.memoryUsage)}
                />
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <HardDrive className="h-4 w-4 text-purple-600" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Disk Usage</span>
                  </div>
                  <span className="text-sm font-bold text-gray-900 dark:text-white">
                    {systemMetrics.diskUsage}%
                  </span>
                </div>
                <Progress 
                  value={systemMetrics.diskUsage} 
                  className="h-3"
                  variant={getUsageColor(systemMetrics.diskUsage)}
                />
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Wifi className="h-4 w-4 text-orange-600" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Network I/O</span>
                  </div>
                  <span className="text-sm font-bold text-gray-900 dark:text-white">
                    {systemMetrics.networkIn + systemMetrics.networkOut} MB/s
                  </span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-500">In: {systemMetrics.networkIn} MB/s</span>
                    <span className="text-gray-500">Out: {systemMetrics.networkOut} MB/s</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Service Status */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Server className="h-5 w-5 text-blue-600" />
              Service Status
            </CardTitle>
            <CardDescription>
              Health status of all system services
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {services.map((service) => {
                const StatusIcon = getStatusIcon(service.status);
                return (
                  <div key={service.name} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className={cn(
                        "h-10 w-10 rounded-lg flex items-center justify-center",
                        service.status === 'online' ? 'bg-green-100 dark:bg-green-900/20' :
                        service.status === 'degraded' ? 'bg-yellow-100 dark:bg-yellow-900/20' :
                        'bg-red-100 dark:bg-red-900/20'
                      )}>
                        <StatusIcon className={cn(
                          "h-5 w-5",
                          service.status === 'online' ? 'text-green-600' :
                          service.status === 'degraded' ? 'text-yellow-600' :
                          'text-red-600'
                        )} />
                      </div>
                      <div>
                        <p className="font-semibold text-gray-900 dark:text-white">
                          {service.name}
                        </p>
                        <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                          <span>{service.endpoint}</span>
                          <span>•</span>
                          <span>Uptime: {service.uptime}%</span>
                          <span>•</span>
                          <span>Last check: {new Date(service.lastCheck).toLocaleTimeString()}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <p className="font-bold text-gray-900 dark:text-white">
                          {service.responseTime}ms
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Response Time
                        </p>
                      </div>
                      <Badge variant={getStatusColor(service.status) as any}>
                        {service.status}
                      </Badge>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-2" />
                        Details
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Additional Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <Database className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {systemMetrics.databaseConnections}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">DB Connections</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <Activity className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {systemMetrics.activeUsers.toLocaleString()}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Active Users</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <Clock className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {systemMetrics.queueSize}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Queue Size</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
}
