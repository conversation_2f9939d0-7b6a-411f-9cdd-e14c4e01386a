"use client";

import { useState } from "react";
import { 
  MessageCircle,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  MessageSquare,
  Clock,
  CheckCircle,
  AlertTriangle,
  User,
  Building2,
  Calendar,
  Tag,
  Star,
  TrendingUp,
  Phone,
  Mail,
  Headphones,
  Users,
  Activity
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import AdminLayout from "@/components/admin/AdminLayout";
import { formatDate, timeAgo, cn } from "@/lib/utils";

interface SupportTicket {
  id: string;
  title: string;
  description: string;
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'technical' | 'billing' | 'feature_request' | 'bug_report' | 'general';
  userId: string;
  userName: string;
  userEmail: string;
  studioName?: string;
  assignedTo?: string;
  createdAt: string;
  updatedAt: string;
  responseTime?: number;
  resolutionTime?: number;
  rating?: number;
  tags: string[];
}

export default function SupportPage() {
  const [tickets] = useState<SupportTicket[]>([
    {
      id: 'TICK-001',
      title: 'Unable to upload photos - getting timeout error',
      description: 'When I try to upload more than 10 photos at once, the system times out and shows an error message.',
      status: 'open',
      priority: 'high',
      category: 'technical',
      userId: 'user1',
      userName: 'Sarah Johnson',
      userEmail: '<EMAIL>',
      studioName: 'PhotoStudio Pro',
      createdAt: '2024-01-20T09:30:00Z',
      updatedAt: '2024-01-20T09:30:00Z',
      tags: ['upload', 'timeout', 'bulk']
    },
    {
      id: 'TICK-002',
      title: 'Billing question about subscription upgrade',
      description: 'I want to upgrade from Professional to Enterprise plan. What happens to my current billing cycle?',
      status: 'in_progress',
      priority: 'medium',
      category: 'billing',
      userId: 'user2',
      userName: 'Mike Davis',
      userEmail: '<EMAIL>',
      studioName: 'Wedding Memories',
      assignedTo: 'Support Agent 1',
      createdAt: '2024-01-19T14:20:00Z',
      updatedAt: '2024-01-20T08:15:00Z',
      responseTime: 120,
      tags: ['billing', 'upgrade', 'enterprise']
    },
    {
      id: 'TICK-003',
      title: 'Feature request: Bulk photo tagging',
      description: 'It would be great to have a feature to tag multiple photos at once instead of doing it one by one.',
      status: 'resolved',
      priority: 'low',
      category: 'feature_request',
      userId: 'user3',
      userName: 'Emma Wilson',
      userEmail: '<EMAIL>',
      studioName: 'Quick Shots',
      assignedTo: 'Product Team',
      createdAt: '2024-01-18T11:45:00Z',
      updatedAt: '2024-01-19T16:30:00Z',
      responseTime: 45,
      resolutionTime: 1725,
      rating: 5,
      tags: ['feature', 'tagging', 'bulk-operations']
    },
    {
      id: 'TICK-004',
      title: 'Face recognition not working properly',
      description: 'The face recognition feature is not detecting faces in some of my wedding photos. The photos are high quality.',
      status: 'open',
      priority: 'urgent',
      category: 'bug_report',
      userId: 'user4',
      userName: 'David Chen',
      userEmail: '<EMAIL>',
      studioName: 'Elite Photography',
      createdAt: '2024-01-20T07:15:00Z',
      updatedAt: '2024-01-20T07:15:00Z',
      tags: ['face-recognition', 'ai', 'wedding']
    }
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [priorityFilter, setPriorityFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");

  const filteredTickets = tickets.filter(ticket => {
    const matchesSearch = ticket.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.id.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || ticket.status === statusFilter;
    const matchesPriority = priorityFilter === "all" || ticket.priority === priorityFilter;
    const matchesCategory = categoryFilter === "all" || ticket.category === categoryFilter;
    
    return matchesSearch && matchesStatus && matchesPriority && matchesCategory;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'destructive';
      case 'in_progress': return 'warning';
      case 'resolved': return 'success';
      case 'closed': return 'secondary';
      default: return 'secondary';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'destructive';
      case 'high': return 'warning';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'secondary';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'technical': return AlertTriangle;
      case 'billing': return MessageCircle;
      case 'feature_request': return Star;
      case 'bug_report': return AlertTriangle;
      case 'general': return MessageSquare;
      default: return MessageSquare;
    }
  };

  const totalTickets = tickets.length;
  const openTickets = tickets.filter(t => t.status === 'open').length;
  const inProgressTickets = tickets.filter(t => t.status === 'in_progress').length;
  const resolvedTickets = tickets.filter(t => t.status === 'resolved').length;
  const avgResponseTime = tickets.filter(t => t.responseTime).reduce((sum, t) => sum + (t.responseTime || 0), 0) / tickets.filter(t => t.responseTime).length || 0;
  const avgRating = tickets.filter(t => t.rating).reduce((sum, t) => sum + (t.rating || 0), 0) / tickets.filter(t => t.rating).length || 0;

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <Headphones className="h-8 w-8 text-blue-600" />
              Support Center
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Manage customer support tickets and user inquiries
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Badge variant="secondary" className="text-sm">
              {totalTickets} Total Tickets
            </Badge>
          </div>
        </div>

        {/* Support Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <MessageCircle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {totalTickets}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Total</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-red-600 rounded-xl flex items-center justify-center">
                  <AlertTriangle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-red-900 dark:text-red-100">
                    {openTickets}
                  </p>
                  <p className="text-sm text-red-600 dark:text-red-400">Open</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-yellow-600 rounded-xl flex items-center justify-center">
                  <Clock className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
                    {inProgressTickets}
                  </p>
                  <p className="text-sm text-yellow-600 dark:text-yellow-400">In Progress</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {resolvedTickets}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Resolved</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {Math.round(avgResponseTime)}m
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Avg Response</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-orange-600 rounded-xl flex items-center justify-center">
                  <Star className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {avgRating.toFixed(1)}
                  </p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">Avg Rating</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Support Tickets */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <MessageCircle className="h-5 w-5 text-blue-600" />
                  Support Tickets
                </CardTitle>
                <CardDescription>
                  Manage and respond to customer support requests
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Filters */}
            <div className="flex flex-col lg:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    placeholder="Search tickets by title, user, or ticket ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 h-11 bg-gray-50 dark:bg-gray-700"
                  />
                </div>
              </div>
              <div className="flex flex-wrap gap-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">All Status</option>
                  <option value="open">Open</option>
                  <option value="in_progress">In Progress</option>
                  <option value="resolved">Resolved</option>
                  <option value="closed">Closed</option>
                </select>
                <select
                  value={priorityFilter}
                  onChange={(e) => setPriorityFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">All Priority</option>
                  <option value="urgent">Urgent</option>
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                </select>
                <select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">All Categories</option>
                  <option value="technical">Technical</option>
                  <option value="billing">Billing</option>
                  <option value="feature_request">Feature Request</option>
                  <option value="bug_report">Bug Report</option>
                  <option value="general">General</option>
                </select>
              </div>
            </div>

            {/* Tickets List */}
            <div className="space-y-4">
              {filteredTickets.map((ticket) => {
                const CategoryIcon = getCategoryIcon(ticket.category);
                return (
                  <Card key={ticket.id} className="border border-gray-200 dark:border-gray-600 hover:shadow-lg transition-all duration-200">
                    <CardContent className="p-6">
                      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
                        <div className="flex-1 space-y-4">
                          {/* Ticket Header */}
                          <div className="flex items-start justify-between">
                            <div className="flex items-center gap-4">
                              <div className={cn(
                                "h-10 w-10 rounded-lg flex items-center justify-center",
                                ticket.category === 'technical' ? 'bg-red-100 dark:bg-red-900/20' :
                                ticket.category === 'billing' ? 'bg-blue-100 dark:bg-blue-900/20' :
                                ticket.category === 'feature_request' ? 'bg-purple-100 dark:bg-purple-900/20' :
                                ticket.category === 'bug_report' ? 'bg-orange-100 dark:bg-orange-900/20' :
                                'bg-gray-100 dark:bg-gray-700'
                              )}>
                                <CategoryIcon className={cn(
                                  "h-5 w-5",
                                  ticket.category === 'technical' ? 'text-red-600' :
                                  ticket.category === 'billing' ? 'text-blue-600' :
                                  ticket.category === 'feature_request' ? 'text-purple-600' :
                                  ticket.category === 'bug_report' ? 'text-orange-600' :
                                  'text-gray-600'
                                )} />
                              </div>
                              <div>
                                <div className="flex items-center gap-2">
                                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                    {ticket.title}
                                  </h3>
                                  <Badge variant="secondary" className="text-xs">
                                    {ticket.id}
                                  </Badge>
                                </div>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                  {ticket.description.substring(0, 100)}...
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant={getStatusColor(ticket.status) as any}>
                                {ticket.status.replace('_', ' ')}
                              </Badge>
                              <Badge variant={getPriorityColor(ticket.priority) as any}>
                                {ticket.priority}
                              </Badge>
                            </div>
                          </div>

                          {/* User & Studio Info */}
                          <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 dark:text-gray-400">
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4" />
                              <span>{ticket.userName}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Mail className="h-4 w-4" />
                              <span>{ticket.userEmail}</span>
                            </div>
                            {ticket.studioName && (
                              <div className="flex items-center gap-2">
                                <Building2 className="h-4 w-4" />
                                <span>{ticket.studioName}</span>
                              </div>
                            )}
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4" />
                              <span>Created {timeAgo(ticket.createdAt)}</span>
                            </div>
                            {ticket.assignedTo && (
                              <div className="flex items-center gap-2">
                                <Users className="h-4 w-4" />
                                <span>Assigned to {ticket.assignedTo}</span>
                              </div>
                            )}
                          </div>

                          {/* Tags */}
                          <div className="flex flex-wrap gap-2">
                            {ticket.tags.map((tag, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                <Tag className="h-3 w-3 mr-1" />
                                {tag}
                              </Badge>
                            ))}
                          </div>

                          {/* Performance Metrics */}
                          {(ticket.responseTime || ticket.resolutionTime || ticket.rating) && (
                            <div className="flex items-center gap-6 text-sm text-gray-600 dark:text-gray-400">
                              {ticket.responseTime && (
                                <div className="flex items-center gap-1">
                                  <Clock className="h-4 w-4" />
                                  <span>Response: {ticket.responseTime}m</span>
                                </div>
                              )}
                              {ticket.resolutionTime && (
                                <div className="flex items-center gap-1">
                                  <CheckCircle className="h-4 w-4" />
                                  <span>Resolution: {Math.round(ticket.resolutionTime / 60)}h</span>
                                </div>
                              )}
                              {ticket.rating && (
                                <div className="flex items-center gap-1">
                                  <Star className="h-4 w-4 text-yellow-500" />
                                  <span>Rating: {ticket.rating}/5</span>
                                </div>
                              )}
                            </div>
                          )}
                        </div>

                        {/* Action Buttons */}
                        <div className="flex flex-col gap-2 lg:flex-row lg:items-center">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </Button>
                          <Button variant="outline" size="sm">
                            <MessageSquare className="h-4 w-4 mr-2" />
                            Reply
                          </Button>
                          <Button variant="outline" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* Empty State */}
            {filteredTickets.length === 0 && (
              <div className="text-center py-12">
                <MessageCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  No tickets found
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                  Try adjusting your search or filter criteria.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
