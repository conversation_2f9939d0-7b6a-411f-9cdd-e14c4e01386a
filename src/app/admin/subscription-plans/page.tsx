"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  DollarSign,
  Plus,
  Edit,
  Trash2,
  Shield,
  HardDrive,
  Users,
  Check,
  X,
  CreditCard,
  Star,
  Crown,
  Zap
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import AdminLayout from "@/components/admin/AdminLayout";
import { formatCurrency, formatFileSize, cn } from "@/lib/utils";

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  billingCycle: string;
  storageLimit: string;
  downloadQuality: string;
  customBranding: boolean;
  watermark: boolean;
  videoSupport: boolean;
  isActive: boolean;
  _count: {
    subscriptions: number;
  };
}

export default function AdminSubscriptionPlansPage() {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingPlan, setEditingPlan] = useState<SubscriptionPlan | null>(null);
  const router = useRouter();

  const [formData, setFormData] = useState({
    name: "",
    price: "",
    billingCycle: "monthly",
    storageLimit: "",
    downloadQuality: "medium",
    customBranding: false,
    watermark: false,
    videoSupport: false,
  });

  const fetchPlans = async () => {
    try {
      const response = await fetch("/api/admin/subscription-plans", {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      });

      const data = await response.json();
      if (data.success) {
        setPlans(data.data);
      }
    } catch (error) {
      console.error("Error fetching plans:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPlans();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const url = editingPlan ? "/api/admin/subscription-plans" : "/api/admin/subscription-plans";
      const method = editingPlan ? "PUT" : "POST";
      
      const payload = {
        ...formData,
        price: parseFloat(formData.price),
        storageLimit: parseInt(formData.storageLimit) * 1024 * 1024 * 1024, // Convert GB to bytes
        ...(editingPlan && { id: editingPlan.id }),
      };

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();
      if (data.success) {
        fetchPlans();
        resetForm();
      }
    } catch (error) {
      console.error("Error saving plan:", error);
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      price: "",
      billingCycle: "monthly",
      storageLimit: "",
      downloadQuality: "medium",
      customBranding: false,
      watermark: false,
      videoSupport: false,
    });
    setShowCreateForm(false);
    setEditingPlan(null);
  };

  const handleEdit = (plan: SubscriptionPlan) => {
    setFormData({
      name: plan.name,
      price: plan.price.toString(),
      billingCycle: plan.billingCycle,
      storageLimit: (Number(plan.storageLimit) / (1024 * 1024 * 1024)).toString(),
      downloadQuality: plan.downloadQuality,
      customBranding: plan.customBranding,
      watermark: plan.watermark,
      videoSupport: plan.videoSupport,
    });
    setEditingPlan(plan);
    setShowCreateForm(true);
  };

  const getPlanIcon = (planName: string) => {
    const name = planName.toLowerCase();
    if (name.includes('basic') || name.includes('starter')) return Star;
    if (name.includes('pro') || name.includes('premium')) return Crown;
    if (name.includes('enterprise') || name.includes('business')) return Zap;
    return CreditCard;
  };

  const getPlanColor = (planName: string) => {
    const name = planName.toLowerCase();
    if (name.includes('basic') || name.includes('starter')) return 'from-blue-500 to-blue-600';
    if (name.includes('pro') || name.includes('premium')) return 'from-purple-500 to-purple-600';
    if (name.includes('enterprise') || name.includes('business')) return 'from-orange-500 to-orange-600';
    return 'from-gray-500 to-gray-600';
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <CreditCard className="h-8 w-8 text-blue-600" />
              Subscription Plans
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Create and manage subscription plans for your photo studios
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Badge variant="secondary" className="text-sm">
              {plans.length} Plans
            </Badge>
            <Button
              onClick={() => setShowCreateForm(true)}
              className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Plan
            </Button>
          </div>
        </div>

        {/* Create/Edit Form */}
        {showCreateForm && (
          <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-600 rounded-t-lg">
              <CardTitle className="text-xl text-gray-900 dark:text-white">
                {editingPlan ? "Edit Subscription Plan" : "Create New Subscription Plan"}
              </CardTitle>
              <CardDescription className="text-gray-600 dark:text-gray-300">
                {editingPlan ? "Update the plan details below" : "Configure your new subscription plan with pricing and features"}
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Basic Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white border-b pb-2">
                    Basic Information
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Plan Name *
                      </label>
                      <Input
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                        placeholder="e.g., Basic, Pro, Enterprise"
                        className="h-11"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Price (INR) *
                      </label>
                      <Input
                        type="number"
                        value={formData.price}
                        onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                        placeholder="999"
                        className="h-11"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Billing Cycle *
                      </label>
                      <select
                        value={formData.billingCycle}
                        onChange={(e) => setFormData({ ...formData, billingCycle: e.target.value })}
                        className="w-full h-11 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="monthly">Monthly</option>
                        <option value="yearly">Yearly</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Storage Limit (GB) *
                      </label>
                      <Input
                        type="number"
                        value={formData.storageLimit}
                        onChange={(e) => setFormData({ ...formData, storageLimit: e.target.value })}
                        placeholder="50"
                        className="h-11"
                        required
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Download Quality
                      </label>
                      <select
                        value={formData.downloadQuality}
                        onChange={(e) => setFormData({ ...formData, downloadQuality: e.target.value })}
                        className="w-full h-11 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="low">Low Quality</option>
                        <option value="medium">Medium Quality</option>
                        <option value="high">High Quality</option>
                        <option value="original">Original Quality</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* Features */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white border-b pb-2">
                    Features
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <label className="flex items-center space-x-3 p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.customBranding}
                        onChange={(e) => setFormData({ ...formData, customBranding: e.target.checked })}
                        className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                      />
                      <div>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">Custom Branding</span>
                        <p className="text-xs text-gray-500 dark:text-gray-400">Allow custom logos and branding</p>
                      </div>
                    </label>
                    <label className="flex items-center space-x-3 p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.watermark}
                        onChange={(e) => setFormData({ ...formData, watermark: e.target.checked })}
                        className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                      />
                      <div>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">Watermark</span>
                        <p className="text-xs text-gray-500 dark:text-gray-400">Add watermark to photos</p>
                      </div>
                    </label>
                    <label className="flex items-center space-x-3 p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.videoSupport}
                        onChange={(e) => setFormData({ ...formData, videoSupport: e.target.checked })}
                        className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                      />
                      <div>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">Video Support</span>
                        <p className="text-xs text-gray-500 dark:text-gray-400">Support video uploads</p>
                      </div>
                    </label>
                  </div>
                </div>

                <div className="flex gap-3 pt-4 border-t">
                  <Button
                    type="submit"
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
                  >
                    {editingPlan ? "Update Plan" : "Create Plan"}
                  </Button>
                  <Button type="button" variant="outline" onClick={resetForm}>
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* Plans Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {plans.map((plan) => {
            const PlanIcon = getPlanIcon(plan.name);
            const planColor = getPlanColor(plan.name);

            return (
              <Card
                key={plan.id}
                className={cn(
                  "relative border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white dark:bg-gray-800",
                  !plan.isActive && "opacity-60 grayscale"
                )}
              >
                {/* Plan Header */}
                <CardHeader className="pb-4">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-3">
                      <div className={cn("h-12 w-12 rounded-xl flex items-center justify-center bg-gradient-to-br", planColor)}>
                        <PlanIcon className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-xl text-gray-900 dark:text-white">
                          {plan.name}
                        </CardTitle>
                        <div className="flex items-center gap-2">
                          <span className="text-2xl font-bold text-gray-900 dark:text-white">
                            {formatCurrency(plan.price)}
                          </span>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            /{plan.billingCycle}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEdit(plan)}
                        className="hover:bg-blue-50 hover:border-blue-200 hover:text-blue-600 dark:hover:bg-blue-900/20"
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  {!plan.isActive && (
                    <Badge variant="destructive" className="absolute top-4 right-4">
                      Inactive
                    </Badge>
                  )}
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Storage & Quality */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <HardDrive className="w-5 h-5 text-blue-600" />
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {formatFileSize(Number(plan.storageLimit))} Storage
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Total storage limit
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <Users className="w-5 h-5 text-green-600" />
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {plan.downloadQuality.charAt(0).toUpperCase() + plan.downloadQuality.slice(1)} Quality
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Download quality
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Features */}
                  <div className="space-y-2">
                    <h4 className="text-sm font-semibold text-gray-900 dark:text-white">Features</h4>
                    <div className="space-y-2">
                      {[
                        { key: 'customBranding', label: 'Custom Branding', enabled: plan.customBranding },
                        { key: 'watermark', label: 'Watermark', enabled: plan.watermark },
                        { key: 'videoSupport', label: 'Video Support', enabled: plan.videoSupport }
                      ].map((feature) => (
                        <div key={feature.key} className="flex items-center gap-2">
                          {feature.enabled ? (
                            <Check className="w-4 h-4 text-green-500" />
                          ) : (
                            <X className="w-4 h-4 text-red-500" />
                          )}
                          <span className={cn(
                            "text-sm",
                            feature.enabled
                              ? "text-gray-900 dark:text-white"
                              : "text-gray-500 dark:text-gray-400"
                          )}>
                            {feature.label}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Subscription Count */}
                  <div className="pt-4 border-t border-gray-200 dark:border-gray-600">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Active Subscriptions
                      </span>
                      <Badge variant="secondary" className="font-semibold">
                        {plan._count.subscriptions}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Empty State */}
        {plans.length === 0 && !loading && (
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardContent className="p-12 text-center">
              <CreditCard className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                No subscription plans found
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                Create your first subscription plan to get started.
              </p>
              <Button
                onClick={() => setShowCreateForm(true)}
                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Plan
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </AdminLayout>
  );
}
