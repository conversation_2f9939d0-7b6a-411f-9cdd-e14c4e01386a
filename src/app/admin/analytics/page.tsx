"use client";

import { useState } from "react";
import {
  Bar<PERSON>hart3,
  TrendingUp,
  TrendingDown,
  Users,
  Building2,
  Camera,
  DollarSign,
  Calendar,
  Download,
  Eye,
  Share2,
  Filter,
  RefreshCw,
  Settings,
  FileText,
  Activity,
  Zap,
  Clock,
  Target,
  Award,
  Globe,
  Smartphone
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import AdminLayout from "@/components/admin/AdminLayout";
import { formatDate, formatCurrency, cn } from "@/lib/utils";

interface PlatformMetrics {
  totalStudios: number;
  activeStudios: number;
  totalClients: number;
  totalPhotos: number;
  totalRevenue: number;
  monthlyGrowth: number;
  storageUsed: number;
  avgPhotosPerStudio: number;
}

interface ConversionMetrics {
  period: string;
  leads: number;
  signups: number;
  conversions: number;
  conversionRate: number;
  revenue: number;
}

interface ClientInteraction {
  studioId: string;
  studioName: string;
  totalInteractions: number;
  photoViews: number;
  downloads: number;
  shares: number;
  favorites: number;
  avgSessionDuration: number;
  returnRate: number;
}

interface ActivityPattern {
  hour: number;
  uploads: number;
  views: number;
  downloads: number;
  shares: number;
}

interface StudioPerformance {
  studioId: string;
  studioName: string;
  clientCount: number;
  photoCount: number;
  revenue: number;
  growthRate: number;
  lastActivity: string;
  planType: string;
  rating: number;
}

export default function AnalyticsPage() {
  const [activeTab, setActiveTab] = useState("overview");
  const [timeRange, setTimeRange] = useState("30d");

  const [platformMetrics] = useState<PlatformMetrics>({
    totalStudios: 1250,
    activeStudios: 1180,
    totalClients: 45600,
    totalPhotos: 2850000,
    totalRevenue: 2456780,
    monthlyGrowth: 12.5,
    storageUsed: 3500000000000, // 3.5TB
    avgPhotosPerStudio: 2280
  });

  const [revenueData] = useState([
    { month: 'Jan', revenue: 185000, studios: 1050, clients: 38000 },
    { month: 'Feb', revenue: 195000, studios: 1080, clients: 39500 },
    { month: 'Mar', revenue: 208000, studios: 1120, clients: 41200 },
    { month: 'Apr', revenue: 225000, studios: 1150, clients: 42800 },
    { month: 'May', revenue: 238000, studios: 1180, clients: 44100 },
    { month: 'Jun', revenue: 246000, studios: 1200, clients: 45600 }
  ]);

  const [studioGrowthData] = useState([
    { date: '2024-01-01', newStudios: 45, churnedStudios: 8 },
    { date: '2024-01-08', newStudios: 52, churnedStudios: 12 },
    { date: '2024-01-15', newStudios: 38, churnedStudios: 6 },
    { date: '2024-01-22', newStudios: 61, churnedStudios: 15 },
    { date: '2024-01-29', newStudios: 47, churnedStudios: 9 }
  ]);

  const [planDistribution] = useState([
    { name: 'Starter', value: 45, color: '#10B981' },
    { name: 'Professional', value: 35, color: '#3B82F6' },
    { name: 'Enterprise', value: 15, color: '#8B5CF6' },
    { name: 'Custom', value: 5, color: '#F59E0B' }
  ]);

  const [topStudios] = useState<StudioPerformance[]>([
    {
      studioId: '1',
      studioName: 'PhotoStudio Pro',
      clientCount: 245,
      photoCount: 18500,
      revenue: 15600,
      growthRate: 28.5,
      lastActivity: '2024-01-20T14:30:00Z',
      planType: 'Enterprise',
      rating: 4.9
    },
    {
      studioId: '2',
      studioName: 'Wedding Memories',
      clientCount: 189,
      photoCount: 14200,
      revenue: 12800,
      growthRate: 22.1,
      lastActivity: '2024-01-20T11:15:00Z',
      planType: 'Professional',
      rating: 4.8
    },
    {
      studioId: '3',
      studioName: 'Portrait Masters',
      clientCount: 156,
      photoCount: 11800,
      revenue: 9800,
      growthRate: 18.7,
      lastActivity: '2024-01-19T16:45:00Z',
      planType: 'Professional',
      rating: 4.7
    }
  ]);

  const [usageMetrics] = useState([
    { feature: 'Photo Upload', usage: 95, trend: 'up' },
    { feature: 'Client Galleries', usage: 88, trend: 'up' },
    { feature: 'Contract Management', usage: 72, trend: 'stable' },
    { feature: 'Payment Processing', usage: 68, trend: 'up' },
    { feature: 'AI Photo Tagging', usage: 45, trend: 'up' },
    { feature: 'Video Storage', usage: 32, trend: 'down' }
  ]);

  const [deviceStats] = useState([
    { device: 'Desktop', percentage: 65, users: 29640 },
    { device: 'Mobile', percentage: 28, users: 12768 },
    { device: 'Tablet', percentage: 7, users: 3192 }
  ]);

  const [geographicData] = useState([
    { region: 'North America', studios: 485, revenue: 980000 },
    { region: 'Europe', studios: 320, revenue: 640000 },
    { region: 'Asia Pacific', studios: 285, revenue: 570000 },
    { region: 'Latin America', studios: 95, revenue: 190000 },
    { region: 'Others', usage: 65, revenue: 130000 }
  ]);

  const [conversionMetrics] = useState<ConversionMetrics[]>([
    { period: 'Jan', leads: 1250, signups: 485, conversions: 320, conversionRate: 25.6, revenue: 98000 },
    { period: 'Feb', leads: 1380, signups: 520, conversions: 365, conversionRate: 26.4, revenue: 112000 },
    { period: 'Mar', leads: 1520, signups: 580, conversions: 420, conversionRate: 27.6, revenue: 128000 },
    { period: 'Apr', leads: 1420, signups: 545, conversions: 385, conversionRate: 27.1, revenue: 118000 },
    { period: 'May', leads: 1680, signups: 650, conversions: 485, conversionRate: 28.9, revenue: 145000 },
    { period: 'Jun', leads: 1850, signups: 720, conversions: 540, conversionRate: 29.2, revenue: 162000 }
  ]);

  const [clientInteractions] = useState<ClientInteraction[]>([
    {
      studioId: '1',
      studioName: 'Elite Photography',
      totalInteractions: 15420,
      photoViews: 8950,
      downloads: 2340,
      shares: 1280,
      favorites: 2850,
      avgSessionDuration: 8.5,
      returnRate: 68.2
    },
    {
      studioId: '2',
      studioName: 'Wedding Dreams Studio',
      totalInteractions: 12680,
      photoViews: 7320,
      downloads: 1890,
      shares: 980,
      favorites: 2490,
      avgSessionDuration: 7.2,
      returnRate: 72.1
    },
    {
      studioId: '3',
      studioName: 'Portrait Masters',
      totalInteractions: 9850,
      photoViews: 5680,
      downloads: 1420,
      shares: 650,
      favorites: 2100,
      avgSessionDuration: 6.8,
      returnRate: 65.4
    }
  ]);

  const [activityPatterns] = useState<ActivityPattern[]>([
    { hour: 0, uploads: 12, views: 45, downloads: 8, shares: 3 },
    { hour: 1, uploads: 8, views: 32, downloads: 5, shares: 2 },
    { hour: 2, uploads: 5, views: 28, downloads: 3, shares: 1 },
    { hour: 3, uploads: 4, views: 25, downloads: 2, shares: 1 },
    { hour: 4, uploads: 6, views: 30, downloads: 4, shares: 2 },
    { hour: 5, uploads: 15, views: 55, downloads: 12, shares: 5 },
    { hour: 6, uploads: 28, views: 85, downloads: 22, shares: 8 },
    { hour: 7, uploads: 45, views: 125, downloads: 35, shares: 15 },
    { hour: 8, uploads: 68, views: 180, downloads: 52, shares: 25 },
    { hour: 9, uploads: 85, views: 220, downloads: 68, shares: 32 },
    { hour: 10, uploads: 95, views: 250, downloads: 78, shares: 38 },
    { hour: 11, uploads: 88, views: 235, downloads: 72, shares: 35 },
    { hour: 12, uploads: 92, views: 245, downloads: 75, shares: 36 },
    { hour: 13, uploads: 98, views: 260, downloads: 82, shares: 42 },
    { hour: 14, uploads: 105, views: 285, downloads: 88, shares: 45 },
    { hour: 15, uploads: 112, views: 295, downloads: 92, shares: 48 },
    { hour: 16, uploads: 108, views: 280, downloads: 85, shares: 44 },
    { hour: 17, uploads: 95, views: 255, downloads: 78, shares: 38 },
    { hour: 18, uploads: 82, views: 220, downloads: 65, shares: 32 },
    { hour: 19, uploads: 75, views: 195, downloads: 58, shares: 28 },
    { hour: 20, uploads: 68, views: 175, downloads: 48, shares: 22 },
    { hour: 21, uploads: 55, views: 145, downloads: 38, shares: 18 },
    { hour: 22, uploads: 42, views: 115, downloads: 28, shares: 12 },
    { hour: 23, uploads: 25, views: 75, downloads: 18, shares: 8 }
  ]);

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'Enterprise': return 'bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300';
      case 'Professional': return 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300';
      case 'Starter': return 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'down': return <TrendingDown className="h-4 w-4 text-red-600" />;
      default: return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <BarChart3 className="h-8 w-8 text-blue-600" />
              Analytics & Reporting
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Comprehensive platform analytics and performance insights
            </p>
          </div>
          <div className="flex items-center gap-3">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
            <Button variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Data
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <Building2 className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {platformMetrics.activeStudios.toLocaleString()}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Active Studios</p>
                  <div className="flex items-center gap-1 mt-1">
                    <TrendingUp className="h-3 w-3 text-blue-600" />
                    <span className="text-xs text-blue-600 dark:text-blue-400">
                      +{platformMetrics.monthlyGrowth}%
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <Users className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {(platformMetrics.totalClients / 1000).toFixed(1)}K
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Total Clients</p>
                  <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                    Avg: {Math.round(platformMetrics.totalClients / platformMetrics.activeStudios)} per studio
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <Camera className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {(platformMetrics.totalPhotos / 1000000).toFixed(1)}M
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Photos Stored</p>
                  <p className="text-xs text-purple-600 dark:text-purple-400 mt-1">
                    Avg: {platformMetrics.avgPhotosPerStudio.toLocaleString()} per studio
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-orange-600 rounded-xl flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {formatCurrency(platformMetrics.totalRevenue)}
                  </p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">Total Revenue</p>
                  <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                    MRR: {formatCurrency(platformMetrics.totalRevenue / 12)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Analytics Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="studios">Studio Performance</TabsTrigger>
            <TabsTrigger value="conversions">Conversions</TabsTrigger>
            <TabsTrigger value="interactions">Interactions</TabsTrigger>
            <TabsTrigger value="activity">Activity Patterns</TabsTrigger>
            <TabsTrigger value="usage">Feature Usage</TabsTrigger>
            <TabsTrigger value="geographic">Geographic</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Revenue Trend */}
              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-green-600" />
                    Revenue Growth
                  </CardTitle>
                  <CardDescription>
                    Monthly revenue and studio growth trends
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={revenueData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
                      <Tooltip formatter={(value: number) => [formatCurrency(value), 'Revenue']} />
                      <Area
                        type="monotone"
                        dataKey="revenue"
                        stroke="#10B981"
                        fill="#10B981"
                        fillOpacity={0.6}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Plan Distribution */}
              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5 text-blue-600" />
                    Subscription Plans
                  </CardTitle>
                  <CardDescription>
                    Distribution of studios by plan type
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={planDistribution}
                        cx="50%"
                        cy="50%"
                        outerRadius={100}
                        dataKey="value"
                        label={({ name, value }) => `${name}: ${value}%`}
                      >
                        {planDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Studio Growth */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5 text-purple-600" />
                  Studio Acquisition vs Churn
                </CardTitle>
                <CardDescription>
                  Weekly new studio signups vs churned studios
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={studioGrowthData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" tickFormatter={(date) => formatDate(date)} />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="newStudios" fill="#10B981" name="New Studios" />
                    <Bar dataKey="churnedStudios" fill="#EF4444" name="Churned Studios" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Studio Performance Tab */}
          <TabsContent value="studios" className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5 text-gold-600" />
                  Top Performing Studios
                </CardTitle>
                <CardDescription>
                  Studios ranked by revenue and growth metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {topStudios.map((studio, index) => (
                    <div key={studio.studioId} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="h-10 w-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-bold">
                            #{index + 1}
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900 dark:text-white">
                              {studio.studioName}
                            </h3>
                            <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                              <span>{studio.clientCount} clients</span>
                              <span>•</span>
                              <span>{studio.photoCount.toLocaleString()} photos</span>
                              <span>•</span>
                              <span>Rating: {studio.rating}/5</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          <div className="text-right">
                            <p className="font-bold text-gray-900 dark:text-white">
                              {formatCurrency(studio.revenue)}
                            </p>
                            <div className="flex items-center gap-1">
                              <TrendingUp className="h-3 w-3 text-green-600" />
                              <span className="text-sm text-green-600">
                                +{studio.growthRate}%
                              </span>
                            </div>
                          </div>
                          <Badge className={cn("text-xs", getPlanColor(studio.planType))}>
                            {studio.planType}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Feature Usage Tab */}
          <TabsContent value="usage" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-yellow-600" />
                    Feature Adoption
                  </CardTitle>
                  <CardDescription>
                    Usage rates of platform features
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {usageMetrics.map(metric => (
                      <div key={metric.feature} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium text-gray-900 dark:text-white">
                              {metric.feature}
                            </span>
                            {getTrendIcon(metric.trend)}
                          </div>
                          <span className="text-sm font-bold text-gray-900 dark:text-white">
                            {metric.usage}%
                          </span>
                        </div>
                        <Progress value={metric.usage} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Smartphone className="h-5 w-5 text-blue-600" />
                    Device Usage
                  </CardTitle>
                  <CardDescription>
                    Platform access by device type
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {deviceStats.map(device => (
                      <div key={device.device} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={cn(
                            "h-8 w-8 rounded-lg flex items-center justify-center",
                            device.device === 'Desktop' ? 'bg-blue-100 dark:bg-blue-900/20' :
                            device.device === 'Mobile' ? 'bg-green-100 dark:bg-green-900/20' :
                            'bg-purple-100 dark:bg-purple-900/20'
                          )}>
                            {device.device === 'Desktop' ? (
                              <Eye className="h-4 w-4 text-blue-600" />
                            ) : device.device === 'Mobile' ? (
                              <Smartphone className="h-4 w-4 text-green-600" />
                            ) : (
                              <Smartphone className="h-4 w-4 text-purple-600" />
                            )}
                          </div>
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">
                              {device.device}
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {device.users.toLocaleString()} users
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-gray-900 dark:text-white">
                            {device.percentage}%
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Geographic Tab */}
          <TabsContent value="geographic" className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5 text-green-600" />
                  Geographic Distribution
                </CardTitle>
                <CardDescription>
                  Studio distribution and revenue by region
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {geographicData.map(region => (
                    <div key={region.region} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <h3 className="font-semibold text-gray-900 dark:text-white mb-3">
                        {region.region}
                      </h3>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">Studios</span>
                          <span className="font-medium text-gray-900 dark:text-white">
                            {region.studios}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">Revenue</span>
                          <span className="font-medium text-gray-900 dark:text-white">
                            {formatCurrency(region.revenue)}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">Avg per Studio</span>
                          <span className="font-medium text-gray-900 dark:text-white">
                            {formatCurrency(region.revenue / region.studios)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Trends Tab */}
          <TabsContent value="trends" className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-blue-600" />
                  Platform Growth Trends
                </CardTitle>
                <CardDescription>
                  Key performance indicators and growth metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <LineChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis yAxisId="left" tickFormatter={(value) => `$${value / 1000}k`} />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Legend />
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="revenue"
                      stroke="#10B981"
                      strokeWidth={3}
                      name="Revenue"
                    />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="studios"
                      stroke="#3B82F6"
                      strokeWidth={2}
                      name="Studios"
                    />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="clients"
                      stroke="#8B5CF6"
                      strokeWidth={2}
                      name="Clients"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Conversions Tab */}
          <TabsContent value="conversions" className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-green-600" />
                  Conversion Analytics
                </CardTitle>
                <CardDescription>
                  Track lead conversion rates and revenue performance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <AreaChart data={conversionMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" tickFormatter={(value) => `${value}%`} />
                    <Tooltip />
                    <Legend />
                    <Area
                      yAxisId="left"
                      type="monotone"
                      dataKey="leads"
                      stackId="1"
                      stroke="#3B82F6"
                      fill="#3B82F6"
                      fillOpacity={0.6}
                      name="Leads"
                    />
                    <Area
                      yAxisId="left"
                      type="monotone"
                      dataKey="signups"
                      stackId="2"
                      stroke="#10B981"
                      fill="#10B981"
                      fillOpacity={0.6}
                      name="Signups"
                    />
                    <Area
                      yAxisId="left"
                      type="monotone"
                      dataKey="conversions"
                      stackId="3"
                      stroke="#8B5CF6"
                      fill="#8B5CF6"
                      fillOpacity={0.6}
                      name="Conversions"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Interactions Tab */}
          <TabsContent value="interactions" className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5 text-purple-600" />
                  Client Interaction Analytics
                </CardTitle>
                <CardDescription>
                  Analyze client engagement and interaction patterns
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {clientInteractions.map((studio) => (
                    <div key={studio.studioId} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          {studio.studioName}
                        </h3>
                        <Badge variant="outline">
                          {studio.returnRate.toFixed(1)}% return rate
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-600">
                            {studio.photoViews.toLocaleString()}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">Photo Views</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600">
                            {studio.downloads.toLocaleString()}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">Downloads</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-600">
                            {studio.shares.toLocaleString()}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">Shares</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-orange-600">
                            {studio.avgSessionDuration.toFixed(1)}m
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">Avg Session</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Activity Patterns Tab */}
          <TabsContent value="activity" className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-blue-600" />
                  24-Hour Activity Patterns
                </CardTitle>
                <CardDescription>
                  Understand when users are most active on the platform
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <AreaChart data={activityPatterns}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="hour"
                      tickFormatter={(hour) => `${hour}:00`}
                    />
                    <YAxis />
                    <Tooltip
                      labelFormatter={(hour) => `${hour}:00`}
                    />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="uploads"
                      stackId="1"
                      stroke="#3B82F6"
                      fill="#3B82F6"
                      fillOpacity={0.6}
                      name="Uploads"
                    />
                    <Area
                      type="monotone"
                      dataKey="views"
                      stackId="2"
                      stroke="#10B981"
                      fill="#10B981"
                      fillOpacity={0.6}
                      name="Views"
                    />
                    <Area
                      type="monotone"
                      dataKey="downloads"
                      stackId="3"
                      stroke="#8B5CF6"
                      fill="#8B5CF6"
                      fillOpacity={0.6}
                      name="Downloads"
                    />
                    <Area
                      type="monotone"
                      dataKey="shares"
                      stackId="4"
                      stroke="#F59E0B"
                      fill="#F59E0B"
                      fillOpacity={0.6}
                      name="Shares"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
}
