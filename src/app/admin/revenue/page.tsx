"use client";

import { useState } from "react";
import { 
  DollarSign,
  TrendingUp,
  TrendingDown,
  CreditCard,
  Users,
  Calendar,
  BarChart3,
  PieChart,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  Zap
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart as RechartsPieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import AdminLayout from "@/components/admin/AdminLayout";
import { formatCurrency, formatDate, cn } from "@/lib/utils";

interface RevenueData {
  totalRevenue: number;
  monthlyRevenue: number;
  revenueGrowth: number;
  averageRevenuePerUser: number;
  churnRate: number;
  lifetimeValue: number;
  planBreakdown: Array<{
    planName: string;
    subscribers: number;
    revenue: number;
    percentage: number;
  }>;
  monthlyTrends: Array<{
    month: string;
    revenue: number;
    newSubscriptions: number;
    churn: number;
    netRevenue: number;
  }>;
  revenueByStudio: Array<{
    studioName: string;
    plan: string;
    revenue: number;
    growth: number;
    status: 'active' | 'churned' | 'paused';
  }>;
  paymentMethods: Array<{
    method: string;
    count: number;
    revenue: number;
    percentage: number;
  }>;
}

export default function RevenueAnalyticsPage() {
  const [period, setPeriod] = useState("12");
  const [revenueData] = useState<RevenueData>({
    totalRevenue: 245680,
    monthlyRevenue: 18450,
    revenueGrowth: 12.5,
    averageRevenuePerUser: 89.50,
    churnRate: 3.2,
    lifetimeValue: 1250,
    planBreakdown: [
      { planName: 'Professional', subscribers: 128, revenue: 10112, percentage: 54.8 },
      { planName: 'Enterprise', subscribers: 23, revenue: 4577, percentage: 24.8 },
      { planName: 'Starter', subscribers: 45, revenue: 1305, percentage: 7.1 },
      { planName: 'Custom', subscribers: 8, revenue: 2456, percentage: 13.3 }
    ],
    monthlyTrends: [
      { month: '2023-02', revenue: 12500, newSubscriptions: 15, churn: 2, netRevenue: 12300 },
      { month: '2023-03', revenue: 13200, newSubscriptions: 18, churn: 3, netRevenue: 12950 },
      { month: '2023-04', revenue: 14100, newSubscriptions: 22, churn: 1, netRevenue: 14050 },
      { month: '2023-05', revenue: 15300, newSubscriptions: 25, churn: 4, netRevenue: 14980 },
      { month: '2023-06', revenue: 16200, newSubscriptions: 20, churn: 2, netRevenue: 16050 },
      { month: '2023-07', revenue: 16800, newSubscriptions: 18, churn: 3, netRevenue: 16520 },
      { month: '2023-08', revenue: 17500, newSubscriptions: 23, churn: 2, netRevenue: 17380 },
      { month: '2023-09', revenue: 17900, newSubscriptions: 19, churn: 5, netRevenue: 17450 },
      { month: '2023-10', revenue: 18200, newSubscriptions: 21, churn: 3, netRevenue: 17980 },
      { month: '2023-11', revenue: 18600, newSubscriptions: 24, churn: 2, netRevenue: 18480 },
      { month: '2023-12', revenue: 18100, newSubscriptions: 16, churn: 4, netRevenue: 17720 },
      { month: '2024-01', revenue: 18450, newSubscriptions: 19, churn: 3, netRevenue: 18200 }
    ],
    revenueByStudio: [
      { studioName: 'PhotoStudio Pro', plan: 'Professional', revenue: 79, growth: 8.5, status: 'active' },
      { studioName: 'Wedding Memories', plan: 'Enterprise', revenue: 199, growth: 15.2, status: 'active' },
      { studioName: 'Quick Shots', plan: 'Starter', revenue: 29, growth: 22.1, status: 'active' },
      { studioName: 'Elite Photography', plan: 'Enterprise', revenue: 199, growth: -5.2, status: 'paused' },
      { studioName: 'Moment Capture', plan: 'Professional', revenue: 79, growth: 12.8, status: 'active' }
    ],
    paymentMethods: [
      { method: 'Credit Card', count: 156, revenue: 14250, percentage: 77.2 },
      { method: 'PayPal', count: 32, revenue: 2890, percentage: 15.7 },
      { method: 'Bank Transfer', count: 14, revenue: 1310, percentage: 7.1 }
    ]
  });

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'paused': return 'warning';
      case 'churned': return 'destructive';
      default: return 'secondary';
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <DollarSign className="h-8 w-8 text-green-600" />
              Revenue Analytics
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Comprehensive revenue insights, trends, and subscription analytics
            </p>
          </div>
          <div className="flex items-center gap-3">
            <select
              value={period}
              onChange={(e) => setPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="3">Last 3 months</option>
              <option value="6">Last 6 months</option>
              <option value="12">Last 12 months</option>
              <option value="24">Last 24 months</option>
            </select>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {formatCurrency(revenueData.monthlyRevenue)}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Monthly Revenue</p>
                  <div className="flex items-center gap-1 mt-1">
                    <TrendingUp className="h-3 w-3 text-green-600" />
                    <span className="text-xs text-green-600 font-medium">+{revenueData.revenueGrowth}%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <Target className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {formatCurrency(revenueData.averageRevenuePerUser)}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">ARPU</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <Zap className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {formatCurrency(revenueData.lifetimeValue)}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Customer LTV</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-red-600 rounded-xl flex items-center justify-center">
                  <TrendingDown className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-red-900 dark:text-red-100">
                    {revenueData.churnRate}%
                  </p>
                  <p className="text-sm text-red-600 dark:text-red-400">Churn Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Revenue Trends */}
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                Revenue Trends
              </CardTitle>
              <CardDescription>
                Monthly revenue growth and subscription metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={revenueData.monthlyTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" tickFormatter={(month) => formatDate(month + '-01')} />
                  <YAxis tickFormatter={(value) => formatCurrency(value)} />
                  <Tooltip 
                    labelFormatter={(month) => formatDate(month + '-01')}
                    formatter={[(value: number) => formatCurrency(value), 'Revenue']}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="revenue" 
                    stroke="#10B981" 
                    fill="#10B981" 
                    fillOpacity={0.6}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Plan Revenue Breakdown */}
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5 text-blue-600" />
                Revenue by Plan
              </CardTitle>
              <CardDescription>
                Revenue distribution across subscription plans
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center">
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Pie
                      data={revenueData.planBreakdown}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ planName, percentage }) => `${planName} ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="revenue"
                    >
                      {revenueData.planBreakdown.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={[(value: number) => formatCurrency(value), 'Revenue']} />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </div>
              <div className="grid grid-cols-2 gap-4 mt-4">
                {revenueData.planBreakdown.map((plan, index) => (
                  <div key={plan.planName} className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {plan.planName}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {plan.subscribers} subscribers • {formatCurrency(plan.revenue)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Studio Revenue Table */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-purple-600" />
              Revenue by Studio
            </CardTitle>
            <CardDescription>
              Individual studio performance and growth metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {revenueData.revenueByStudio.map((studio, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className="h-10 w-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-semibold">
                      {studio.studioName.charAt(0)}
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900 dark:text-white">
                        {studio.studioName}
                      </p>
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <span>{studio.plan} Plan</span>
                        <Badge variant={getStatusColor(studio.status) as any} className="text-xs">
                          {studio.status}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-gray-900 dark:text-white">
                      {formatCurrency(studio.revenue)}/month
                    </p>
                    <div className="flex items-center gap-1">
                      {studio.growth > 0 ? (
                        <TrendingUp className="h-3 w-3 text-green-600" />
                      ) : (
                        <TrendingDown className="h-3 w-3 text-red-600" />
                      )}
                      <span className={cn(
                        "text-xs font-medium",
                        studio.growth > 0 ? "text-green-600" : "text-red-600"
                      )}>
                        {studio.growth > 0 ? '+' : ''}{studio.growth}%
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Payment Methods & Additional Metrics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Payment Methods */}
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5 text-orange-600" />
                Payment Methods
              </CardTitle>
              <CardDescription>
                Revenue breakdown by payment method
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {revenueData.paymentMethods.map((method, index) => (
                  <div key={method.method} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={cn(
                        "h-8 w-8 rounded-lg flex items-center justify-center",
                        index === 0 ? "bg-blue-100 dark:bg-blue-900/20" :
                        index === 1 ? "bg-yellow-100 dark:bg-yellow-900/20" :
                        "bg-green-100 dark:bg-green-900/20"
                      )}>
                        <CreditCard className={cn(
                          "h-4 w-4",
                          index === 0 ? "text-blue-600" :
                          index === 1 ? "text-yellow-600" :
                          "text-green-600"
                        )} />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {method.method}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {method.count} transactions
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-gray-900 dark:text-white">
                        {formatCurrency(method.revenue)}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {method.percentage}%
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Key Performance Indicators */}
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-green-600" />
                Key Performance Indicators
              </CardTitle>
              <CardDescription>
                Important business metrics and targets
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Monthly Recurring Revenue
                    </span>
                    <span className="text-sm font-bold text-gray-900 dark:text-white">
                      {formatCurrency(revenueData.monthlyRevenue)}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-green-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: '75%' }}
                    />
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    75% of monthly target ({formatCurrency(25000)})
                  </p>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Customer Acquisition Cost
                    </span>
                    <span className="text-sm font-bold text-gray-900 dark:text-white">
                      {formatCurrency(125)}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: '60%' }}
                    />
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Target: {formatCurrency(100)} or less
                  </p>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Net Revenue Retention
                    </span>
                    <span className="text-sm font-bold text-gray-900 dark:text-white">
                      112%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: '90%' }}
                    />
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Excellent retention above 110%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
}
