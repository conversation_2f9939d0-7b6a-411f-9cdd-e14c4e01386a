"use client";

import { useState } from "react";
import { 
  Code,
  Key,
  Activity,
  Shield,
  Clock,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Eye,
  Copy,
  RefreshCw,
  Settings,
  BarChart3,
  Zap,
  Globe,
  Lock,
  Unlock,
  Plus,
  Edit,
  Trash2
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import AdminLayout from "@/components/admin/AdminLayout";
import { formatDate, timeAgo, cn } from "@/lib/utils";

interface APIKey {
  id: string;
  name: string;
  key: string;
  studioId?: string;
  studioName?: string;
  permissions: string[];
  rateLimit: number;
  requestsUsed: number;
  requestsLimit: number;
  status: 'active' | 'inactive' | 'revoked';
  createdAt: string;
  lastUsed?: string;
  expiresAt?: string;
}

interface APIEndpoint {
  path: string;
  method: string;
  description: string;
  requests: number;
  avgResponseTime: number;
  errorRate: number;
  status: 'healthy' | 'degraded' | 'down';
}

export default function APIManagementPage() {
  const [apiKeys] = useState<APIKey[]>([
    {
      id: '1',
      name: 'PhotoStudio Pro - Production',
      key: 'psp_live_1234567890abcdef',
      studioId: 'studio1',
      studioName: 'PhotoStudio Pro',
      permissions: ['photos:read', 'photos:write', 'clients:read', 'analytics:read'],
      rateLimit: 1000,
      requestsUsed: 750,
      requestsLimit: 10000,
      status: 'active',
      createdAt: '2024-01-15T10:00:00Z',
      lastUsed: '2024-01-20T09:30:00Z',
      expiresAt: '2024-07-15T10:00:00Z'
    },
    {
      id: '2',
      name: 'Wedding Memories - Integration',
      key: 'wm_live_abcdef1234567890',
      studioId: 'studio2',
      studioName: 'Wedding Memories',
      permissions: ['photos:read', 'clients:read', 'galleries:read'],
      rateLimit: 500,
      requestsUsed: 320,
      requestsLimit: 5000,
      status: 'active',
      createdAt: '2024-01-10T14:30:00Z',
      lastUsed: '2024-01-19T16:45:00Z'
    },
    {
      id: '3',
      name: 'Quick Shots - Testing',
      key: 'qs_test_fedcba0987654321',
      studioId: 'studio3',
      studioName: 'Quick Shots',
      permissions: ['photos:read'],
      rateLimit: 100,
      requestsUsed: 45,
      requestsLimit: 1000,
      status: 'inactive',
      createdAt: '2024-01-18T11:00:00Z',
      lastUsed: '2024-01-18T15:20:00Z',
      expiresAt: '2024-02-18T11:00:00Z'
    }
  ]);

  const [endpoints] = useState<APIEndpoint[]>([
    {
      path: '/api/v1/photos',
      method: 'GET',
      description: 'Retrieve photos',
      requests: 15420,
      avgResponseTime: 245,
      errorRate: 0.8,
      status: 'healthy'
    },
    {
      path: '/api/v1/photos',
      method: 'POST',
      description: 'Upload photos',
      requests: 8950,
      avgResponseTime: 1200,
      errorRate: 2.1,
      status: 'healthy'
    },
    {
      path: '/api/v1/clients',
      method: 'GET',
      description: 'Get client list',
      requests: 5680,
      avgResponseTime: 180,
      errorRate: 0.3,
      status: 'healthy'
    },
    {
      path: '/api/v1/face-recognition',
      method: 'POST',
      description: 'Process face recognition',
      requests: 3240,
      avgResponseTime: 2800,
      errorRate: 5.2,
      status: 'degraded'
    },
    {
      path: '/api/v1/analytics',
      method: 'GET',
      description: 'Get analytics data',
      requests: 1890,
      avgResponseTime: 320,
      errorRate: 0.1,
      status: 'healthy'
    }
  ]);

  const [apiMetrics] = useState([
    { time: '00:00', requests: 1200, errors: 15, responseTime: 245 },
    { time: '04:00', requests: 800, errors: 8, responseTime: 220 },
    { time: '08:00', requests: 2100, errors: 25, responseTime: 280 },
    { time: '12:00', requests: 3200, errors: 45, responseTime: 320 },
    { time: '16:00', requests: 2800, errors: 38, responseTime: 295 },
    { time: '20:00', requests: 1900, errors: 22, responseTime: 260 }
  ]);

  const totalRequests = endpoints.reduce((sum, endpoint) => sum + endpoint.requests, 0);
  const avgResponseTime = endpoints.reduce((sum, endpoint) => sum + endpoint.avgResponseTime, 0) / endpoints.length;
  const avgErrorRate = endpoints.reduce((sum, endpoint) => sum + endpoint.errorRate, 0) / endpoints.length;
  const activeKeys = apiKeys.filter(key => key.status === 'active').length;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'healthy': return 'success';
      case 'inactive':
      case 'degraded': return 'warning';
      case 'revoked':
      case 'down': return 'destructive';
      default: return 'secondary';
    }
  };

  const getUsagePercentage = (used: number, limit: number) => {
    return Math.min((used / limit) * 100, 100);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const maskApiKey = (key: string) => {
    return key.substring(0, 8) + '...' + key.substring(key.length - 8);
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <Code className="h-8 w-8 text-blue-600" />
              API Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Monitor API usage, manage keys, and track endpoint performance
            </p>
          </div>
          <Button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
            <Plus className="h-4 w-4 mr-2" />
            Create API Key
          </Button>
        </div>

        {/* API Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <Activity className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {totalRequests.toLocaleString()}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Total Requests</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <Key className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {activeKeys}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Active Keys</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <Zap className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {Math.round(avgResponseTime)}ms
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Avg Response</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-red-600 rounded-xl flex items-center justify-center">
                  <AlertTriangle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-red-900 dark:text-red-100">
                    {avgErrorRate.toFixed(1)}%
                  </p>
                  <p className="text-sm text-red-600 dark:text-red-400">Error Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* API Metrics Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Request Volume */}
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-blue-600" />
                Request Volume
              </CardTitle>
              <CardDescription>
                API requests over the last 24 hours
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={apiMetrics}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="requests" fill="#3B82F6" name="Requests" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Response Time & Errors */}
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-green-600" />
                Performance Metrics
              </CardTitle>
              <CardDescription>
                Response time and error trends
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={apiMetrics}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis />
                  <Tooltip />
                  <Line 
                    type="monotone" 
                    dataKey="responseTime" 
                    stroke="#10B981" 
                    strokeWidth={2}
                    name="Response Time (ms)"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="errors" 
                    stroke="#EF4444" 
                    strokeWidth={2}
                    name="Errors"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* API Keys Management */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Key className="h-5 w-5 text-green-600" />
              API Keys
            </CardTitle>
            <CardDescription>
              Manage API keys and their permissions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {apiKeys.map((apiKey) => (
                <Card key={apiKey.id} className="border border-gray-200 dark:border-gray-600">
                  <CardContent className="p-6">
                    <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
                      <div className="flex-1 space-y-4">
                        {/* Key Header */}
                        <div className="flex items-start justify-between">
                          <div>
                            <div className="flex items-center gap-2">
                              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                {apiKey.name}
                              </h3>
                              <Badge variant={getStatusColor(apiKey.status) as any}>
                                {apiKey.status}
                              </Badge>
                            </div>
                            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                              {apiKey.studioName || 'System Key'}
                            </p>
                          </div>
                        </div>

                        {/* API Key */}
                        <div className="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <code className="flex-1 text-sm font-mono text-gray-700 dark:text-gray-300">
                            {maskApiKey(apiKey.key)}
                          </code>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(apiKey.key)}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                        </div>

                        {/* Usage Stats */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                Monthly Usage
                              </span>
                              <span className="text-sm text-gray-600 dark:text-gray-400">
                                {apiKey.requestsUsed.toLocaleString()} / {apiKey.requestsLimit.toLocaleString()}
                              </span>
                            </div>
                            <Progress 
                              value={getUsagePercentage(apiKey.requestsUsed, apiKey.requestsLimit)} 
                              className="h-2"
                            />
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            <p><strong>Rate Limit:</strong> {apiKey.rateLimit}/hour</p>
                            <p><strong>Created:</strong> {formatDate(apiKey.createdAt)}</p>
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            {apiKey.lastUsed && (
                              <p><strong>Last Used:</strong> {timeAgo(apiKey.lastUsed)}</p>
                            )}
                            {apiKey.expiresAt && (
                              <p><strong>Expires:</strong> {formatDate(apiKey.expiresAt)}</p>
                            )}
                          </div>
                        </div>

                        {/* Permissions */}
                        <div>
                          <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Permissions:
                          </p>
                          <div className="flex flex-wrap gap-2">
                            {apiKey.permissions.map((permission, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {permission}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex flex-col gap-2 lg:flex-row lg:items-center">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Button>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </Button>
                        <Button variant="outline" size="sm">
                          <RefreshCw className="h-4 w-4 mr-2" />
                          Regenerate
                        </Button>
                        <Button variant="outline" size="sm">
                          {apiKey.status === 'active' ? (
                            <>
                              <Lock className="h-4 w-4 mr-2" />
                              Revoke
                            </>
                          ) : (
                            <>
                              <Unlock className="h-4 w-4 mr-2" />
                              Activate
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* API Endpoints Status */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5 text-purple-600" />
              API Endpoints
            </CardTitle>
            <CardDescription>
              Monitor endpoint performance and health
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {endpoints.map((endpoint, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className={cn(
                      "h-10 w-10 rounded-lg flex items-center justify-center",
                      endpoint.status === 'healthy' ? 'bg-green-100 dark:bg-green-900/20' :
                      endpoint.status === 'degraded' ? 'bg-yellow-100 dark:bg-yellow-900/20' :
                      'bg-red-100 dark:bg-red-900/20'
                    )}>
                      <Globe className={cn(
                        "h-5 w-5",
                        endpoint.status === 'healthy' ? 'text-green-600' :
                        endpoint.status === 'degraded' ? 'text-yellow-600' :
                        'text-red-600'
                      )} />
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs font-mono">
                          {endpoint.method}
                        </Badge>
                        <code className="text-sm font-mono text-gray-700 dark:text-gray-300">
                          {endpoint.path}
                        </code>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {endpoint.description}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-6 text-sm text-gray-600 dark:text-gray-400">
                    <div className="text-right">
                      <p className="font-semibold text-gray-900 dark:text-white">
                        {endpoint.requests.toLocaleString()}
                      </p>
                      <p>requests</p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900 dark:text-white">
                        {endpoint.avgResponseTime}ms
                      </p>
                      <p>avg response</p>
                    </div>
                    <div className="text-right">
                      <p className={cn(
                        "font-semibold",
                        endpoint.errorRate > 5 ? "text-red-600" :
                        endpoint.errorRate > 2 ? "text-yellow-600" :
                        "text-green-600"
                      )}>
                        {endpoint.errorRate}%
                      </p>
                      <p>error rate</p>
                    </div>
                    <Badge variant={getStatusColor(endpoint.status) as any}>
                      {endpoint.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
