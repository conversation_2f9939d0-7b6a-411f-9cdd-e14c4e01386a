"use client";

import { useState } from "react";
import { 
  FileText,
  Download,
  Calendar,
  Filter,
  BarChart3,
  Users,
  Building2,
  TrendingUp,
  DollarSign,
  Activity,
  Zap,
  Eye,
  Share2,
  FileSpreadsheet,
  FileImage,
  Mail,
  Clock,
  Target,
  Globe,
  HardDrive,
  CreditCard,
  AlertTriangle,
  CheckCircle,
  Settings,
  Plus
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import AdminLayout from "@/components/admin/AdminLayout";
import { formatDate, formatCurrency, cn } from "@/lib/utils";

interface AdminReportTemplate {
  id: string;
  name: string;
  description: string;
  category: 'platform' | 'studios' | 'revenue' | 'usage' | 'security' | 'compliance';
  icon: any;
  formats: string[];
  frequency: 'on-demand' | 'daily' | 'weekly' | 'monthly';
  lastGenerated?: string;
  isScheduled?: boolean;
  isPopular?: boolean;
}

interface ScheduledReport {
  id: string;
  templateId: string;
  templateName: string;
  frequency: string;
  format: string;
  recipients: string[];
  nextRun: string;
  lastRun?: string;
  status: 'active' | 'paused' | 'failed';
}

export default function AdminReportsPage() {
  const [dateRange, setDateRange] = useState("30");
  const [selectedCategory, setSelectedCategory] = useState("all");
  
  const [reportTemplates] = useState<AdminReportTemplate[]>([
    {
      id: '1',
      name: 'Platform Overview Report',
      description: 'Comprehensive platform metrics including user growth, revenue, and system health',
      category: 'platform',
      icon: Globe,
      formats: ['PDF', 'Excel', 'PowerPoint'],
      frequency: 'monthly',
      lastGenerated: '2024-01-15T09:00:00Z',
      isScheduled: true,
      isPopular: true
    },
    {
      id: '2',
      name: 'Studio Performance Analysis',
      description: 'Individual studio metrics, usage patterns, and performance benchmarks',
      category: 'studios',
      icon: Building2,
      formats: ['PDF', 'Excel'],
      frequency: 'weekly',
      lastGenerated: '2024-01-18T10:30:00Z',
      isScheduled: true,
      isPopular: true
    },
    {
      id: '3',
      name: 'Revenue & Financial Report',
      description: 'Detailed financial analysis including MRR, churn, and revenue forecasting',
      category: 'revenue',
      icon: DollarSign,
      formats: ['PDF', 'Excel'],
      frequency: 'monthly',
      lastGenerated: '2024-01-10T14:20:00Z',
      isScheduled: true
    },
    {
      id: '4',
      name: 'System Usage Analytics',
      description: 'Storage utilization, API usage, and system performance metrics',
      category: 'usage',
      icon: HardDrive,
      formats: ['PDF', 'Excel', 'CSV'],
      frequency: 'weekly',
      lastGenerated: '2024-01-19T08:15:00Z'
    },
    {
      id: '5',
      name: 'Security Audit Report',
      description: 'Security events, threat analysis, and compliance status',
      category: 'security',
      icon: AlertTriangle,
      formats: ['PDF', 'Excel'],
      frequency: 'weekly',
      lastGenerated: '2024-01-17T16:45:00Z',
      isScheduled: true
    },
    {
      id: '6',
      name: 'Compliance & Data Protection',
      description: 'GDPR compliance, data retention, and privacy audit report',
      category: 'compliance',
      icon: CheckCircle,
      formats: ['PDF'],
      frequency: 'monthly',
      lastGenerated: '2024-01-05T11:00:00Z'
    },
    {
      id: '7',
      name: 'Customer Success Metrics',
      description: 'Client satisfaction, support tickets, and retention analysis',
      category: 'studios',
      icon: Target,
      formats: ['PDF', 'Excel'],
      frequency: 'monthly',
      isPopular: true
    },
    {
      id: '8',
      name: 'API Usage & Performance',
      description: 'API endpoint performance, rate limiting, and integration health',
      category: 'usage',
      icon: Zap,
      formats: ['PDF', 'Excel', 'CSV'],
      frequency: 'weekly'
    }
  ]);

  const [scheduledReports] = useState<ScheduledReport[]>([
    {
      id: '1',
      templateId: '1',
      templateName: 'Platform Overview Report',
      frequency: 'Monthly',
      format: 'PDF',
      recipients: ['<EMAIL>', '<EMAIL>'],
      nextRun: '2024-02-01T09:00:00Z',
      lastRun: '2024-01-15T09:00:00Z',
      status: 'active'
    },
    {
      id: '2',
      templateId: '2',
      templateName: 'Studio Performance Analysis',
      frequency: 'Weekly',
      format: 'Excel',
      recipients: ['<EMAIL>'],
      nextRun: '2024-01-22T10:00:00Z',
      lastRun: '2024-01-18T10:30:00Z',
      status: 'active'
    },
    {
      id: '3',
      templateId: '5',
      templateName: 'Security Audit Report',
      frequency: 'Weekly',
      format: 'PDF',
      recipients: ['<EMAIL>', '<EMAIL>'],
      nextRun: '2024-01-24T16:00:00Z',
      lastRun: '2024-01-17T16:45:00Z',
      status: 'active'
    }
  ]);

  const filteredTemplates = reportTemplates.filter(template => 
    selectedCategory === "all" || template.category === selectedCategory
  );

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'platform': return 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300';
      case 'studios': return 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300';
      case 'revenue': return 'bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300';
      case 'usage': return 'bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300';
      case 'security': return 'bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300';
      case 'compliance': return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'paused': return 'warning';
      case 'failed': return 'destructive';
      default: return 'secondary';
    }
  };

  const getFormatIcon = (format: string) => {
    switch (format.toLowerCase()) {
      case 'pdf': return FileText;
      case 'excel':
      case 'csv': return FileSpreadsheet;
      case 'powerpoint': return FileImage;
      default: return FileText;
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <FileText className="h-8 w-8 text-blue-600" />
              Platform Reports
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Generate comprehensive platform reports and analytics
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Report Settings
            </Button>
            <Button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
              <Plus className="h-4 w-4 mr-2" />
              Custom Report
            </Button>
          </div>
        </div>

        {/* Platform Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <FileText className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {reportTemplates.length}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Report Templates</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <Clock className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {scheduledReports.filter(r => r.status === 'active').length}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Scheduled Reports</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {reportTemplates.filter(r => r.isPopular).length}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Popular Reports</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-orange-600 rounded-xl flex items-center justify-center">
                  <Mail className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {scheduledReports.reduce((sum, r) => sum + r.recipients.length, 0)}
                  </p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">Email Recipients</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Report Templates */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-blue-600" />
                  Report Templates
                </CardTitle>
                <CardDescription>
                  Comprehensive platform reporting templates
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">All Categories</option>
                  <option value="platform">Platform</option>
                  <option value="studios">Studios</option>
                  <option value="revenue">Revenue</option>
                  <option value="usage">Usage</option>
                  <option value="security">Security</option>
                  <option value="compliance">Compliance</option>
                </select>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTemplates.map((template) => {
                const IconComponent = template.icon;
                return (
                  <Card key={template.id} className="border border-gray-200 dark:border-gray-600 hover:shadow-lg transition-all duration-200">
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        {/* Template Header */}
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-3">
                            <div className={cn(
                              "h-10 w-10 rounded-lg flex items-center justify-center",
                              getCategoryColor(template.category)
                            )}>
                              <IconComponent className="h-5 w-5" />
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900 dark:text-white">
                                {template.name}
                              </h3>
                              <div className="flex items-center gap-2 mt-1">
                                {template.isPopular && (
                                  <Badge variant="success" className="text-xs">
                                    Popular
                                  </Badge>
                                )}
                                {template.isScheduled && (
                                  <Badge variant="secondary" className="text-xs">
                                    <Clock className="h-3 w-3 mr-1" />
                                    Scheduled
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Description */}
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {template.description}
                        </p>

                        {/* Frequency & Formats */}
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-gray-500 dark:text-gray-400">Frequency:</span>
                            <Badge variant="outline" className="text-xs">
                              {template.frequency}
                            </Badge>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Formats:</p>
                            <div className="flex flex-wrap gap-1">
                              {template.formats.map((format, index) => {
                                const FormatIcon = getFormatIcon(format);
                                return (
                                  <Badge key={index} variant="secondary" className="text-xs">
                                    <FormatIcon className="h-3 w-3 mr-1" />
                                    {format}
                                  </Badge>
                                );
                              })}
                            </div>
                          </div>
                        </div>

                        {/* Last Generated */}
                        {template.lastGenerated && (
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Last generated: {formatDate(template.lastGenerated)}
                          </div>
                        )}

                        {/* Actions */}
                        <div className="flex items-center gap-2">
                          <Button size="sm" className="flex-1">
                            <Download className="h-4 w-4 mr-2" />
                            Generate
                          </Button>
                          <Button variant="outline" size="sm">
                            <Settings className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Scheduled Reports */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-green-600" />
              Scheduled Reports
            </CardTitle>
            <CardDescription>
              Automated report generation and delivery
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {scheduledReports.map((report) => (
                <div key={report.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className="h-10 w-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                      <Clock className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900 dark:text-white">
                        {report.templateName}
                      </p>
                      <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                        <span>{report.frequency}</span>
                        <span>•</span>
                        <span>{report.format}</span>
                        <span>•</span>
                        <span>{report.recipients.length} recipients</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-right text-sm">
                      <p className="text-gray-900 dark:text-white">
                        Next: {formatDate(report.nextRun)}
                      </p>
                      {report.lastRun && (
                        <p className="text-gray-600 dark:text-gray-400">
                          Last: {formatDate(report.lastRun)}
                        </p>
                      )}
                    </div>
                    <Badge variant={getStatusColor(report.status) as any}>
                      {report.status}
                    </Badge>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </Button>
                      <Button variant="outline" size="sm">
                        <Settings className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-orange-600" />
              Quick Actions
            </CardTitle>
            <CardDescription>
              Instant data exports and emergency reports
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                <Building2 className="h-6 w-6 text-blue-600" />
                <span className="text-sm">Export All Studios</span>
              </Button>
              <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                <DollarSign className="h-6 w-6 text-green-600" />
                <span className="text-sm">Revenue Summary</span>
              </Button>
              <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                <AlertTriangle className="h-6 w-6 text-red-600" />
                <span className="text-sm">Security Incidents</span>
              </Button>
              <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                <HardDrive className="h-6 w-6 text-purple-600" />
                <span className="text-sm">System Health</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
