"use client";

import { useState } from "react";
import { 
  Users,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  Ban,
  CheckCircle,
  Building2,
  Camera,
  Download,
  Calendar,
  MapPin,
  Phone,
  Mail,
  Crown,
  Activity,
  TrendingUp,
  Clock,
  Star
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import AdminLayout from "@/components/admin/AdminLayout";
import { formatDate, timeAgo, cn } from "@/lib/utils";

interface Client {
  id: string;
  name: string;
  email: string;
  phone?: string;
  studioId: string;
  studioName: string;
  eventType: string;
  eventDate: string;
  isActive: boolean;
  isVIP: boolean;
  totalPhotos: number;
  matchedPhotos: number;
  downloads: number;
  lastAccess: string;
  joinDate: string;
  qrCodeExpiry: string;
  notes?: string;
  tags: string[];
  city?: string;
  country?: string;
}

export default function AdminClientsPage() {
  const [clients] = useState<Client[]>([
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '****** 567 8900',
      studioId: 'studio1',
      studioName: 'PhotoStudio Pro',
      eventType: 'Wedding',
      eventDate: '2024-01-20',
      isActive: true,
      isVIP: true,
      totalPhotos: 150,
      matchedPhotos: 142,
      downloads: 45,
      lastAccess: '2024-01-19T16:30:00Z',
      joinDate: '2024-01-10',
      qrCodeExpiry: '2024-02-15',
      notes: 'Bride prefers candid shots. Very happy with service.',
      tags: ['Wedding', 'VIP', 'Referral'],
      city: 'New York',
      country: 'United States'
    },
    {
      id: '2',
      name: 'Mike Davis',
      email: '<EMAIL>',
      phone: '****** 567 8901',
      studioId: 'studio2',
      studioName: 'Wedding Memories',
      eventType: 'Birthday',
      eventDate: '2024-01-18',
      isActive: true,
      isVIP: false,
      totalPhotos: 85,
      matchedPhotos: 78,
      downloads: 23,
      lastAccess: '2024-01-18T14:20:00Z',
      joinDate: '2024-01-08',
      qrCodeExpiry: '2024-02-10',
      notes: 'Corporate birthday party. Requested group photos.',
      tags: ['Birthday', 'Corporate'],
      city: 'Los Angeles',
      country: 'United States'
    },
    {
      id: '3',
      name: 'Emma Wilson',
      email: '<EMAIL>',
      studioId: 'studio1',
      studioName: 'PhotoStudio Pro',
      eventType: 'Engagement',
      eventDate: '2024-01-12',
      isActive: false,
      isVIP: false,
      totalPhotos: 120,
      matchedPhotos: 115,
      downloads: 18,
      lastAccess: '2024-01-12T18:45:00Z',
      joinDate: '2024-01-05',
      qrCodeExpiry: '2024-01-25',
      notes: 'Outdoor engagement shoot. Loved the sunset photos.',
      tags: ['Engagement', 'Outdoor'],
      city: 'Chicago',
      country: 'United States'
    }
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [studioFilter, setStudioFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [eventTypeFilter, setEventTypeFilter] = useState("all");
  const [showVIPOnly, setShowVIPOnly] = useState(false);

  const filteredClients = clients.filter(client => {
    const matchesSearch = client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.studioName.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStudio = studioFilter === "all" || client.studioId === studioFilter;
    const matchesStatus = statusFilter === "all" || 
                         (statusFilter === "active" && client.isActive) ||
                         (statusFilter === "inactive" && !client.isActive);
    const matchesEventType = eventTypeFilter === "all" || client.eventType.toLowerCase() === eventTypeFilter.toLowerCase();
    const matchesVIP = !showVIPOnly || client.isVIP;
    
    return matchesSearch && matchesStudio && matchesStatus && matchesEventType && matchesVIP;
  });

  const getEventTypeIcon = (eventType: string) => {
    switch (eventType.toLowerCase()) {
      case 'wedding': return '💒';
      case 'birthday': return '🎂';
      case 'engagement': return '💍';
      case 'corporate': return '🏢';
      case 'family': return '👨‍👩‍👧‍👦';
      default: return '📸';
    }
  };

  const totalClients = clients.length;
  const activeClients = clients.filter(c => c.isActive).length;
  const vipClients = clients.filter(c => c.isVIP).length;
  const totalPhotos = clients.reduce((sum, c) => sum + c.totalPhotos, 0);
  const totalDownloads = clients.reduce((sum, c) => sum + c.downloads, 0);

  const studios = [...new Set(clients.map(c => ({ id: c.studioId, name: c.studioName })))];
  const eventTypes = [...new Set(clients.map(c => c.eventType))];

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <Users className="h-8 w-8 text-blue-600" />
              Global Client Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Manage and monitor all clients across all studios
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Badge variant="secondary" className="text-sm">
              {totalClients} Total Clients
            </Badge>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <Users className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {totalClients}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Total Clients</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <Activity className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {activeClients}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Active Clients</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <Crown className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {vipClients}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">VIP Clients</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-orange-600 rounded-xl flex items-center justify-center">
                  <Camera className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {totalPhotos.toLocaleString()}
                  </p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">Total Photos</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-red-600 rounded-xl flex items-center justify-center">
                  <Download className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-red-900 dark:text-red-100">
                    {totalDownloads.toLocaleString()}
                  </p>
                  <p className="text-sm text-red-600 dark:text-red-400">Total Downloads</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Clients Table */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-blue-600" />
                  All Clients
                </CardTitle>
                <CardDescription>
                  Comprehensive list of all clients across all studios
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Filters */}
            <div className="flex flex-col lg:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    placeholder="Search clients by name, email, or studio..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 h-11 bg-gray-50 dark:bg-gray-700"
                  />
                </div>
              </div>
              <div className="flex flex-wrap gap-2">
                <select
                  value={studioFilter}
                  onChange={(e) => setStudioFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">All Studios</option>
                  {studios.map(studio => (
                    <option key={studio.id} value={studio.id}>{studio.name}</option>
                  ))}
                </select>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
                <select
                  value={eventTypeFilter}
                  onChange={(e) => setEventTypeFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">All Events</option>
                  {eventTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
                <Button
                  variant={showVIPOnly ? "default" : "outline"}
                  size="sm"
                  onClick={() => setShowVIPOnly(!showVIPOnly)}
                >
                  <Crown className="h-4 w-4 mr-2" />
                  VIP Only
                </Button>
              </div>
            </div>

            {/* Clients List */}
            <div className="space-y-4">
              {filteredClients.map((client) => (
                <Card key={client.id} className="border border-gray-200 dark:border-gray-600 hover:shadow-lg transition-all duration-200">
                  <CardContent className="p-6">
                    <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
                      <div className="flex-1 space-y-4">
                        {/* Client Header */}
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-4">
                            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white font-semibold text-lg">
                              {client.name.charAt(0)}
                            </div>
                            <div>
                              <div className="flex items-center gap-2">
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                                  {client.name}
                                </h3>
                                {client.isVIP && (
                                  <Crown className="h-4 w-4 text-yellow-500" />
                                )}
                              </div>
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                {client.email}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant={client.isActive ? 'success' : 'secondary'}>
                              {client.isActive ? 'Active' : 'Inactive'}
                            </Badge>
                          </div>
                        </div>

                        {/* Studio & Event Info */}
                        <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 dark:text-gray-400">
                          <div className="flex items-center gap-2">
                            <Building2 className="h-4 w-4" />
                            <span>{client.studioName}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-lg">{getEventTypeIcon(client.eventType)}</span>
                            <span>{client.eventType}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            <span>{formatDate(client.eventDate)}</span>
                          </div>
                          {client.city && (
                            <div className="flex items-center gap-1">
                              <MapPin className="h-4 w-4" />
                              <span>{client.city}, {client.country}</span>
                            </div>
                          )}
                        </div>

                        {/* Stats */}
                        <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 dark:text-gray-400">
                          <div className="flex items-center gap-1">
                            <Camera className="h-4 w-4" />
                            <span>{client.matchedPhotos}/{client.totalPhotos} photos</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Download className="h-4 w-4" />
                            <span>{client.downloads} downloads</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            <span>Last access: {timeAgo(client.lastAccess)}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            <span>Joined: {formatDate(client.joinDate)}</span>
                          </div>
                        </div>

                        {/* Tags */}
                        <div className="flex flex-wrap gap-2">
                          {client.tags.map((tag, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>

                        {/* Notes */}
                        {client.notes && (
                          <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <p className="text-sm text-gray-700 dark:text-gray-300">
                              {client.notes}
                            </p>
                          </div>
                        )}
                      </div>

                      {/* Action Buttons */}
                      <div className="flex flex-col gap-2 lg:flex-row lg:items-center">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Button>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </Button>
                        <Button variant="outline" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Empty State */}
            {filteredClients.length === 0 && (
              <div className="text-center py-12">
                <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  No clients found
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                  Try adjusting your search or filter criteria.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
