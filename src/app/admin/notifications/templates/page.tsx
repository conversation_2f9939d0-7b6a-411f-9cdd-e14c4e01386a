"use client";

import { useState } from "react";
import { 
  Mail,
  MessageSquare,
  Bell,
  Edit,
  Copy,
  Trash2,
  Eye,
  Plus,
  Search,
  Filter,
  Send,
  Code,
  Type,
  Image,
  Link,
  Bold,
  Italic,
  Underline,
  List,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Save,
  X,
  CheckCircle,
  AlertTriangle,
  Clock,
  Users,
  Settings
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import AdminLayout from "@/components/admin/AdminLayout";
import { formatDate, cn } from "@/lib/utils";

interface NotificationTemplate {
  id: string;
  name: string;
  subject: string;
  type: 'email' | 'sms' | 'push' | 'in_app';
  category: 'welcome' | 'billing' | 'security' | 'marketing' | 'system' | 'reminder';
  content: string;
  variables: string[];
  isActive: boolean;
  isDefault: boolean;
  usage: number;
  lastUsed?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

interface TemplateVariable {
  name: string;
  description: string;
  example: string;
  required: boolean;
}

export default function NotificationTemplatesPage() {
  const [activeTab, setActiveTab] = useState("templates");
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [showEditor, setShowEditor] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<NotificationTemplate | null>(null);
  const [editorContent, setEditorContent] = useState("");
  const [editorSubject, setEditorSubject] = useState("");

  const [templates] = useState<NotificationTemplate[]>([
    {
      id: '1',
      name: 'Welcome Email',
      subject: 'Welcome to {{studio_name}} - Your Photos Are Ready!',
      type: 'email',
      category: 'welcome',
      content: `<h1>Welcome to {{studio_name}}!</h1>
<p>Hi {{client_name}},</p>
<p>Thank you for choosing {{studio_name}} for your {{event_type}}. Your photos are now ready for viewing!</p>
<p><a href="{{gallery_url}}" style="background: #3B82F6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">View Your Photos</a></p>
<p>You have {{photo_count}} photos waiting for you. The gallery will be available until {{expiry_date}}.</p>
<p>Best regards,<br>{{studio_name}} Team</p>`,
      variables: ['studio_name', 'client_name', 'event_type', 'gallery_url', 'photo_count', 'expiry_date'],
      isActive: true,
      isDefault: true,
      usage: 1250,
      lastUsed: '2024-01-20T14:30:00Z',
      createdAt: '2023-06-15T10:00:00Z',
      updatedAt: '2024-01-15T16:20:00Z',
      createdBy: 'Admin'
    },
    {
      id: '2',
      name: 'Payment Reminder',
      subject: 'Payment Reminder - {{studio_name}}',
      type: 'email',
      category: 'billing',
      content: `<h2>Payment Reminder</h2>
<p>Dear {{client_name}},</p>
<p>This is a friendly reminder that your payment of {{amount}} for {{service_description}} is due on {{due_date}}.</p>
<p>Please make your payment by clicking the link below:</p>
<p><a href="{{payment_url}}">Pay Now</a></p>
<p>If you have any questions, please contact us at {{contact_email}}.</p>`,
      variables: ['client_name', 'amount', 'service_description', 'due_date', 'payment_url', 'contact_email'],
      isActive: true,
      isDefault: false,
      usage: 89,
      lastUsed: '2024-01-19T09:15:00Z',
      createdAt: '2023-08-20T14:00:00Z',
      updatedAt: '2024-01-10T11:30:00Z',
      createdBy: 'Admin'
    },
    {
      id: '3',
      name: 'Security Alert',
      subject: 'Security Alert - {{studio_name}}',
      type: 'email',
      category: 'security',
      content: `<h2 style="color: #EF4444;">Security Alert</h2>
<p>Hi {{client_name}},</p>
<p>We detected a login to your account from a new device:</p>
<ul>
<li>Device: {{device_info}}</li>
<li>Location: {{location}}</li>
<li>Time: {{login_time}}</li>
</ul>
<p>If this was you, no action is needed. If you don't recognize this activity, please secure your account immediately.</p>
<p><a href="{{security_url}}">Secure My Account</a></p>`,
      variables: ['client_name', 'device_info', 'location', 'login_time', 'security_url'],
      isActive: true,
      isDefault: true,
      usage: 23,
      lastUsed: '2024-01-18T16:45:00Z',
      createdAt: '2023-09-10T12:00:00Z',
      updatedAt: '2023-12-05T09:20:00Z',
      createdBy: 'Security Team'
    },
    {
      id: '4',
      name: 'Photo Upload Complete',
      subject: 'Your {{event_type}} photos are ready!',
      type: 'push',
      category: 'system',
      content: 'Hi {{client_name}}! Your {{photo_count}} photos from {{event_date}} are now ready for viewing. Tap to see them!',
      variables: ['client_name', 'event_type', 'photo_count', 'event_date'],
      isActive: true,
      isDefault: false,
      usage: 456,
      lastUsed: '2024-01-20T11:20:00Z',
      createdAt: '2023-10-15T15:30:00Z',
      updatedAt: '2024-01-05T14:10:00Z',
      createdBy: 'System'
    }
  ]);

  const [availableVariables] = useState<TemplateVariable[]>([
    { name: 'client_name', description: 'Client\'s full name', example: 'John Smith', required: true },
    { name: 'studio_name', description: 'Photography studio name', example: 'PhotoStudio Pro', required: true },
    { name: 'event_type', description: 'Type of event', example: 'Wedding', required: false },
    { name: 'event_date', description: 'Date of the event', example: 'January 15, 2024', required: false },
    { name: 'photo_count', description: 'Number of photos', example: '150', required: false },
    { name: 'gallery_url', description: 'Link to photo gallery', example: 'https://app.com/gallery/abc123', required: false },
    { name: 'expiry_date', description: 'Gallery expiration date', example: 'February 15, 2024', required: false },
    { name: 'amount', description: 'Payment amount', example: '$299.00', required: false },
    { name: 'due_date', description: 'Payment due date', example: 'January 30, 2024', required: false },
    { name: 'payment_url', description: 'Payment link', example: 'https://app.com/pay/xyz789', required: false },
    { name: 'contact_email', description: 'Studio contact email', example: '<EMAIL>', required: false },
    { name: 'device_info', description: 'Device information', example: 'iPhone 15 Pro', required: false },
    { name: 'location', description: 'Login location', example: 'New York, NY', required: false },
    { name: 'login_time', description: 'Login timestamp', example: 'Jan 20, 2024 at 2:30 PM', required: false },
    { name: 'security_url', description: 'Account security link', example: 'https://app.com/security', required: false }
  ]);

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.subject.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === "all" || template.type === typeFilter;
    const matchesCategory = categoryFilter === "all" || template.category === categoryFilter;
    return matchesSearch && matchesType && matchesCategory;
  });

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'email': return Mail;
      case 'sms': return MessageSquare;
      case 'push': return Bell;
      case 'in_app': return Bell;
      default: return Mail;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'email': return 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300';
      case 'sms': return 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300';
      case 'push': return 'bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300';
      case 'in_app': return 'bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'welcome': return 'success';
      case 'billing': return 'warning';
      case 'security': return 'destructive';
      case 'marketing': return 'secondary';
      case 'system': return 'default';
      case 'reminder': return 'warning';
      default: return 'secondary';
    }
  };

  const handleEditTemplate = (template: NotificationTemplate) => {
    setSelectedTemplate(template);
    setEditorContent(template.content);
    setEditorSubject(template.subject);
    setShowEditor(true);
  };

  const handleCreateTemplate = () => {
    setSelectedTemplate(null);
    setEditorContent("");
    setEditorSubject("");
    setShowEditor(true);
  };

  const insertVariable = (variable: string) => {
    const textarea = document.getElementById('template-content') as HTMLTextAreaElement;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newContent = editorContent.substring(0, start) + `{{${variable}}}` + editorContent.substring(end);
      setEditorContent(newContent);
      
      // Reset cursor position
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + variable.length + 4, start + variable.length + 4);
      }, 0);
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <Mail className="h-8 w-8 text-blue-600" />
              Notification Templates
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Create and manage notification templates with dynamic content
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Template Settings
            </Button>
            <Button onClick={handleCreateTemplate} className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
              <Plus className="h-4 w-4 mr-2" />
              Create Template
            </Button>
          </div>
        </div>

        {/* Template Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <Mail className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {templates.length}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Total Templates</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {templates.filter(t => t.isActive).length}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Active Templates</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <Send className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {templates.reduce((sum, t) => sum + t.usage, 0).toLocaleString()}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Total Sent</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-orange-600 rounded-xl flex items-center justify-center">
                  <Code className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {availableVariables.length}
                  </p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">Variables</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="variables">Variables</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          {/* Templates Tab */}
          <TabsContent value="templates" className="space-y-6">
            {/* Filters */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                      <Input
                        placeholder="Search templates by name or subject..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 h-11 bg-gray-50 dark:bg-gray-700"
                      />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <select
                      value={typeFilter}
                      onChange={(e) => setTypeFilter(e.target.value)}
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="all">All Types</option>
                      <option value="email">Email</option>
                      <option value="sms">SMS</option>
                      <option value="push">Push</option>
                      <option value="in_app">In-App</option>
                    </select>
                    <select
                      value={categoryFilter}
                      onChange={(e) => setCategoryFilter(e.target.value)}
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="all">All Categories</option>
                      <option value="welcome">Welcome</option>
                      <option value="billing">Billing</option>
                      <option value="security">Security</option>
                      <option value="marketing">Marketing</option>
                      <option value="system">System</option>
                      <option value="reminder">Reminder</option>
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Templates List */}
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="space-y-4">
                  {filteredTemplates.map(template => {
                    const TypeIcon = getTypeIcon(template.type);
                    return (
                      <div key={template.id} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <div className={cn(
                              "h-10 w-10 rounded-lg flex items-center justify-center",
                              getTypeColor(template.type)
                            )}>
                              <TypeIcon className="h-5 w-5" />
                            </div>
                            <div>
                              <div className="flex items-center gap-2">
                                <h3 className="font-semibold text-gray-900 dark:text-white">
                                  {template.name}
                                </h3>
                                {template.isDefault && (
                                  <Badge variant="secondary" className="text-xs">
                                    Default
                                  </Badge>
                                )}
                                <Badge variant={getCategoryColor(template.category) as any} className="text-xs">
                                  {template.category}
                                </Badge>
                                <Badge variant={template.isActive ? 'success' : 'secondary'} className="text-xs">
                                  {template.isActive ? 'Active' : 'Inactive'}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                {template.subject}
                              </p>
                              <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-500 mt-2">
                                <span>Used {template.usage.toLocaleString()} times</span>
                                {template.lastUsed && (
                                  <>
                                    <span>•</span>
                                    <span>Last used {formatDate(template.lastUsed)}</span>
                                  </>
                                )}
                                <span>•</span>
                                <span>{template.variables.length} variables</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4 mr-2" />
                              Preview
                            </Button>
                            <Button variant="outline" size="sm" onClick={() => handleEditTemplate(template)}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </Button>
                            <Button variant="outline" size="sm">
                              <Copy className="h-4 w-4 mr-2" />
                              Duplicate
                            </Button>
                            <Button variant="outline" size="sm">
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </Button>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Variables Tab */}
          <TabsContent value="variables" className="space-y-6">
            <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Code className="h-5 w-5 text-orange-600" />
                  Available Variables
                </CardTitle>
                <CardDescription>
                  Dynamic variables you can use in your notification templates
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {availableVariables.map(variable => (
                    <div key={variable.name} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <code className="text-sm font-mono bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded">
                            {`{{${variable.name}}}`}
                          </code>
                          {variable.required && (
                            <Badge variant="destructive" className="text-xs">
                              Required
                            </Badge>
                          )}
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => insertVariable(variable.name)}
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          Insert
                        </Button>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                        {variable.description}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-500">
                        Example: {variable.example}
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle>Template Usage</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {templates
                      .sort((a, b) => b.usage - a.usage)
                      .slice(0, 5)
                      .map(template => (
                        <div key={template.id} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className={cn(
                              "h-6 w-6 rounded flex items-center justify-center",
                              getTypeColor(template.type)
                            )}>
                              {(() => {
                                const Icon = getTypeIcon(template.type);
                                return <Icon className="h-3 w-3" />;
                              })()}
                            </div>
                            <span className="text-sm font-medium">{template.name}</span>
                          </div>
                          <span className="text-sm font-bold">{template.usage.toLocaleString()}</span>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle>Template Types</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {['email', 'sms', 'push', 'in_app'].map(type => {
                      const count = templates.filter(t => t.type === type).length;
                      const usage = templates.filter(t => t.type === type).reduce((sum, t) => sum + t.usage, 0);
                      return (
                        <div key={type} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className={cn(
                              "h-6 w-6 rounded flex items-center justify-center",
                              getTypeColor(type)
                            )}>
                              {(() => {
                                const Icon = getTypeIcon(type);
                                return <Icon className="h-3 w-3" />;
                              })()}
                            </div>
                            <span className="text-sm font-medium capitalize">{type}</span>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-bold">{count} templates</p>
                            <p className="text-xs text-gray-500">{usage.toLocaleString()} sent</p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Template Editor Modal */}
        {showEditor && (
          <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
              <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-600">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {selectedTemplate ? 'Edit Template' : 'Create Template'}
                </h3>
                <Button variant="outline" size="sm" onClick={() => setShowEditor(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex-1 overflow-y-auto p-6">
                <div className="space-y-6">
                  {/* Template Details */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Template Name
                      </label>
                      <Input
                        placeholder="Enter template name"
                        defaultValue={selectedTemplate?.name}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Type
                      </label>
                      <select
                        defaultValue={selectedTemplate?.type || 'email'}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option value="email">Email</option>
                        <option value="sms">SMS</option>
                        <option value="push">Push Notification</option>
                        <option value="in_app">In-App Notification</option>
                      </select>
                    </div>
                  </div>

                  {/* Subject Line */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Subject Line
                    </label>
                    <Input
                      placeholder="Enter subject line"
                      value={editorSubject}
                      onChange={(e) => setEditorSubject(e.target.value)}
                    />
                  </div>

                  {/* Content Editor */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Template Content
                    </label>
                    <Textarea
                      id="template-content"
                      placeholder="Enter your template content here. Use {{variable_name}} for dynamic content."
                      value={editorContent}
                      onChange={(e) => setEditorContent(e.target.value)}
                      className="min-h-[300px] font-mono text-sm"
                    />
                  </div>

                  {/* Variable Shortcuts */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Quick Insert Variables
                    </label>
                    <div className="flex flex-wrap gap-2">
                      {availableVariables.slice(0, 8).map(variable => (
                        <Button
                          key={variable.name}
                          variant="outline"
                          size="sm"
                          onClick={() => insertVariable(variable.name)}
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          {variable.name}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-600">
                <Button variant="outline" onClick={() => setShowEditor(false)}>
                  Cancel
                </Button>
                <Button variant="outline">
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </Button>
                <Button>
                  <Save className="h-4 w-4 mr-2" />
                  Save Template
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
