"use client";

import { useState } from "react";
import { 
  CreditCard,
  Plus,
  Edit,
  Trash2,
  Copy,
  Check,
  X,
  Users,
  HardDrive,
  Camera,
  Zap,
  Crown,
  Star,
  TrendingUp,
  DollarSign,
  Settings,
  Eye,
  MoreHorizontal
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import AdminLayout from "@/components/admin/AdminLayout";
import { formatCurrency, cn } from "@/lib/utils";

interface Plan {
  id: string;
  name: string;
  description: string;
  price: number;
  interval: 'month' | 'year';
  isActive: boolean;
  isPopular: boolean;
  features: string[];
  limits: {
    storage: string;
    clients: string;
    photos: string;
    bandwidth: string;
    support: string;
  };
  subscriberCount: number;
  revenue: number;
  createdAt: string;
  updatedAt: string;
}

export default function PlansPage() {
  const [plans, setPlans] = useState<Plan[]>([
    {
      id: '1',
      name: 'Starter',
      description: 'Perfect for small studios just getting started',
      price: 29,
      interval: 'month',
      isActive: true,
      isPopular: false,
      features: [
        'Up to 10 clients',
        '50GB storage',
        '1,000 photos/month',
        'Basic face recognition',
        'Email support',
        'Mobile app access'
      ],
      limits: {
        storage: '50GB',
        clients: '10',
        photos: '1,000/month',
        bandwidth: '100GB/month',
        support: 'Email'
      },
      subscriberCount: 45,
      revenue: 1305,
      createdAt: '2023-06-15',
      updatedAt: '2024-01-10'
    },
    {
      id: '2',
      name: 'Professional',
      description: 'Ideal for growing studios with more clients',
      price: 79,
      interval: 'month',
      isActive: true,
      isPopular: true,
      features: [
        'Up to 50 clients',
        '200GB storage',
        '5,000 photos/month',
        'Advanced face recognition',
        'Priority support',
        'Custom branding',
        'Analytics dashboard',
        'Bulk operations'
      ],
      limits: {
        storage: '200GB',
        clients: '50',
        photos: '5,000/month',
        bandwidth: '500GB/month',
        support: 'Priority Email'
      },
      subscriberCount: 128,
      revenue: 10112,
      createdAt: '2023-06-15',
      updatedAt: '2024-01-15'
    },
    {
      id: '3',
      name: 'Enterprise',
      description: 'For large studios with unlimited needs',
      price: 199,
      interval: 'month',
      isActive: true,
      isPopular: false,
      features: [
        'Unlimited clients',
        '1TB storage',
        'Unlimited photos',
        'AI-powered recognition',
        '24/7 phone support',
        'White-label solution',
        'Advanced analytics',
        'API access',
        'Custom integrations',
        'Dedicated account manager'
      ],
      limits: {
        storage: '1TB',
        clients: 'Unlimited',
        photos: 'Unlimited',
        bandwidth: '2TB/month',
        support: '24/7 Phone'
      },
      subscriberCount: 23,
      revenue: 4577,
      createdAt: '2023-06-15',
      updatedAt: '2024-01-12'
    }
  ]);

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingPlan, setEditingPlan] = useState<Plan | null>(null);

  const totalSubscribers = plans.reduce((sum, plan) => sum + plan.subscriberCount, 0);
  const totalRevenue = plans.reduce((sum, plan) => sum + plan.revenue, 0);
  const activePlans = plans.filter(plan => plan.isActive).length;

  const togglePlanStatus = (planId: string) => {
    setPlans(prev => prev.map(plan => 
      plan.id === planId ? { ...plan, isActive: !plan.isActive } : plan
    ));
  };

  const togglePopular = (planId: string) => {
    setPlans(prev => prev.map(plan => 
      plan.id === planId ? { ...plan, isPopular: !plan.isPopular } : plan
    ));
  };

  const duplicatePlan = (plan: Plan) => {
    const newPlan = {
      ...plan,
      id: Date.now().toString(),
      name: `${plan.name} Copy`,
      subscriberCount: 0,
      revenue: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    setPlans(prev => [...prev, newPlan]);
  };

  const deletePlan = (planId: string) => {
    if (confirm('Are you sure you want to delete this plan?')) {
      setPlans(prev => prev.filter(plan => plan.id !== planId));
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <CreditCard className="h-8 w-8 text-blue-600" />
              Subscription Plans
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Create, manage, and monitor subscription plans for studios
            </p>
          </div>
          <Button 
            onClick={() => setShowCreateModal(true)}
            className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Plan
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <CreditCard className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {plans.length}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Total Plans</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <Check className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {activePlans}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Active Plans</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <Users className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {totalSubscribers}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Total Subscribers</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-orange-600 rounded-xl flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {formatCurrency(totalRevenue)}
                  </p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">Monthly Revenue</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Plans Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {plans.map((plan) => (
            <Card 
              key={plan.id} 
              className={cn(
                "border-2 transition-all duration-200 hover:shadow-lg",
                plan.isPopular 
                  ? "border-blue-500 shadow-lg scale-105" 
                  : "border-gray-200 dark:border-gray-600",
                !plan.isActive && "opacity-60"
              )}
            >
              {plan.isPopular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                  <Badge className="bg-blue-600 text-white">
                    <Star className="h-3 w-3 mr-1" />
                    Most Popular
                  </Badge>
                </div>
              )}
              
              <CardHeader className="text-center relative">
                <div className="absolute top-4 right-4 flex items-center gap-2">
                  <Switch
                    checked={plan.isActive}
                    onCheckedChange={() => togglePlanStatus(plan.id)}
                    size="sm"
                  />
                </div>
                
                <CardTitle className="text-2xl">{plan.name}</CardTitle>
                <CardDescription className="text-sm">
                  {plan.description}
                </CardDescription>
                
                <div className="mt-4">
                  <span className="text-4xl font-bold text-gray-900 dark:text-white">
                    {formatCurrency(plan.price)}
                  </span>
                  <span className="text-gray-600 dark:text-gray-400">
                    /{plan.interval}
                  </span>
                </div>
                
                <div className="flex items-center justify-center gap-4 mt-4 text-sm text-gray-600 dark:text-gray-400">
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    <span>{plan.subscriberCount} subscribers</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <TrendingUp className="h-4 w-4" />
                    <span>{formatCurrency(plan.revenue)}/mo</span>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                {/* Plan Limits */}
                <div className="grid grid-cols-2 gap-3 mb-6">
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <HardDrive className="h-5 w-5 text-blue-600 mx-auto mb-1" />
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {plan.limits.storage}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">Storage</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <Users className="h-5 w-5 text-green-600 mx-auto mb-1" />
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {plan.limits.clients}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">Clients</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <Camera className="h-5 w-5 text-purple-600 mx-auto mb-1" />
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {plan.limits.photos}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">Photos</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <Zap className="h-5 w-5 text-orange-600 mx-auto mb-1" />
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {plan.limits.bandwidth}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">Bandwidth</p>
                  </div>
                </div>

                {/* Features List */}
                <ul className="space-y-2 mb-6">
                  {plan.features.slice(0, 4).map((feature, index) => (
                    <li key={index} className="flex items-center gap-2 text-sm">
                      <Check className="h-4 w-4 text-green-600 flex-shrink-0" />
                      <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                    </li>
                  ))}
                  {plan.features.length > 4 && (
                    <li className="text-sm text-gray-500 dark:text-gray-400">
                      +{plan.features.length - 4} more features
                    </li>
                  )}
                </ul>

                {/* Action Buttons */}
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setEditingPlan(plan)}
                    className="flex-1"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => duplicatePlan(plan)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => togglePopular(plan.id)}
                  >
                    <Star className={cn(
                      "h-4 w-4",
                      plan.isPopular ? "text-yellow-500 fill-current" : "text-gray-400"
                    )} />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => deletePlan(plan.id)}
                  >
                    <Trash2 className="h-4 w-4 text-red-500" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Plan Comparison Table */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5 text-blue-600" />
              Plan Comparison
            </CardTitle>
            <CardDescription>
              Detailed comparison of all subscription plans
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200 dark:border-gray-600">
                    <th className="text-left py-3 px-4 font-semibold text-gray-900 dark:text-white">
                      Feature
                    </th>
                    {plans.map(plan => (
                      <th key={plan.id} className="text-center py-3 px-4 font-semibold text-gray-900 dark:text-white">
                        {plan.name}
                        {plan.isPopular && (
                          <Crown className="h-4 w-4 text-yellow-500 inline ml-1" />
                        )}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-gray-100 dark:border-gray-700">
                    <td className="py-3 px-4 font-medium text-gray-900 dark:text-white">Price</td>
                    {plans.map(plan => (
                      <td key={plan.id} className="text-center py-3 px-4">
                        <span className="font-bold text-lg">{formatCurrency(plan.price)}</span>
                        <span className="text-sm text-gray-500">/{plan.interval}</span>
                      </td>
                    ))}
                  </tr>
                  <tr className="border-b border-gray-100 dark:border-gray-700">
                    <td className="py-3 px-4 font-medium text-gray-900 dark:text-white">Storage</td>
                    {plans.map(plan => (
                      <td key={plan.id} className="text-center py-3 px-4 text-gray-700 dark:text-gray-300">
                        {plan.limits.storage}
                      </td>
                    ))}
                  </tr>
                  <tr className="border-b border-gray-100 dark:border-gray-700">
                    <td className="py-3 px-4 font-medium text-gray-900 dark:text-white">Clients</td>
                    {plans.map(plan => (
                      <td key={plan.id} className="text-center py-3 px-4 text-gray-700 dark:text-gray-300">
                        {plan.limits.clients}
                      </td>
                    ))}
                  </tr>
                  <tr className="border-b border-gray-100 dark:border-gray-700">
                    <td className="py-3 px-4 font-medium text-gray-900 dark:text-white">Photos/Month</td>
                    {plans.map(plan => (
                      <td key={plan.id} className="text-center py-3 px-4 text-gray-700 dark:text-gray-300">
                        {plan.limits.photos}
                      </td>
                    ))}
                  </tr>
                  <tr className="border-b border-gray-100 dark:border-gray-700">
                    <td className="py-3 px-4 font-medium text-gray-900 dark:text-white">Support</td>
                    {plans.map(plan => (
                      <td key={plan.id} className="text-center py-3 px-4 text-gray-700 dark:text-gray-300">
                        {plan.limits.support}
                      </td>
                    ))}
                  </tr>
                  <tr>
                    <td className="py-3 px-4 font-medium text-gray-900 dark:text-white">Subscribers</td>
                    {plans.map(plan => (
                      <td key={plan.id} className="text-center py-3 px-4">
                        <Badge variant="secondary" className="text-xs">
                          {plan.subscriberCount} users
                        </Badge>
                      </td>
                    ))}
                  </tr>
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
