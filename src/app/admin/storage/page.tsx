"use client";

import { useState } from "react";
import {
  HardDrive,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Building2,
  FileImage,
  Video,
  Archive,
  Calendar,
  Download,
  Upload,
  Trash2,
  AlertTriangle,
  CheckCircle,
  Activity,
  Zap,
  Server,
  Database,
  Users,
  Settings,
  RefreshCw,
  Eye,
  Filter,
  Search
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import AdminLayout from "@/components/admin/AdminLayout";
import { formatFileSize, formatDate, cn } from "@/lib/utils";

interface StorageData {
  totalStorage: number;
  usedStorage: number;
  availableStorage: number;
  storageGrowth: number;
  fileTypeBreakdown: Array<{
    type: string;
    size: number;
    count: number;
    percentage: number;
  }>;
  studioUsage: Array<{
    studioId: string;
    studioName: string;
    used: number;
    limit: number;
    fileCount: number;
    growth: number;
  }>;
  monthlyGrowth: Array<{
    month: string;
    totalSize: number;
    newFiles: number;
    deletedFiles: number;
  }>;
  dailyActivity: Array<{
    date: string;
    uploads: number;
    downloads: number;
    deletions: number;
  }>;
}

export default function StorageAnalyticsPage() {
  const [period, setPeriod] = useState("30");
  const [storageData] = useState<StorageData>({
    totalStorage: 10000000000000, // 10TB
    usedStorage: 6500000000000, // 6.5TB
    availableStorage: 3500000000000, // 3.5TB
    storageGrowth: 12.5,
    fileTypeBreakdown: [
      { type: 'JPEG', size: 4200000000000, count: 125000, percentage: 64.6 },
      { type: 'RAW', size: 1800000000000, count: 15000, percentage: 27.7 },
      { type: 'PNG', size: 350000000000, count: 8500, percentage: 5.4 },
      { type: 'MP4', size: 150000000000, count: 1200, percentage: 2.3 }
    ],
    studioUsage: [
      {
        studioId: '1',
        studioName: 'PhotoStudio Pro',
        used: 850000000000, // 850GB
        limit: 1000000000000, // 1TB
        fileCount: 45000,
        growth: 8.5
      },
      {
        studioId: '2',
        studioName: 'Wedding Memories',
        used: 1200000000000, // 1.2TB
        limit: 2000000000000, // 2TB
        fileCount: 78000,
        growth: 15.2
      },
      {
        studioId: '3',
        studioName: 'Quick Shots',
        used: 150000000000, // 150GB
        limit: 500000000000, // 500GB
        fileCount: 8500,
        growth: 22.1
      }
    ],
    monthlyGrowth: [
      { month: '2023-07', totalSize: 4800000000000, newFiles: 12500, deletedFiles: 850 },
      { month: '2023-08', totalSize: 5100000000000, newFiles: 15200, deletedFiles: 920 },
      { month: '2023-09', totalSize: 5450000000000, newFiles: 18500, deletedFiles: 1100 },
      { month: '2023-10', totalSize: 5800000000000, newFiles: 16800, deletedFiles: 950 },
      { month: '2023-11', totalSize: 6100000000000, newFiles: 19200, deletedFiles: 1200 },
      { month: '2023-12', totalSize: 6350000000000, newFiles: 14500, deletedFiles: 800 },
      { month: '2024-01', totalSize: 6500000000000, newFiles: 12800, deletedFiles: 750 }
    ],
    dailyActivity: [
      { date: '2024-01-15', uploads: 1250, downloads: 3200, deletions: 45 },
      { date: '2024-01-16', uploads: 1580, downloads: 2800, deletions: 32 },
      { date: '2024-01-17', uploads: 1420, downloads: 3500, deletions: 28 },
      { date: '2024-01-18', uploads: 1680, downloads: 4200, deletions: 55 },
      { date: '2024-01-19', uploads: 1350, downloads: 3800, deletions: 38 },
      { date: '2024-01-20', uploads: 1750, downloads: 4500, deletions: 42 }
    ]
  });

  const storagePercentage = (storageData.usedStorage / storageData.totalStorage) * 100;
  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

  const getStorageColor = () => {
    if (storagePercentage > 90) return 'destructive';
    if (storagePercentage > 75) return 'warning';
    return 'default';
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <HardDrive className="h-8 w-8 text-blue-600" />
              Storage Analytics
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Detailed insights into storage usage, growth patterns, and file management
            </p>
          </div>
          <div className="flex items-center gap-3">
            <select
              value={period}
              onChange={(e) => setPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="90">Last 90 days</option>
              <option value="365">Last year</option>
            </select>
          </div>
        </div>

        {/* Storage Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <HardDrive className="h-6 w-6 text-white" />
                </div>
                <div className="flex-1">
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {formatFileSize(storageData.usedStorage)}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Used Storage</p>
                  <Progress 
                    value={storagePercentage} 
                    className="h-2 mt-2" 
                    variant={getStorageColor()}
                  />
                  <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                    {storagePercentage.toFixed(1)}% of {formatFileSize(storageData.totalStorage)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    +{storageData.storageGrowth}%
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Monthly Growth</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <FileImage className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {storageData.fileTypeBreakdown.reduce((sum, f) => sum + f.count, 0).toLocaleString()}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Total Files</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-orange-600 rounded-xl flex items-center justify-center">
                  <Building2 className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {storageData.studioUsage.length}
                  </p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">Active Studios</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Storage Growth Over Time */}
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-blue-600" />
                Storage Growth Over Time
              </CardTitle>
              <CardDescription>
                Monthly storage usage and file count trends
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={storageData.monthlyGrowth}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" tickFormatter={(month) => formatDate(month + '-01')} />
                  <YAxis tickFormatter={(value) => formatFileSize(value)} />
                  <Tooltip 
                    labelFormatter={(month) => formatDate(month + '-01')}
                    formatter={[(value: number) => formatFileSize(value), 'Storage Used']}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="totalSize" 
                    stroke="#3B82F6" 
                    fill="#3B82F6" 
                    fillOpacity={0.6}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* File Type Breakdown */}
          <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileImage className="h-5 w-5 text-green-600" />
                File Type Distribution
              </CardTitle>
              <CardDescription>
                Storage usage by file type
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center">
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={storageData.fileTypeBreakdown}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ type, percentage }) => `${type} ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="size"
                    >
                      {storageData.fileTypeBreakdown.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={[(value: number) => formatFileSize(value), 'Size']} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="grid grid-cols-2 gap-4 mt-4">
                {storageData.fileTypeBreakdown.map((fileType, index) => (
                  <div key={fileType.type} className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {fileType.type}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {fileType.count.toLocaleString()} files • {formatFileSize(fileType.size)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Studio Usage Breakdown */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5 text-purple-600" />
              Studio Storage Usage
            </CardTitle>
            <CardDescription>
              Storage consumption and limits by studio
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {storageData.studioUsage.map((studio) => {
                const usagePercentage = (studio.used / studio.limit) * 100;
                return (
                  <div key={studio.studioId} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-semibold">
                          {studio.studioName.charAt(0)}
                        </div>
                        <div>
                          <p className="font-semibold text-gray-900 dark:text-white">
                            {studio.studioName}
                          </p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {studio.fileCount.toLocaleString()} files
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-gray-900 dark:text-white">
                          {formatFileSize(studio.used)} / {formatFileSize(studio.limit)}
                        </p>
                        <div className="flex items-center gap-2">
                          <Badge variant={studio.growth > 0 ? 'success' : 'secondary'} className="text-xs">
                            {studio.growth > 0 ? '+' : ''}{studio.growth}% growth
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">Storage Usage</span>
                        <span className="text-gray-600 dark:text-gray-400">
                          {usagePercentage.toFixed(1)}%
                        </span>
                      </div>
                      <Progress 
                        value={usagePercentage} 
                        className="h-2"
                        variant={usagePercentage > 90 ? 'destructive' : usagePercentage > 75 ? 'warning' : 'default'}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Daily Activity */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-orange-600" />
              Daily Storage Activity
            </CardTitle>
            <CardDescription>
              File uploads, downloads, and deletions over time
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={storageData.dailyActivity}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" tickFormatter={(date) => formatDate(date)} />
                <YAxis />
                <Tooltip labelFormatter={(date) => formatDate(date)} />
                <Bar dataKey="uploads" fill="#3B82F6" name="Uploads" />
                <Bar dataKey="downloads" fill="#10B981" name="Downloads" />
                <Bar dataKey="deletions" fill="#EF4444" name="Deletions" />
                <Legend />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
