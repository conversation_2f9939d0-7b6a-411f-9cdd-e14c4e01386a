"use client";

import { useState } from "react";
import { 
  FileText,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Calendar,
  User,
  Tag,
  Globe,
  Image,
  Video,
  File,
  MoreHorizontal,
  Star,
  Clock,
  TrendingUp,
  CheckCircle,
  AlertTriangle
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import AdminLayout from "@/components/admin/AdminLayout";
import { formatDate, timeAgo, cn } from "@/lib/utils";

interface ContentItem {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  type: 'page' | 'blog_post' | 'help_article' | 'announcement' | 'tutorial';
  status: 'draft' | 'published' | 'archived';
  author: string;
  authorId: string;
  featuredImage?: string;
  tags: string[];
  views: number;
  likes: number;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  seoTitle?: string;
  seoDescription?: string;
  isSticky?: boolean;
  isFeatured?: boolean;
}

export default function ContentManagementPage() {
  const [content] = useState<ContentItem[]>([
    {
      id: '1',
      title: 'Getting Started with PhotoStudio Pro',
      slug: 'getting-started-photostudio-pro',
      content: 'Complete guide to setting up your photo studio management system...',
      excerpt: 'Learn how to set up and configure your PhotoStudio Pro account for maximum efficiency.',
      type: 'tutorial',
      status: 'published',
      author: 'Admin Team',
      authorId: 'admin1',
      featuredImage: '/api/placeholder/400/200',
      tags: ['tutorial', 'getting-started', 'setup'],
      views: 1250,
      likes: 89,
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-18T14:30:00Z',
      publishedAt: '2024-01-16T09:00:00Z',
      seoTitle: 'PhotoStudio Pro Setup Guide - Complete Tutorial',
      seoDescription: 'Step-by-step guide to setting up PhotoStudio Pro for your photography business.',
      isFeatured: true
    },
    {
      id: '2',
      title: 'New Feature: Advanced Face Recognition',
      slug: 'new-feature-advanced-face-recognition',
      content: 'We are excited to announce our new advanced face recognition feature...',
      excerpt: 'Introducing AI-powered face recognition with 99.5% accuracy for better photo matching.',
      type: 'announcement',
      status: 'published',
      author: 'Product Team',
      authorId: 'product1',
      featuredImage: '/api/placeholder/400/200',
      tags: ['announcement', 'feature', 'ai', 'face-recognition'],
      views: 2100,
      likes: 156,
      createdAt: '2024-01-18T15:00:00Z',
      updatedAt: '2024-01-19T10:15:00Z',
      publishedAt: '2024-01-19T08:00:00Z',
      seoTitle: 'Advanced Face Recognition Feature Launch',
      seoDescription: 'New AI-powered face recognition feature with improved accuracy and speed.',
      isSticky: true,
      isFeatured: true
    },
    {
      id: '3',
      title: 'How to Optimize Photo Storage',
      slug: 'optimize-photo-storage',
      content: 'Best practices for managing and optimizing your photo storage...',
      excerpt: 'Tips and tricks to efficiently manage your photo storage and reduce costs.',
      type: 'blog_post',
      status: 'published',
      author: 'Sarah Johnson',
      authorId: 'author1',
      tags: ['storage', 'optimization', 'tips', 'cost-saving'],
      views: 890,
      likes: 67,
      createdAt: '2024-01-12T11:30:00Z',
      updatedAt: '2024-01-12T11:30:00Z',
      publishedAt: '2024-01-13T10:00:00Z',
      seoTitle: 'Photo Storage Optimization Guide',
      seoDescription: 'Learn how to optimize your photo storage for better performance and cost savings.'
    },
    {
      id: '4',
      title: 'Troubleshooting Upload Issues',
      slug: 'troubleshooting-upload-issues',
      content: 'Common upload problems and their solutions...',
      excerpt: 'Resolve common photo upload issues with our comprehensive troubleshooting guide.',
      type: 'help_article',
      status: 'published',
      author: 'Support Team',
      authorId: 'support1',
      tags: ['help', 'troubleshooting', 'upload', 'technical'],
      views: 1450,
      likes: 92,
      createdAt: '2024-01-10T09:00:00Z',
      updatedAt: '2024-01-17T16:20:00Z',
      publishedAt: '2024-01-11T08:00:00Z',
      seoTitle: 'Fix Photo Upload Issues - Troubleshooting Guide',
      seoDescription: 'Step-by-step solutions for common photo upload problems and errors.'
    },
    {
      id: '5',
      title: 'Privacy Policy Update',
      slug: 'privacy-policy-update',
      content: 'Important updates to our privacy policy...',
      excerpt: 'We have updated our privacy policy to better protect your data and comply with regulations.',
      type: 'page',
      status: 'draft',
      author: 'Legal Team',
      authorId: 'legal1',
      tags: ['privacy', 'policy', 'legal', 'update'],
      views: 0,
      likes: 0,
      createdAt: '2024-01-19T14:00:00Z',
      updatedAt: '2024-01-20T09:30:00Z',
      seoTitle: 'Privacy Policy - PhotoStudio Pro',
      seoDescription: 'Our commitment to protecting your privacy and personal data.'
    }
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [authorFilter, setAuthorFilter] = useState("all");

  const filteredContent = content.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesType = typeFilter === "all" || item.type === typeFilter;
    const matchesStatus = statusFilter === "all" || item.status === statusFilter;
    const matchesAuthor = authorFilter === "all" || item.author === authorFilter;
    
    return matchesSearch && matchesType && matchesStatus && matchesAuthor;
  });

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'page': return FileText;
      case 'blog_post': return FileText;
      case 'help_article': return FileText;
      case 'announcement': return Globe;
      case 'tutorial': return Video;
      default: return File;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'success';
      case 'draft': return 'warning';
      case 'archived': return 'secondary';
      default: return 'secondary';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'page': return 'default';
      case 'blog_post': return 'secondary';
      case 'help_article': return 'success';
      case 'announcement': return 'destructive';
      case 'tutorial': return 'warning';
      default: return 'secondary';
    }
  };

  const totalContent = content.length;
  const publishedContent = content.filter(c => c.status === 'published').length;
  const draftContent = content.filter(c => c.status === 'draft').length;
  const totalViews = content.reduce((sum, c) => sum + c.views, 0);
  const totalLikes = content.reduce((sum, c) => sum + c.likes, 0);
  const authors = [...new Set(content.map(c => c.author))];

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <FileText className="h-8 w-8 text-blue-600" />
              Content Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Create, manage, and publish content across your platform
            </p>
          </div>
          <Button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
            <Plus className="h-4 w-4 mr-2" />
            Create Content
          </Button>
        </div>

        {/* Content Stats */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          <Card className="border-0 shadow-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center">
                  <FileText className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {totalContent}
                  </p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Total Content</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {publishedContent}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">Published</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-yellow-600 rounded-xl flex items-center justify-center">
                  <Edit className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
                    {draftContent}
                  </p>
                  <p className="text-sm text-yellow-600 dark:text-yellow-400">Drafts</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-purple-600 rounded-xl flex items-center justify-center">
                  <Eye className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {totalViews.toLocaleString()}
                  </p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Total Views</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 bg-red-600 rounded-xl flex items-center justify-center">
                  <Star className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-red-900 dark:text-red-100">
                    {totalLikes.toLocaleString()}
                  </p>
                  <p className="text-sm text-red-600 dark:text-red-400">Total Likes</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Content List */}
        <Card className="border-0 shadow-md bg-white dark:bg-gray-800">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  All Content
                </CardTitle>
                <CardDescription>
                  Manage your website content, blog posts, and documentation
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Filters */}
            <div className="flex flex-col lg:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    placeholder="Search content by title, content, or tags..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 h-11 bg-gray-50 dark:bg-gray-700"
                  />
                </div>
              </div>
              <div className="flex flex-wrap gap-2">
                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">All Types</option>
                  <option value="page">Pages</option>
                  <option value="blog_post">Blog Posts</option>
                  <option value="help_article">Help Articles</option>
                  <option value="announcement">Announcements</option>
                  <option value="tutorial">Tutorials</option>
                </select>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">All Status</option>
                  <option value="published">Published</option>
                  <option value="draft">Draft</option>
                  <option value="archived">Archived</option>
                </select>
                <select
                  value={authorFilter}
                  onChange={(e) => setAuthorFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">All Authors</option>
                  {authors.map(author => (
                    <option key={author} value={author}>{author}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Content Items */}
            <div className="space-y-4">
              {filteredContent.map((item) => {
                const TypeIcon = getTypeIcon(item.type);
                return (
                  <Card key={item.id} className="border border-gray-200 dark:border-gray-600 hover:shadow-lg transition-all duration-200">
                    <CardContent className="p-6">
                      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
                        <div className="flex-1 space-y-4">
                          {/* Content Header */}
                          <div className="flex items-start justify-between">
                            <div className="flex items-center gap-4">
                              {item.featuredImage && (
                                <img
                                  src={item.featuredImage}
                                  alt={item.title}
                                  className="w-16 h-16 object-cover rounded-lg"
                                />
                              )}
                              <div className={cn(
                                "h-12 w-12 rounded-lg flex items-center justify-center",
                                !item.featuredImage && "bg-blue-100 dark:bg-blue-900/20"
                              )}>
                                {!item.featuredImage && (
                                  <TypeIcon className="h-6 w-6 text-blue-600" />
                                )}
                              </div>
                              <div>
                                <div className="flex items-center gap-2">
                                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                                    {item.title}
                                  </h3>
                                  {item.isSticky && (
                                    <Badge variant="destructive" className="text-xs">
                                      Sticky
                                    </Badge>
                                  )}
                                  {item.isFeatured && (
                                    <Badge variant="warning" className="text-xs">
                                      <Star className="h-3 w-3 mr-1" />
                                      Featured
                                    </Badge>
                                  )}
                                </div>
                                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                  {item.excerpt}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant={getStatusColor(item.status) as any}>
                                {item.status}
                              </Badge>
                              <Badge variant={getTypeColor(item.type) as any}>
                                {item.type.replace('_', ' ')}
                              </Badge>
                            </div>
                          </div>

                          {/* Content Meta */}
                          <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 dark:text-gray-400">
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4" />
                              <span>{item.author}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4" />
                              <span>Created {formatDate(item.createdAt)}</span>
                            </div>
                            {item.publishedAt && (
                              <div className="flex items-center gap-2">
                                <Globe className="h-4 w-4" />
                                <span>Published {formatDate(item.publishedAt)}</span>
                              </div>
                            )}
                            <div className="flex items-center gap-2">
                              <Clock className="h-4 w-4" />
                              <span>Updated {timeAgo(item.updatedAt)}</span>
                            </div>
                          </div>

                          {/* Performance Metrics */}
                          <div className="flex items-center gap-6 text-sm text-gray-600 dark:text-gray-400">
                            <div className="flex items-center gap-1">
                              <Eye className="h-4 w-4" />
                              <span>{item.views.toLocaleString()} views</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Star className="h-4 w-4" />
                              <span>{item.likes.toLocaleString()} likes</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <TrendingUp className="h-4 w-4" />
                              <span>/{item.slug}</span>
                            </div>
                          </div>

                          {/* Tags */}
                          <div className="flex flex-wrap gap-2">
                            {item.tags.map((tag, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                <Tag className="h-3 w-3 mr-1" />
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex flex-col gap-2 lg:flex-row lg:items-center">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            Preview
                          </Button>
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </Button>
                          <Button variant="outline" size="sm">
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </Button>
                          <Button variant="outline" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* Empty State */}
            {filteredContent.length === 0 && (
              <div className="text-center py-12">
                <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  No content found
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                  Try adjusting your search or filter criteria.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
