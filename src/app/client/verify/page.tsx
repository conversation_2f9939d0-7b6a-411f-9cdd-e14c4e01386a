"use client";

import { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Camera, CheckCircle, XCircle, RotateCcw, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function ClientVerifyPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<'pending' | 'success' | 'failed' | 'no-profile'>('pending');
  const [error, setError] = useState("");
  const [cameraActive, setCameraActive] = useState(false);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const router = useRouter();

  useEffect(() => {
    // Check if client is authenticated
    const token = localStorage.getItem("clientToken");
    if (!token) {
      router.push("/client/access");
      return;
    }

    // Auto-start camera
    startCamera();

    return () => {
      stopCamera();
    };
  }, [router]);

  const startCamera = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 640 },
          height: { ideal: 480 },
          facingMode: 'user',
        },
      });

      setStream(mediaStream);
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
      setCameraActive(true);
    } catch (error) {
      console.error('Error accessing camera:', error);
      setError('Unable to access camera. Please ensure camera permissions are granted.');
    }
  };

  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
    setCameraActive(false);
  };

  const captureAndVerify = async () => {
    if (!videoRef.current || !canvasRef.current) return;

    setIsLoading(true);
    setError("");

    try {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');

      if (!context) return;

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Draw current video frame to canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convert canvas to blob
      canvas.toBlob(async (blob) => {
        if (!blob) return;

        // In a real implementation, you would:
        // 1. Send the image to face-api.js for face detection
        // 2. Extract face descriptor
        // 3. Send descriptor to verification API

        // For now, we'll simulate the process
        await simulateFaceVerification();
      }, 'image/jpeg', 0.8);

    } catch (error) {
      console.error('Error during verification:', error);
      setError('Verification failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const simulateFaceVerification = async () => {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Simulate random verification result for demo
    const random = Math.random();
    if (random > 0.7) {
      setVerificationStatus('success');
    } else if (random > 0.3) {
      setVerificationStatus('failed');
    } else {
      setVerificationStatus('no-profile');
    }
  };

  const proceedToGallery = () => {
    router.push("/client/gallery");
  };

  const retryVerification = () => {
    setVerificationStatus('pending');
    setError("");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Camera className="h-12 w-12 text-blue-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900">Face Verification</h1>
          <p className="text-gray-600 mt-2">Verify your identity to access your photos</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Identity Verification</CardTitle>
            <CardDescription>
              Look directly at the camera and click "Verify" to confirm your identity
            </CardDescription>
          </CardHeader>
          <CardContent>
            {verificationStatus === 'pending' && (
              <div className="space-y-6">
                {/* Camera Feed */}
                <div className="camera-container">
                  <video
                    ref={videoRef}
                    autoPlay
                    playsInline
                    muted
                    className="w-full rounded-lg bg-gray-900"
                    style={{ maxHeight: '400px' }}
                  />
                  <canvas ref={canvasRef} className="hidden" />
                  
                  {!cameraActive && (
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-900 rounded-lg">
                      <div className="text-center text-white">
                        <Camera className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>Camera not active</p>
                        <Button 
                          variant="outline" 
                          onClick={startCamera}
                          className="mt-4 text-black"
                        >
                          Start Camera
                        </Button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Instructions */}
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-medium text-blue-900 mb-2">Verification Instructions:</h3>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Position your face clearly in the camera frame</li>
                    <li>• Ensure good lighting on your face</li>
                    <li>• Look directly at the camera</li>
                    <li>• Click "Verify Identity" when ready</li>
                  </ul>
                </div>

                {/* Error Message */}
                {error && (
                  <div className="text-red-600 text-sm bg-red-50 p-3 rounded-md">
                    {error}
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-4">
                  <Button
                    onClick={captureAndVerify}
                    disabled={!cameraActive || isLoading}
                    className="flex-1"
                  >
                    {isLoading ? "Verifying..." : "Verify Identity"}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => router.push("/client/gallery")}
                  >
                    Skip Verification
                  </Button>
                </div>
              </div>
            )}

            {verificationStatus === 'success' && (
              <div className="text-center space-y-6">
                <div className="flex justify-center">
                  <CheckCircle className="h-16 w-16 text-green-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-green-600 mb-2">
                    Verification Successful!
                  </h3>
                  <p className="text-gray-600">
                    Your identity has been verified. You can now access your photo gallery.
                  </p>
                </div>
                <Button onClick={proceedToGallery} className="w-full">
                  Access Photo Gallery
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            )}

            {verificationStatus === 'failed' && (
              <div className="text-center space-y-6">
                <div className="flex justify-center">
                  <XCircle className="h-16 w-16 text-red-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-red-600 mb-2">
                    Verification Failed
                  </h3>
                  <p className="text-gray-600">
                    We couldn't verify your identity. Please try again or contact your photographer.
                  </p>
                </div>
                <div className="flex gap-4">
                  <Button onClick={retryVerification} className="flex-1">
                    <RotateCcw className="mr-2 h-4 w-4" />
                    Try Again
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => router.push("/client/gallery")}
                  >
                    Continue Anyway
                  </Button>
                </div>
              </div>
            )}

            {verificationStatus === 'no-profile' && (
              <div className="text-center space-y-6">
                <div className="flex justify-center">
                  <Camera className="h-16 w-16 text-yellow-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-yellow-600 mb-2">
                    No Face Profile Found
                  </h3>
                  <p className="text-gray-600">
                    You don't have a face profile set up yet. You can still access your photos, 
                    but consider enrolling your face for enhanced security.
                  </p>
                </div>
                <div className="flex gap-4">
                  <Button onClick={proceedToGallery} className="flex-1">
                    Continue to Gallery
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                  <Button variant="outline" onClick={retryVerification}>
                    Enroll Face Profile
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Back Link */}
        <div className="mt-6 text-center">
          <button
            onClick={() => router.push("/client/access")}
            className="text-blue-600 hover:underline text-sm"
          >
            ← Back to Access
          </button>
        </div>
      </div>
    </div>
  );
}
