"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, Eye, EyeOff, <PERSON><PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function ClientAccessPage() {
  const [accessMethod, setAccessMethod] = useState<"link" | "qr">("link");
  const [formData, setFormData] = useState({
    accessCode: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      const response = await fetch("/api/client/access", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        // Store client token
        localStorage.setItem("clientToken", data.token);
        // Redirect to face verification
        router.push("/client/verify");
      } else {
        setError(data.message || "Access failed");
      }
    } catch (error) {
      setError("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Camera className="h-12 w-12 text-blue-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900">Studio ERP</h1>
          <p className="text-gray-600 mt-2">Access your photos</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Client Access</CardTitle>
            <CardDescription>
              Enter your access code and password to view your photos
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Access Method Selection */}
            <div className="mb-6">
              <div className="flex space-x-2">
                <button
                  type="button"
                  onClick={() => setAccessMethod("link")}
                  className={`flex-1 py-2 px-3 text-sm rounded-md border transition-colors flex items-center justify-center ${
                    accessMethod === "link"
                      ? "bg-blue-600 text-white border-blue-600"
                      : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
                  }`}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Access Link
                </button>
                <button
                  type="button"
                  onClick={() => setAccessMethod("qr")}
                  className={`flex-1 py-2 px-3 text-sm rounded-md border transition-colors flex items-center justify-center ${
                    accessMethod === "qr"
                      ? "bg-blue-600 text-white border-blue-600"
                      : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
                  }`}
                >
                  <Scan className="h-4 w-4 mr-2" />
                  QR Code
                </button>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Access Code */}
              <div className="space-y-2">
                <label htmlFor="accessCode" className="text-sm font-medium">
                  {accessMethod === "link" ? "Access Code" : "QR Code Data"}
                </label>
                <Input
                  id="accessCode"
                  type="text"
                  placeholder={
                    accessMethod === "link" 
                      ? "Enter your access code" 
                      : "Scan QR code or enter code manually"
                  }
                  value={formData.accessCode}
                  onChange={(e) =>
                    setFormData({ ...formData, accessCode: e.target.value })
                  }
                  required
                />
                {accessMethod === "qr" && (
                  <div className="mt-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="w-full"
                    >
                      <Scan className="h-4 w-4 mr-2" />
                      Scan QR Code
                    </Button>
                  </div>
                )}
              </div>

              {/* Password */}
              <div className="space-y-2">
                <label htmlFor="password" className="text-sm font-medium">
                  Password
                </label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter your password"
                    value={formData.password}
                    onChange={(e) =>
                      setFormData({ ...formData, password: e.target.value })
                    }
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </div>

              {/* Error Message */}
              {error && (
                <div className="text-red-600 text-sm bg-red-50 p-3 rounded-md">
                  {error}
                </div>
              )}

              {/* Submit Button */}
              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? "Verifying..." : "Access Photos"}
              </Button>
            </form>

            {/* Info */}
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="text-sm font-medium text-blue-900 mb-2">
                How to access your photos:
              </h3>
              <ul className="text-xs text-blue-800 space-y-1">
                <li>• Use the access link or QR code provided by your photographer</li>
                <li>• Enter the password given to you</li>
                <li>• Complete face verification to view your photos</li>
                <li>• Download your favorite photos</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Back to Home */}
        <div className="mt-6 text-center">
          <button
            onClick={() => router.push("/")}
            className="text-blue-600 hover:underline text-sm"
          >
            ← Back to Home
          </button>
        </div>
      </div>
    </div>
  );
}
