"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { 
  Camera, 
  Heart, 
  Download, 
  MessageCircle, 
  Filter,
  Grid,
  List,
  Search,
  LogOut
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface Photo {
  id: string;
  filename: string;
  originalName: string;
  path: string;
  size: string;
  uploadedAt: string;
  isFavorited: boolean;
  event?: {
    id: string;
    name: string;
    date: string;
    location?: string;
  };
  comments: {
    id: string;
    content: string;
    createdAt: string;
  }[];
  _count: {
    favorites: number;
    comments: number;
  };
}

export default function ClientGalleryPage() {
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [search, setSearch] = useState("");
  const [selectedEvent, setSelectedEvent] = useState("");
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [client, setClient] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    // Check authentication
    const token = localStorage.getItem("clientToken");
    if (!token) {
      router.push("/client/access");
      return;
    }

    // Get client info from token (in real app, decode JWT)
    const clientInfo = localStorage.getItem("clientInfo");
    if (clientInfo) {
      setClient(JSON.parse(clientInfo));
    }

    fetchPhotos();
  }, [page, selectedEvent, router]);

  const fetchPhotos = async () => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "20",
        ...(selectedEvent && { eventId: selectedEvent }),
      });

      const response = await fetch(`/api/client/photos?${params}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("clientToken")}`,
        },
      });

      const data = await response.json();
      if (data.success) {
        setPhotos(data.data.photos || []);
        setTotalPages(data.data.totalPages || 1);
      } else {
        console.error("Failed to fetch photos:", data.message);
        // If unauthorized, redirect to access page
        if (response.status === 401) {
          localStorage.removeItem("clientToken");
          localStorage.removeItem("clientInfo");
          router.push("/client/access");
        }
      }
    } catch (error) {
      console.error("Error fetching photos:", error);
    } finally {
      setLoading(false);
    }
  };

  const toggleFavorite = async (photoId: string, isFavorited: boolean) => {
    try {
      const response = await fetch("/api/client/photos", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("clientToken")}`,
        },
        body: JSON.stringify({
          photoId,
          action: isFavorited ? "unfavorite" : "favorite",
        }),
      });

      if (response.ok) {
        // Update local state
        setPhotos(photos.map(photo => 
          photo.id === photoId 
            ? { ...photo, isFavorited: !isFavorited }
            : photo
        ));
      }
    } catch (error) {
      console.error("Error toggling favorite:", error);
    }
  };

  const downloadPhoto = async (photo: Photo) => {
    try {
      const response = await fetch("/api/client/photos", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("clientToken")}`,
        },
        body: JSON.stringify({
          photoId: photo.id,
          action: "download",
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.downloadUrl) {
          // Create download link
          const link = document.createElement("a");
          link.href = data.downloadUrl;
          link.download = data.filename || photo.originalName;
          link.target = "_blank";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
      } else {
        console.error("Download failed");
        // Fallback to direct path download
        const link = document.createElement('a');
        link.href = `/api/photos/download/${photo.id}`;
        link.download = photo.originalName;
        link.target = "_blank";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error("Error downloading photo:", error);
      // Fallback to direct path download
      const link = document.createElement('a');
      link.href = `/api/photos/download/${photo.id}`;
      link.download = photo.originalName;
      link.target = "_blank";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem("clientToken");
    localStorage.removeItem("clientInfo");
    router.push("/");
  };

  const formatFileSize = (bytes: string): string => {
    const size = Number(bytes);
    if (size === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(size) / Math.log(k));
    return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Camera className="h-8 w-8 text-blue-600" />
              <span className="ml-2 text-xl font-bold text-gray-900">Your Photo Gallery</span>
            </div>
            <div className="flex items-center space-x-4">
              {client && (
                <span className="text-sm text-gray-600">
                  Welcome, {client.name}
                </span>
              )}
              <Button variant="outline" size="sm" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Message */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Your Photos</h1>
          <p className="text-gray-600">
            {photos.length} photos found from your events
          </p>
        </div>

        {/* Filters and Controls */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
              <div className="flex-1 flex gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search photos..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <select
                  value={selectedEvent}
                  onChange={(e) => setSelectedEvent(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="">All Events</option>
                  {/* In real app, populate with actual events */}
                  <option value="event1">Wedding Ceremony</option>
                  <option value="event2">Reception Party</option>
                </select>
              </div>
              <div className="flex gap-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Photos Grid/List */}
        {photos.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <Camera className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Photos Found</h3>
              <p className="text-gray-600">
                No photos have been matched to your profile yet. Please check back later or contact your photographer.
              </p>
            </CardContent>
          </Card>
        ) : (
          <>
            {viewMode === 'grid' ? (
              <div className="photo-grid">
                {photos.map((photo) => (
                  <Card key={photo.id} className="photo-item group">
                    <div className="relative aspect-square overflow-hidden rounded-t-lg">
                      <img
                        src={photo.path}
                        alt={photo.originalName}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-2">
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() => toggleFavorite(photo.id, photo.isFavorited)}
                          >
                            <Heart className={`h-4 w-4 ${photo.isFavorited ? 'fill-red-500 text-red-500' : ''}`} />
                          </Button>
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() => downloadPhoto(photo)}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {photo.originalName}
                          </p>
                          <p className="text-xs text-gray-500">
                            {formatFileSize(photo.size)}
                          </p>
                          {photo.event && (
                            <p className="text-xs text-blue-600">
                              {photo.event.name}
                            </p>
                          )}
                        </div>
                        <div className="flex items-center gap-1 text-xs text-gray-500">
                          <Heart className="h-3 w-3" />
                          <span>{photo._count.favorites}</span>
                          <MessageCircle className="h-3 w-3 ml-2" />
                          <span>{photo._count.comments}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {photos.map((photo) => (
                  <Card key={photo.id}>
                    <CardContent className="p-6">
                      <div className="flex items-center gap-6">
                        <div className="w-24 h-24 rounded-lg overflow-hidden flex-shrink-0">
                          <img
                            src={photo.path}
                            alt={photo.originalName}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="text-lg font-medium text-gray-900 truncate">
                            {photo.originalName}
                          </h3>
                          <p className="text-sm text-gray-600">
                            {formatFileSize(photo.size)} • {new Date(photo.uploadedAt).toLocaleDateString()}
                          </p>
                          {photo.event && (
                            <p className="text-sm text-blue-600">
                              {photo.event.name} • {photo.event.location}
                            </p>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => toggleFavorite(photo.id, photo.isFavorited)}
                          >
                            <Heart className={`h-4 w-4 ${photo.isFavorited ? 'fill-red-500 text-red-500' : ''}`} />
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => downloadPhoto(photo)}
                          >
                            <Download className="h-4 w-4 mr-2" />
                            Download
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center mt-8">
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    disabled={page === 1}
                    onClick={() => setPage(page - 1)}
                  >
                    Previous
                  </Button>
                  <span className="flex items-center px-4 py-2 text-sm text-gray-700">
                    Page {page} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    disabled={page === totalPages}
                    onClick={() => setPage(page + 1)}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
