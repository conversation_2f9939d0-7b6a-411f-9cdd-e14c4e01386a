"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Camera, Users, Shield } from "lucide-react";

export default function TestPage() {
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <h1 className="text-4xl font-bold text-gray-900 text-center">
          Component Test Page
        </h1>
        
        {/* Buttons */}
        <Card>
          <CardHeader>
            <CardTitle>Buttons</CardTitle>
            <CardDescription>Different button variants</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-4">
              <Button>Default Button</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="destructive">Destructive</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="link">Link</Button>
            </div>
            <div className="flex flex-wrap gap-4">
              <Button size="sm">Small</Button>
              <Button size="default">Default</Button>
              <Button size="lg">Large</Button>
            </div>
          </CardContent>
        </Card>

        {/* Inputs */}
        <Card>
          <CardHeader>
            <CardTitle>Inputs</CardTitle>
            <CardDescription>Form input components</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Input placeholder="Enter your email" type="email" />
            <Input placeholder="Enter your password" type="password" />
            <Input placeholder="Disabled input" disabled />
          </CardContent>
        </Card>

        {/* Icons */}
        <Card>
          <CardHeader>
            <CardTitle>Icons</CardTitle>
            <CardDescription>Lucide React icons</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <Camera className="h-8 w-8 text-blue-600" />
              <Users className="h-8 w-8 text-green-600" />
              <Shield className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        {/* Colors */}
        <Card>
          <CardHeader>
            <CardTitle>Colors</CardTitle>
            <CardDescription>Tailwind color palette</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-6 gap-4">
              <div className="h-16 bg-blue-500 rounded flex items-center justify-center text-white text-sm">Blue</div>
              <div className="h-16 bg-green-500 rounded flex items-center justify-center text-white text-sm">Green</div>
              <div className="h-16 bg-red-500 rounded flex items-center justify-center text-white text-sm">Red</div>
              <div className="h-16 bg-yellow-500 rounded flex items-center justify-center text-white text-sm">Yellow</div>
              <div className="h-16 bg-purple-500 rounded flex items-center justify-center text-white text-sm">Purple</div>
              <div className="h-16 bg-gray-500 rounded flex items-center justify-center text-white text-sm">Gray</div>
            </div>
          </CardContent>
        </Card>

        {/* Grid Layout */}
        <Card>
          <CardHeader>
            <CardTitle>Grid Layout</CardTitle>
            <CardDescription>Responsive grid system</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-blue-100 p-4 rounded">Grid Item 1</div>
              <div className="bg-green-100 p-4 rounded">Grid Item 2</div>
              <div className="bg-red-100 p-4 rounded">Grid Item 3</div>
              <div className="bg-yellow-100 p-4 rounded">Grid Item 4</div>
              <div className="bg-purple-100 p-4 rounded">Grid Item 5</div>
              <div className="bg-gray-100 p-4 rounded">Grid Item 6</div>
            </div>
          </CardContent>
        </Card>

        {/* Typography */}
        <Card>
          <CardHeader>
            <CardTitle>Typography</CardTitle>
            <CardDescription>Text styles and sizes</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <h1 className="text-4xl font-bold">Heading 1</h1>
            <h2 className="text-3xl font-semibold">Heading 2</h2>
            <h3 className="text-2xl font-medium">Heading 3</h3>
            <p className="text-lg">Large paragraph text</p>
            <p className="text-base">Regular paragraph text</p>
            <p className="text-sm text-gray-600">Small muted text</p>
          </CardContent>
        </Card>

        <div className="text-center">
          <Button onClick={() => window.history.back()}>
            ← Back to Home
          </Button>
        </div>
      </div>
    </div>
  );
}
