# Studio ERP - Photo Management System

A complete ERP platform for photography studios to manage photo distribution using advanced face recognition technology. Studios can upload bulk photos, and clients can securely access only their matched photos through password protection and face verification.

## 🚀 Features

### Admin Panel
- **Studio Management**: Approve/block studios, view platform statistics
- **Subscription Plans**: Create and manage subscription tiers
- **Analytics**: Platform-wide usage reports and revenue tracking
- **System Monitoring**: Overall platform health and performance

### Studio Dashboard
- **Client Management**: Add clients, generate QR codes and secure access links
- **Bulk Photo Upload**: Upload hundreds of photos with automatic face detection
- **Face Recognition**: AI-powered automatic photo matching to client profiles
- **Storage Management**: Track usage against subscription limits
- **Analytics**: Client engagement and business performance metrics
- **Subscription Tools**: Upgrade plans, view billing history

### Client Portal
- **Secure Access**: Password-protected galleries with QR/link access
- **Face Verification**: Live face scan matching for photo access
- **Photo Gallery**: View only matched photos with download capabilities
- **Favorites & Comments**: Heart photos and leave feedback
- **Mobile Responsive**: Optimized for all devices

### Core Technology
- **Face Recognition**: Advanced AI using face-api.js
- **Security**: JWT authentication, encrypted data, signed URLs
- **Storage**: AWS S3/Cloudinary integration with usage monitoring
- **Notifications**: Email and WhatsApp alerts
- **Billing**: Razorpay/Stripe integration for subscriptions

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Node.js
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT with bcrypt password hashing
- **Face Recognition**: face-api.js
- **File Storage**: AWS S3 or Cloudinary
- **Payments**: Razorpay/Stripe
- **UI Components**: Radix UI, Lucide React icons

## 📋 Prerequisites

- Node.js 18+ and npm
- PostgreSQL database
- AWS S3 bucket or Cloudinary account (for file storage)
- Razorpay or Stripe account (for payments)

## 🚀 Quick Start

### 1. Clone and Install

```bash
git clone <repository-url>
cd studio-erp
npm install
```

### 2. Environment Setup

Copy the environment file and configure:

```bash
cp .env.example .env
```

Update `.env` with your configuration:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/studio_erp"

# JWT Secret
JWT_SECRET="your-super-secret-jwt-key-here"

# File Storage (choose one)
AWS_ACCESS_KEY_ID="your-aws-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret"
AWS_S3_BUCKET="your-bucket-name"

# OR Cloudinary
CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"

# Payment Gateway
RAZORPAY_KEY_ID="your-razorpay-key"
RAZORPAY_KEY_SECRET="your-razorpay-secret"

# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
```

### 3. Database Setup

```bash
# Generate Prisma client
npm run db:generate

# Push schema to database
npm run db:push

# Optional: Run migrations (for production)
npm run db:migrate

# Optional: Seed database with sample data
npm run db:seed
```

### 4. Start Development Server

```bash
npm run dev
```

Visit `http://localhost:3000` to see the application.

## 📁 Project Structure

```
studio-erp/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── admin/             # Admin panel pages
│   │   ├── studio/            # Studio dashboard pages
│   │   ├── client/            # Client portal pages
│   │   ├── auth/              # Authentication pages
│   │   └── api/               # API routes
│   ├── components/            # React components
│   │   ├── ui/                # Base UI components
│   │   ├── admin/             # Admin-specific components
│   │   ├── studio/            # Studio-specific components
│   │   ├── client/            # Client-specific components
│   │   └── common/            # Shared components
│   ├── lib/                   # Utility libraries
│   │   ├── auth/              # Authentication utilities
│   │   ├── db/                # Database connection
│   │   ├── face-recognition/  # Face recognition utilities
│   │   ├── storage/           # File storage utilities
│   │   ├── notifications/     # Email/SMS utilities
│   │   └── utils/             # General utilities
│   ├── types/                 # TypeScript type definitions
│   ├── hooks/                 # Custom React hooks
│   └── contexts/              # React contexts
├── prisma/                    # Database schema and migrations
├── public/                    # Static assets
└── README.md
```

## 🔧 Development Commands

```bash
# Development
npm run dev              # Start development server
npm run build           # Build for production
npm run start           # Start production server

# Database
npm run db:generate     # Generate Prisma client
npm run db:push         # Push schema changes
npm run db:migrate      # Run migrations
npm run db:studio       # Open Prisma Studio
npm run db:seed         # Seed database

# Code Quality
npm run lint            # Run ESLint
```

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically on push

### Manual Deployment

```bash
# Build the application
npm run build

# Start production server
npm run start
```

## 🔐 Default Admin Account

After seeding the database, use these credentials:

- **Email**: <EMAIL>
- **Password**: Admin@123

## 📖 API Documentation

### Authentication Endpoints

- `POST /api/auth/login` - User login
- `POST /api/auth/register` - Studio registration
- `POST /api/auth/logout` - User logout

### Studio Endpoints

- `GET /api/studio/dashboard` - Dashboard statistics
- `POST /api/studio/clients` - Add new client
- `GET /api/studio/clients` - List all clients
- `POST /api/studio/upload` - Upload photos

### Client Endpoints

- `POST /api/client/access` - Access gallery
- `GET /api/client/photos` - Get matched photos
- `POST /api/client/verify` - Face verification

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Ensure code quality with linting and type checking
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Email: <EMAIL>

## 🔧 Development

### Code Quality

```bash
# Run linting
npm run lint

# Fix linting issues
npm run lint:fix

# Type checking
npm run type-check
```

## 📚 Documentation

- **API Documentation**: `/docs/API_DOCUMENTATION.md`
- **Deployment Guide**: `/docs/DEPLOYMENT_GUIDE.md`
- **Architecture Overview**: `/docs/ARCHITECTURE.md`

## 🔄 Roadmap

- [x] Core authentication system
- [x] Photo upload and management
- [x] Face recognition integration
- [x] Client portal
- [x] Admin dashboard
- [x] API documentation
- [x] Test suite
- [x] Deployment guides
- [ ] Mobile app for studios
- [ ] Advanced analytics dashboard
- [ ] Video support
- [ ] Multi-language support
- [ ] Advanced face recognition models
- [ ] Bulk client import
- [ ] Custom branding themes
- [ ] API webhooks
