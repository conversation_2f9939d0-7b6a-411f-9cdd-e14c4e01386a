{"name": "photo-studio-erp", "version": "1.0.0", "description": "Photo Studio ERP System with Face Recognition", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma migrate reset", "postinstall": "prisma generate", "analyze": "cross-env ANALYZE=true next build"}, "dependencies": {"@next/bundle-analyzer": "^15.0.0", "@prisma/client": "^6.12.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@sentry/nextjs": "^7.85.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/qrcode": "^1.5.5", "bcryptjs": "^3.0.2", "canvas": "^2.11.2", "clsx": "^2.1.1", "face-api.js": "^0.22.2", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "multer": "^2.0.2", "next": "15.4.2", "nodemailer": "^6.9.7", "prisma": "^6.12.0", "qrcode": "^1.5.4", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "^3.1.0", "sharp": "^0.32.6", "tailwind-merge": "^3.3.1", "zod": "^3.22.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20", "@types/nodemailer": "^6.4.14", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.4.2", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["photo-studio", "erp", "face-recognition", "nextjs", "typescript", "prisma", "postgresql"], "author": "Your Name <<EMAIL>>", "license": "MIT"}