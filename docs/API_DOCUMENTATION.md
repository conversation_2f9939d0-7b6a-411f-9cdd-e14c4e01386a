# Photo Studio ERP - API Documentation

## Overview

This document provides comprehensive documentation for the Photo Studio ERP API endpoints. The API follows RESTful conventions and uses JWT authentication for secure access.

## Base URL

```
Production: https://your-domain.com/api
Development: http://localhost:3000/api
```

## Authentication

All protected endpoints require a JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Response Format

All API responses follow this standard format:

```json
{
  "success": boolean,
  "message": string,
  "data": object | array,
  "errors": array (optional)
}
```

## Error Codes

- `200` - Success
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `500` - Internal Server Error

## Authentication Endpoints

### POST /api/auth/login

Login with email and password.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "userType": "studio" | "admin"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "name": "User Name",
      "userType": "studio"
    },
    "token": "jwt-token"
  }
}
```

### POST /api/auth/register

Register a new studio account.

**Request Body:**
```json
{
  "name": "Studio Name",
  "email": "<EMAIL>",
  "password": "Password123!",
  "confirmPassword": "Password123!",
  "businessName": "Business Name",
  "phone": "+**********"
}
```

### POST /api/auth/logout

Logout current user (clears cookies).

### POST /api/auth/forgot-password

Initiate password reset process.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "userType": "studio" | "admin"
}
```

### POST /api/auth/reset-password

Complete password reset with token.

**Request Body:**
```json
{
  "token": "reset-token",
  "password": "NewPassword123!",
  "confirmPassword": "NewPassword123!"
}
```

## Studio Endpoints

### GET /api/studio/dashboard

Get studio dashboard data including analytics and recent activity.

**Query Parameters:**
- `period` - Time period for analytics (7d, 30d, 90d, 1y)

### GET /api/studio/photos

Get paginated list of studio photos.

**Query Parameters:**
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20, max: 100)
- `eventId` - Filter by event ID
- `isMatched` - Filter by match status (true/false)
- `isProcessed` - Filter by processing status (true/false)
- `search` - Search in filename or original name
- `sortBy` - Sort field (uploadedAt, filename, size)
- `sortOrder` - Sort order (asc, desc)

### PUT /api/studio/photos

Update photo details.

**Request Body:**
```json
{
  "photoId": "photo-id",
  "originalName": "new-name.jpg",
  "eventId": "event-id",
  "clientId": "client-id",
  "isMatched": true,
  "isProcessed": false
}
```

### DELETE /api/studio/photos

Delete multiple photos.

**Query Parameters:**
- `photoIds` - Comma-separated list of photo IDs

### POST /api/studio/upload

Upload photos to studio.

**Request Body:** FormData with files and optional eventId

### GET /api/studio/clients

Get paginated list of studio clients.

**Query Parameters:**
- `page` - Page number
- `limit` - Items per page
- `search` - Search in name or email
- `status` - Filter by status

### POST /api/studio/clients

Create new client.

**Request Body:**
```json
{
  "name": "Client Name",
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "+**********"
}
```

### GET /api/studio/events

Get studio events.

**Query Parameters:**
- `page` - Page number
- `limit` - Items per page
- `status` - Filter by status
- `search` - Search in name or location

### POST /api/studio/events

Create new event.

**Request Body:**
```json
{
  "name": "Event Name",
  "description": "Event description",
  "date": "2023-12-31T23:59:59Z",
  "location": "Event location",
  "eventType": "wedding"
}
```

## Client Endpoints

### GET /api/client/access

Verify client access code and get photos.

**Query Parameters:**
- `accessCode` - Client access code
- `password` - Client password

### GET /api/client/photos

Get client's photos.

**Query Parameters:**
- `page` - Page number
- `limit` - Items per page
- `eventId` - Filter by event

### POST /api/client/favorites

Add/remove photo from favorites.

**Request Body:**
```json
{
  "photoId": "photo-id",
  "action": "add" | "remove"
}
```

### GET /api/client/download

Download single photo.

**Query Parameters:**
- `photoId` - Photo ID
- `quality` - Download quality (low, medium, high, original)

### POST /api/client/download

Bulk download photos.

**Request Body:**
```json
{
  "photoIds": ["photo-1", "photo-2"],
  "quality": "high",
  "format": "zip"
}
```

## Admin Endpoints

### GET /api/admin/analytics

Get system-wide analytics.

**Query Parameters:**
- `period` - Time period
- `metric` - Specific metric to retrieve

### GET /api/admin/users

Get all users (studios and admins).

**Query Parameters:**
- `page` - Page number
- `limit` - Items per page
- `userType` - Filter by user type
- `status` - Filter by status
- `search` - Search query

### POST /api/admin/users

Create new admin user.

**Request Body:**
```json
{
  "name": "Admin Name",
  "email": "<EMAIL>",
  "password": "Password123!",
  "role": "admin"
}
```

### PUT /api/admin/users

Update user status or details.

**Request Body:**
```json
{
  "userId": "user-id",
  "action": "approve" | "suspend" | "activate" | "update",
  "data": {
    "reason": "Suspension reason"
  }
}
```

### GET /api/admin/system

Get system information and health status.

### POST /api/admin/system

Perform system maintenance actions.

**Request Body:**
```json
{
  "action": "cleanup-storage" | "optimize-database" | "clear-cache" | "backup-database",
  "parameters": {}
}
```

## Gallery Endpoints

### GET /api/gallery/[id]

Access public gallery (no authentication required).

**Query Parameters:**
- `password` - Gallery password (if protected)

### GET /api/studio/gallery

Get gallery overview and statistics.

### POST /api/studio/gallery

Process photos for face recognition.

**Request Body:**
```json
{
  "action": "process-faces" | "process-event" | "match-clients",
  "photoIds": ["photo-1", "photo-2"],
  "eventId": "event-id"
}
```

## Face Recognition Endpoints

### GET /api/studio/face-recognition

Get face recognition statistics.

### POST /api/studio/face-recognition

Process face recognition.

**Request Body:**
```json
{
  "action": "process-photos" | "process-event" | "match-client" | "rematch-all",
  "photoIds": ["photo-1", "photo-2"],
  "eventId": "event-id",
  "clientId": "client-id"
}
```

### PUT /api/studio/face-recognition

Update face recognition settings.

**Request Body:**
```json
{
  "threshold": 0.8,
  "autoProcess": true,
  "batchSize": 10
}
```

## Rate Limiting

API endpoints are rate-limited to prevent abuse:

- Authentication endpoints: 5 requests per minute
- Upload endpoints: 10 requests per minute
- General endpoints: 100 requests per minute

## Webhooks

The system supports webhooks for real-time notifications:

### Photo Processing Complete
```json
{
  "event": "photo.processed",
  "data": {
    "photoId": "photo-id",
    "studioId": "studio-id",
    "processedAt": "2023-12-31T23:59:59Z"
  }
}
```

### Client Access
```json
{
  "event": "client.access",
  "data": {
    "clientId": "client-id",
    "studioId": "studio-id",
    "accessedAt": "2023-12-31T23:59:59Z"
  }
}
```

## SDK Examples

### JavaScript/Node.js

```javascript
const API_BASE = 'https://your-domain.com/api';
const token = 'your-jwt-token';

// Login
const login = async (email, password, userType) => {
  const response = await fetch(`${API_BASE}/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ email, password, userType }),
  });
  return response.json();
};

// Get photos
const getPhotos = async (page = 1, limit = 20) => {
  const response = await fetch(`${API_BASE}/studio/photos?page=${page}&limit=${limit}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
  return response.json();
};
```

### Python

```python
import requests

API_BASE = 'https://your-domain.com/api'
token = 'your-jwt-token'

# Login
def login(email, password, user_type):
    response = requests.post(f'{API_BASE}/auth/login', json={
        'email': email,
        'password': password,
        'userType': user_type
    })
    return response.json()

# Get photos
def get_photos(page=1, limit=20):
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.get(f'{API_BASE}/studio/photos', 
                          params={'page': page, 'limit': limit},
                          headers=headers)
    return response.json()
```
