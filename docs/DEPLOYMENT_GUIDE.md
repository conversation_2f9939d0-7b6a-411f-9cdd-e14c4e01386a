# Photo Studio ERP - Deployment Guide

## Overview

This guide covers the deployment of the Photo Studio ERP system to production environments. The application is built with Next.js 15 and requires PostgreSQL database and file storage.

## Prerequisites

- Node.js 18+ 
- PostgreSQL 14+
- Redis (for caching and sessions)
- File storage (AWS S3, Google Cloud Storage, or local storage)
- SSL certificate for HTTPS
- Domain name

## Environment Variables

Create a `.env.production` file with the following variables:

```bash
# Database
DATABASE_URL="postgresql://username:password@host:port/database"
DIRECT_URL="postgresql://username:password@host:port/database"

# Authentication
JWT_SECRET="your-super-secret-jwt-key-min-32-chars"
NEXTAUTH_SECRET="your-nextauth-secret-key"
NEXTAUTH_URL="https://your-domain.com"

# File Storage (choose one)
# AWS S3
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="your-bucket-name"

# Google Cloud Storage
GOOGLE_CLOUD_PROJECT_ID="your-project-id"
GOOGLE_CLOUD_STORAGE_BUCKET="your-bucket-name"
GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account.json"

# Local Storage (development only)
UPLOAD_DIR="/var/www/uploads"

# Redis
REDIS_URL="redis://localhost:6379"

# Email (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Face Recognition API (optional)
FACE_API_KEY="your-face-api-key"
FACE_API_URL="https://api.face-recognition-service.com"

# Monitoring
SENTRY_DSN="your-sentry-dsn"
ANALYTICS_ID="your-analytics-id"

# Rate Limiting
RATE_LIMIT_WINDOW="60000"
RATE_LIMIT_MAX="100"

# File Upload Limits
MAX_FILE_SIZE="********"  # 10MB in bytes
MAX_FILES_PER_UPLOAD="50"
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif,image/webp"
```

## Database Setup

### 1. Create Database

```sql
CREATE DATABASE photo_studio_erp;
CREATE USER photo_studio_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE photo_studio_erp TO photo_studio_user;
```

### 2. Run Migrations

```bash
# Install dependencies
npm install

# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma migrate deploy

# Seed initial data (optional)
npx prisma db seed
```

### 3. Database Optimization

```sql
-- Create indexes for better performance
CREATE INDEX CONCURRENTLY idx_photos_studio_uploaded ON "Photo" ("studioId", "uploadedAt");
CREATE INDEX CONCURRENTLY idx_photos_event_matched ON "Photo" ("eventId", "isMatched");
CREATE INDEX CONCURRENTLY idx_clients_studio_status ON "Client" ("studioId", "status");
CREATE INDEX CONCURRENTLY idx_events_studio_date ON "Event" ("studioId", "date");
CREATE INDEX CONCURRENTLY idx_activity_logs_user_action ON "ActivityLog" ("userId", "action");

-- Set up database maintenance
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;
```

## Docker Deployment

### 1. Dockerfile

```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci --only=production

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build application
RUN npm run build

# Production image
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### 2. Docker Compose

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    env_file:
      - .env.production
    depends_on:
      - postgres
      - redis
    volumes:
      - uploads:/app/uploads

  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: photo_studio_erp
      POSTGRES_USER: photo_studio_user
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - uploads:/var/www/uploads
    depends_on:
      - app

volumes:
  postgres_data:
  redis_data:
  uploads:
```

## Nginx Configuration

```nginx
upstream nextjs_upstream {
    server app:3000;
}

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;

    client_max_body_size 100M;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;

    # Static files
    location /_next/static {
        proxy_cache STATIC;
        proxy_pass http://nextjs_upstream;
        add_header Cache-Control "public, max-age=31536000, immutable";
    }

    # Image optimization
    location /_next/image {
        proxy_cache STATIC;
        proxy_pass http://nextjs_upstream;
        add_header Cache-Control "public, max-age=31536000";
    }

    # API routes
    location /api {
        proxy_pass http://nextjs_upstream;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # File uploads
    location /uploads {
        alias /var/www/uploads;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Main application
    location / {
        proxy_pass http://nextjs_upstream;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}

# Cache configuration
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=STATIC:10m inactive=7d use_temp_path=off;
```

## Cloud Deployment Options

### 1. Vercel (Recommended for Next.js)

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod

# Configure environment variables in Vercel dashboard
```

### 2. AWS ECS/Fargate

```yaml
# task-definition.json
{
  "family": "photo-studio-erp",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "photo-studio-erp",
      "image": "your-account.dkr.ecr.region.amazonaws.com/photo-studio-erp:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "DATABASE_URL",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:database-url"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/photo-studio-erp",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

### 3. Google Cloud Run

```yaml
# cloudbuild.yaml
steps:
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/photo-studio-erp', '.']
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/photo-studio-erp']
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'photo-studio-erp'
      - '--image'
      - 'gcr.io/$PROJECT_ID/photo-studio-erp'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
```

## Monitoring and Logging

### 1. Application Monitoring

```javascript
// lib/monitoring.js
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
});

export const captureException = Sentry.captureException;
export const captureMessage = Sentry.captureMessage;
```

### 2. Health Check Endpoint

```javascript
// pages/api/health.js
export default async function handler(req, res) {
  try {
    // Check database connection
    await prisma.studio.findFirst();

    // Check Redis connection
    await redis.ping();

    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      error: error.message,
    });
  }
}
```

## Security Checklist

- [ ] HTTPS enabled with valid SSL certificate
- [ ] Environment variables secured
- [ ] Database credentials rotated
- [ ] CORS properly configured
- [ ] Rate limiting implemented
- [ ] Input validation on all endpoints
- [ ] File upload restrictions in place
- [ ] Security headers configured
- [ ] Regular security updates scheduled
- [ ] Backup strategy implemented

## Performance Optimization

### 1. Database Optimization

```sql
-- Regular maintenance
VACUUM ANALYZE;
REINDEX DATABASE photo_studio_erp;

-- Monitor slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;
```

### 2. Caching Strategy

```javascript
// lib/cache.js
import Redis from 'ioredis';

const redis = new Redis(process.env.REDIS_URL);

export const cache = {
  get: (key) => redis.get(key),
  set: (key, value, ttl = 3600) => redis.setex(key, ttl, JSON.stringify(value)),
  del: (key) => redis.del(key),
};
```

## Backup and Recovery

### 1. Database Backup

```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump $DATABASE_URL > backup_$DATE.sql
aws s3 cp backup_$DATE.sql s3://your-backup-bucket/
```

### 2. File Storage Backup

```bash
#!/bin/bash
# sync uploads to backup storage
aws s3 sync /var/www/uploads s3://your-backup-bucket/uploads/
```

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Check DATABASE_URL format
   - Verify network connectivity
   - Check database server status

2. **File Upload Issues**
   - Verify storage permissions
   - Check file size limits
   - Validate file types

3. **Performance Issues**
   - Monitor database queries
   - Check memory usage
   - Review cache hit rates

### Logs Location

- Application logs: `/var/log/photo-studio-erp/`
- Nginx logs: `/var/log/nginx/`
- Database logs: `/var/log/postgresql/`

## Support

For deployment support, contact the development team or refer to the troubleshooting guide.
