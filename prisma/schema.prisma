// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Admin {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  role      String   @default("admin")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("admins")
}

model Studio {
  id              String   @id @default(cuid())
  email           String   @unique
  password        String
  name            String
  businessName    String?
  phone           String?
  address         String?
  logo            String?
  isActive        Boolean  @default(true)
  isApproved      Boolean  @default(false)
  subscriptionId  String?
  storageUsed     BigInt   @default(0)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  subscription    Subscription? @relation(fields: [subscriptionId], references: [id])
  clients         Client[]
  photos          Photo[]
  events          Event[]
  billingHistory  BillingHistory[]
  notifications   Notification[]

  @@map("studios")
}

model Client {
  id          String   @id @default(cuid())
  email       String
  password    String
  name        String
  phone       String?
  studioId    String
  qrCode      String   @unique
  accessLink  String   @unique
  isActive    Boolean  @default(true)
  lastAccess  DateTime?
  // Security fields
  faceRetryCount    Int      @default(0)
  isBlocked         Boolean  @default(false)
  blockedUntil      DateTime?
  maxDevices        Int      @default(1)
  requireFaceVerify Boolean  @default(true)
  geoRestrictions   Json?    // Array of allowed countries/regions
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  studio          Studio @relation(fields: [studioId], references: [id], onDelete: Cascade)
  faceDescriptors FaceDescriptor[]
  photos          Photo[]
  favorites       Favorite[]
  comments        Comment[]
  accessLogs      AccessLog[]
  sessions        ClientSession[]
  securityAttempts SecurityAttempt[]
  deviceAccess    DeviceAccess[]

  @@unique([email, studioId])
  @@map("clients")
}

model SubscriptionPlan {
  id              String   @id @default(cuid())
  name            String   @unique
  price           Decimal
  billingCycle    String   // monthly, yearly
  storageLimit    BigInt   // in bytes
  downloadQuality String   // low, medium, high, original
  customBranding  Boolean  @default(false)
  watermark       Boolean  @default(false)
  videoSupport    Boolean  @default(false)
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  subscriptions Subscription[]

  @@map("subscription_plans")
}

model Subscription {
  id              String   @id @default(cuid())
  studioId        String   @unique
  planId          String
  status          String   // active, cancelled, expired
  startDate       DateTime
  endDate         DateTime
  autoRenew       Boolean  @default(true)
  paymentMethod   String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  plan    SubscriptionPlan @relation(fields: [planId], references: [id])
  studios Studio[]

  @@map("subscriptions")
}

model Event {
  id          String   @id @default(cuid())
  name        String
  description String?
  date        DateTime
  location    String?
  studioId    String
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  studio Studio @relation(fields: [studioId], references: [id], onDelete: Cascade)
  photos Photo[]

  @@map("events")
}

model Photo {
  id              String   @id @default(cuid())
  filename        String
  originalName    String
  path            String
  size            BigInt
  mimeType        String
  studioId        String
  clientId        String?
  eventId         String?
  isMatched       Boolean  @default(false)
  isProcessed     Boolean  @default(false)
  uploadedAt      DateTime @default(now())
  processedAt     DateTime?

  // Relations
  studio          Studio @relation(fields: [studioId], references: [id], onDelete: Cascade)
  client          Client? @relation(fields: [clientId], references: [id])
  event           Event? @relation(fields: [eventId], references: [id])
  faceDescriptors FaceDescriptor[]
  favorites       Favorite[]
  comments        Comment[]

  @@map("photos")
}

model FaceDescriptor {
  id          String   @id @default(cuid())
  photoId     String
  clientId    String
  descriptor  Json     // Face descriptor array
  confidence  Float
  boundingBox Json     // Face bounding box coordinates
  createdAt   DateTime @default(now())

  // Relations
  photo  Photo  @relation(fields: [photoId], references: [id], onDelete: Cascade)
  client Client @relation(fields: [clientId], references: [id], onDelete: Cascade)

  @@map("face_descriptors")
}

model Favorite {
  id       String @id @default(cuid())
  photoId  String
  clientId String

  // Relations
  photo  Photo  @relation(fields: [photoId], references: [id], onDelete: Cascade)
  client Client @relation(fields: [clientId], references: [id], onDelete: Cascade)

  @@unique([photoId, clientId])
  @@map("favorites")
}

model Comment {
  id        String   @id @default(cuid())
  photoId   String
  clientId  String
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  photo  Photo  @relation(fields: [photoId], references: [id], onDelete: Cascade)
  client Client @relation(fields: [clientId], references: [id], onDelete: Cascade)

  @@map("comments")
}

model BillingHistory {
  id          String   @id @default(cuid())
  studioId    String
  amount      Decimal
  currency    String   @default("INR")
  status      String   // pending, completed, failed
  paymentId   String?
  description String?
  createdAt   DateTime @default(now())

  // Relations
  studio Studio @relation(fields: [studioId], references: [id], onDelete: Cascade)

  @@map("billing_history")
}

model Notification {
  id        String   @id @default(cuid())
  studioId  String?
  clientId  String?
  type      String   // email, whatsapp, system
  title     String
  message   String
  isRead    Boolean  @default(false)
  sentAt    DateTime?
  createdAt DateTime @default(now())

  // Relations
  studio Studio? @relation(fields: [studioId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model AccessLog {
  id               String   @id @default(cuid())
  clientId         String
  ipAddress        String
  userAgent        String?
  browserFingerprint String?
  deviceId         String?
  location         String?  // Geo-location data
  action           String   // login, view_photo, download, etc.
  success          Boolean  @default(true)
  failureReason    String?  // For failed attempts
  sessionId        String?  // Track sessions
  createdAt        DateTime @default(now())

  // Relations
  client Client @relation(fields: [clientId], references: [id], onDelete: Cascade)

  @@map("access_logs")
}

model SystemSettings {
  id    String @id @default(cuid())
  key   String @unique
  value String
  type  String // string, number, boolean, json

  @@map("system_settings")
}

// Security & Session Management
model ClientSession {
  id               String   @id @default(cuid())
  clientId         String
  sessionToken     String   @unique
  deviceId         String?
  browserFingerprint String?
  ipAddress        String
  userAgent        String?
  location         String?
  isActive         Boolean  @default(true)
  lastActivity     DateTime @default(now())
  expiresAt        DateTime
  createdAt        DateTime @default(now())

  // Relations
  client Client @relation(fields: [clientId], references: [id], onDelete: Cascade)

  @@map("client_sessions")
}

model SecurityAttempt {
  id               String   @id @default(cuid())
  clientId         String?
  attemptType      String   // face_scan, login, access
  ipAddress        String
  userAgent        String?
  browserFingerprint String?
  success          Boolean  @default(false)
  failureReason    String?
  attemptCount     Int      @default(1)
  blockedUntil     DateTime?
  createdAt        DateTime @default(now())

  // Relations
  client Client? @relation(fields: [clientId], references: [id], onDelete: Cascade)

  @@map("security_attempts")
}

model DeviceAccess {
  id               String   @id @default(cuid())
  clientId         String
  deviceId         String   @unique
  deviceName       String?
  browserFingerprint String
  ipAddress        String
  userAgent        String?
  location         String?
  isApproved       Boolean  @default(false)
  isBlocked        Boolean  @default(false)
  firstSeen        DateTime @default(now())
  lastSeen         DateTime @default(now())
  accessCount      Int      @default(1)

  // Relations
  client Client @relation(fields: [clientId], references: [id], onDelete: Cascade)

  @@unique([clientId, deviceId])
  @@map("device_access")
}
