import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Create default admin
  const adminPassword = await bcrypt.hash('Admin@123', 12);
  const admin = await prisma.admin.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: adminPassword,
      name: 'System Administrator',
      role: 'admin',
    },
  });
  console.log('✅ Created admin user:', admin.email);

  // Create subscription plans
  const plans = [
    {
      name: 'Free',
      price: 0,
      billingCycle: 'monthly',
      storageLimit: BigInt(10 * 1024 * 1024 * 1024), // 10GB
      downloadQuality: 'medium',
      customBranding: false,
      watermark: true,
      videoSupport: false,
    },
    {
      name: 'Basic',
      price: 999,
      billingCycle: 'monthly',
      storageLimit: BigInt(50 * 1024 * 1024 * 1024), // 50GB
      downloadQuality: 'high',
      customBranding: true,
      watermark: false,
      videoSupport: false,
    },
    {
      name: 'Pro',
      price: 2499,
      billingCycle: 'monthly',
      storageLimit: BigInt(200 * 1024 * 1024 * 1024), // 200GB
      downloadQuality: 'original',
      customBranding: true,
      watermark: false,
      videoSupport: true,
    },
    {
      name: 'Enterprise',
      price: 4999,
      billingCycle: 'monthly',
      storageLimit: BigInt(1024 * 1024 * 1024 * 1024), // 1TB
      downloadQuality: 'original',
      customBranding: true,
      watermark: false,
      videoSupport: true,
    },
  ];

  for (const planData of plans) {
    const plan = await prisma.subscriptionPlan.upsert({
      where: { name: planData.name },
      update: {},
      create: planData,
    });
    console.log('✅ Created subscription plan:', plan.name);
  }

  // Create sample studio
  const studioPassword = await bcrypt.hash('Studio@123', 12);
  const studio = await prisma.studio.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: studioPassword,
      name: 'John Doe',
      businessName: 'Demo Photography Studio',
      phone: '+1234567890',
      isActive: true,
      isApproved: true,
    },
  });
  console.log('✅ Created demo studio:', studio.email);

  // Create sample clients for the studio
  const clients = [
    {
      name: 'Alice Johnson',
      email: '<EMAIL>',
      password: await bcrypt.hash('Client@123', 12),
      phone: '+1234567891',
    },
    {
      name: 'Bob Smith',
      email: '<EMAIL>',
      password: await bcrypt.hash('Client@123', 12),
      phone: '+1234567892',
    },
    {
      name: 'Carol Davis',
      email: '<EMAIL>',
      password: await bcrypt.hash('Client@123', 12),
      phone: '+1234567893',
    },
  ];

  for (const clientData of clients) {
    const qrCode = Math.random().toString(36).substring(2, 15);
    const accessLink = Math.random().toString(36).substring(2, 15);
    
    const client = await prisma.client.upsert({
      where: { 
        email_studioId: {
          email: clientData.email,
          studioId: studio.id,
        }
      },
      update: {},
      create: {
        ...clientData,
        studioId: studio.id,
        qrCode,
        accessLink,
      },
    });
    console.log('✅ Created client:', client.email);
  }

  // Create sample event
  const event = await prisma.event.create({
    data: {
      name: 'Wedding Photography Session',
      description: 'Beautiful wedding ceremony and reception photos',
      date: new Date('2024-01-15'),
      location: 'Grand Hotel Ballroom',
      studioId: studio.id,
    },
  });
  console.log('✅ Created sample event:', event.name);

  // Create system settings
  const settings = [
    { key: 'app_name', value: 'Studio ERP', type: 'string' },
    { key: 'max_file_size', value: '10485760', type: 'number' }, // 10MB
    { key: 'face_recognition_threshold', value: '0.4', type: 'number' },
    { key: 'email_notifications', value: 'true', type: 'boolean' },
    { key: 'whatsapp_notifications', value: 'false', type: 'boolean' },
  ];

  for (const setting of settings) {
    await prisma.systemSettings.upsert({
      where: { key: setting.key },
      update: {},
      create: setting,
    });
    console.log('✅ Created system setting:', setting.key);
  }

  console.log('🎉 Database seeded successfully!');
  console.log('\n📋 Default Accounts:');
  console.log('Admin: <EMAIL> / Admin@123');
  console.log('Studio: <EMAIL> / Studio@123');
  console.log('Clients: <EMAIL>, <EMAIL>, <EMAIL> / Client@123');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
